# Konveyor Environment Configuration
#
# 📚 Complete Documentation:
# - Configuration Guide: docs/reference/configuration.md
# - Environment Variables: docs/reference/environment-variables.md
# - Django Settings: docs/reference/settings.md
#
# 🚀 Quick Start:
# 1. Copy this file: cp .env.example .env
# 2. Fill in your Azure service values
# 3. Update Django secret key
# 4. Configure Slack/Bot Framework if needed

# =============================================================================
# CORE AZURE SERVICES (REQUIRED)
# =============================================================================

# Azure OpenAI
AZURE_OPENAI_ENDPOINT="https://your-openai.openai.azure.com/"
AZURE_OPENAI_API_KEY="<your-openai-api-key>"
AZURE_OPENAI_CHAT_DEPLOYMENT="gpt-4o"
AZURE_OPENAI_EMBEDDING_DEPLOYMENT="text-embedding-ada-002"
AZURE_OPENAI_API_VERSION="2024-05-13"

# Azure Cognitive Search
AZURE_SEARCH_ENDPOINT="https://your-search.search.windows.net"
AZURE_SEARCH_API_KEY="<your-search-api-key>"
AZURE_SEARCH_INDEX_NAME="konveyor-documents"

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=<account>;AccountKey=<key>;EndpointSuffix=core.windows.net"
AZURE_STORAGE_CONTAINER_NAME="documents"

# Azure Document Intelligence
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT="https://your-doc-intel.cognitiveservices.azure.com/"
AZURE_DOCUMENT_INTELLIGENCE_API_KEY="<your-document-intelligence-api-key>"

# Azure Core
AZURE_TENANT_ID="<your-tenant-id>"
AZURE_SUBSCRIPTION_ID="<your-subscription-id>"
AZURE_LOCATION="eastus"

# =============================================================================
# DJANGO FRAMEWORK (REQUIRED)
# =============================================================================

DJANGO_SECRET_KEY="<your-secret-key-generate-new-one>"
DJANGO_SETTINGS_MODULE="konveyor.settings.development"
DJANGO_ENVIRONMENT="development"
ALLOWED_HOSTS="localhost,127.0.0.1"

# =============================================================================
# DATABASE CONFIGURATION (OPTIONAL FOR DEVELOPMENT)
# =============================================================================
# Note: Development uses SQLite by default, PostgreSQL for production

DB_NAME="konveyor"
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_HOST="localhost"
DB_PORT="5432"

# =============================================================================
# BOT FRAMEWORK & SLACK INTEGRATION (OPTIONAL)
# =============================================================================

# Microsoft Bot Framework
MICROSOFT_APP_ID="<your-bot-framework-app-id>"
MICROSOFT_APP_PASSWORD="<your-bot-framework-app-password>"

# Slack Integration
SLACK_BOT_TOKEN="<your-slack-bot-token>"  # xoxb-...
SLACK_SIGNING_SECRET="<your-slack-signing-secret>"
SLACK_APP_TOKEN="<your-slack-app-token>"  # xapp-... (for Socket Mode)
SLACK_CLIENT_ID="<your-slack-client-id>"
SLACK_CLIENT_SECRET="<your-slack-client-secret>"

# =============================================================================
# OPTIONAL AZURE SERVICES
# =============================================================================

# Azure Key Vault (for secret management)
AZURE_KEY_VAULT_URL="https://your-keyvault.vault.azure.net/"

# Service Principal (for local development)
AZURE_CLIENT_ID="<your-service-principal-client-id>"
AZURE_CLIENT_SECRET="<your-service-principal-secret>"

# Additional Azure Services (if needed)
AZURE_COSMOS_CONNECTION_STRING="<your-cosmos-connection-string>"
AZURE_REDIS_CONNECTION_STRING="<your-redis-connection-string>"
AZURE_BOT_ENDPOINT="https://your-bot.azurewebsites.net"

# =============================================================================
# DEVELOPMENT SETTINGS (OPTIONAL)
# =============================================================================

# Bot Behavior
BOT_NAME="Konveyor Bot"
MAX_RESPONSE_LENGTH="4000"
RESPONSE_STYLE="balanced"
FOCUS_AREA="general"

# Slack Channels
SLACK_DEFAULT_CHANNEL="#general"
SLACK_ADMIN_CHANNEL="#konveyor-admin"

# Development Tools
INTERNAL_IPS="127.0.0.1"

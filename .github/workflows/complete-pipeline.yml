name: "Complete CI/CD Pipeline"
permissions:
  id-token: write  # Required for Azure login
  contents: read   # Required for checkout
  packages: write  # Required for pushing to GHCR
  actions: write   # Required for workflow calls and creating GitHub variables
  pages: write     # Required for code quality and integration test
on:
  pull_request:
    branches: [ main, dev ]
  push:
    branches: [ main, dev ]
    tags:
      - 'v*.*.*'  # Trigger on any tag that matches the pattern v*.*.* (e.g., v1.0.0, v1.2.3)
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to Azure after tests'
        required: false
        default: false
        type: boolean
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'mock'
        type: choice
        options:
          - mock
          - real
          - both
      environment:
        description: 'Environment to run tests in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - test
          - prod
      test_category:
        description: 'Category of tests to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - real
          - search
          - document
          - slack
      fast_track:
        description: 'Run only critical tests for quick deployment'
        required: false
        default: 'false'
        type: string
      ref:
        description: 'The branch, tag or SHA to deploy (e.g., main, v1.0.0)'
        required: false
        type: string

jobs:
  # Step 1: Build and push Docker image
  build-image:
    name: "Build and Push Docker Image"
    uses: ./.github/workflows/build-image.yml
    with:
      ref: ${{ github.event.inputs.ref || github.ref }}
      environment: ${{ github.event.inputs.environment || 'test' }}
    secrets:
      GHCR_PAT: ${{ secrets.GHCR_PAT }}

  # Step 2: Check branch naming conventions
  branch-naming:
    name: "Validate Branch Naming"
    needs: [build-image]
    if: github.event_name == 'pull_request'
    uses: ./.github/workflows/commit-conventions.yml

  # Step 3: Run code quality checks
  code-quality:
    name: "Code Quality Checks"
    needs: [branch-naming, build-image]
    if: always() && (needs.branch-naming.result == 'success' || needs.branch-naming.result == 'skipped')
    uses: ./.github/workflows/code-quality.yml
    with:
      test_type: ${{ github.event.inputs.test_type || 'mock' }}
      environment: ${{ github.event.inputs.environment || 'test' }}
      image_tag: ${{ needs.build-image.outputs.image_tag }}
    secrets:
      ALLOWED_HOSTS: ${{ secrets.ALLOWED_HOSTS }}
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      TF_VAR_MICROSOFT_APP_ID: ${{ secrets.TF_VAR_MICROSOFT_APP_ID }}
      TF_VAR_MICROSOFT_APP_PASSWORD: ${{ secrets.TF_VAR_MICROSOFT_APP_PASSWORD }}
      TF_VAR_SLACK_BOT_TOKEN: ${{ secrets.TF_VAR_SLACK_BOT_TOKEN }}
      TF_VAR_SLACK_APP_TOKEN: ${{ secrets.TF_VAR_SLACK_APP_TOKEN }}
      TF_VAR_SLACK_SIGNING_SECRET: ${{ secrets.TF_VAR_SLACK_SIGNING_SECRET }}
      TF_VAR_SLACK_CLIENT_ID: ${{ secrets.TF_VAR_SLACK_CLIENT_ID }}
      TF_VAR_SLACK_CLIENT_SECRET: ${{ secrets.TF_VAR_SLACK_CLIENT_SECRET }}
      TF_VAR_SLACK_TEST_CHANNEL_ID: ${{ secrets.TF_VAR_SLACK_TEST_CHANNEL_ID }}
      GHCR_PAT: ${{ secrets.GHCR_PAT }}
      DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}

  # Step 4: Run integration tests
  integration-tests:
    name: "Integration Tests"
    needs: [code-quality, build-image]
    if: always() && (needs.code-quality.result == 'success' || needs.code-quality.result == 'skipped')
    uses: ./.github/workflows/integration-tests.yml
    with:
      test_type: ${{ github.event.inputs.test_type || 'mock' }}
      environment: ${{ github.event.inputs.environment || 'dev' }}
      test_category: ${{ github.event.inputs.test_category || 'all' }}
      fast_track: ${{ github.event.inputs.fast_track || 'false' }}
      image_tag: ${{ needs.build-image.outputs.image_tag }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}

  # Step 5: Deploy infrastructure (if needed)
  deploy-infra:
    name: "Deploy Infrastructure"
    needs: [integration-tests, build-image]
    if: |
      always() &&
      needs.integration-tests.outputs.success == 'true' &&
      (github.event.inputs.deploy == 'true' || github.ref_type == 'tag' || contains(github.ref, 'infra'))
    uses: ./.github/workflows/infra-deploy.yml
    with:
      ref: ${{ github.event.inputs.ref || github.ref }}
      environment: ${{ github.event.inputs.environment || 'test' }}
      image_tag: ${{ needs.build-image.outputs.image_tag }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      GHCR_PAT: ${{ secrets.GHCR_PAT }}
      DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}

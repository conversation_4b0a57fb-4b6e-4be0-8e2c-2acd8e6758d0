name: "Deploy Infrastructure and Save Variables"

permissions:
  contents: read   # Required for checkout
  id-token: write  # Required for GitHub OIDC token authentication
  actions: write   # Required for creating GitHub variables
  packages: read   # Required for pulling from GHCR

on:
  workflow_dispatch:
    inputs:
      ref:
        description: 'The branch, tag or SHA to deploy (e.g., main, v1.0.0)'
        required: true
        type: string
      environment:
        description: 'Environment to deploy to (test, prod)'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - prod
  workflow_call:
    inputs:
      ref:
        description: 'The branch, tag or SHA to deploy (e.g., main, v1.0.0)'
        required: false
        type: string
      environment:
        description: 'Environment to deploy to (test, prod)'
        required: false
        default: 'test'
        type: string
      image_tag:
        description: 'Docker image tag to use'
        required: false
        default: 'latest'
        type: string
    secrets:
      AZURE_CREDENTIALS:
        description: 'Azure credentials for authentication'
        required: false
      GHCR_PAT:
        description: 'GitHub Personal Access Token for GHCR and creating secrets'
        required: true
      DJANGO_SECRET_KEY:
        description: 'Django secret key'
        required: false

env:
  TF_VERSION: '1.5.7'
  AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
  AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}

jobs:
  resource-cleanup:
    name: "Clean up soft-deleted resources"
    uses: ./.github/workflows/resource-cleanup.yml
    with:
      environment: ${{ inputs.environment || github.event.inputs.environment || 'test' }}
      resource_group: ${{ format('konveyor-{0}-rg', inputs.environment || github.event.inputs.environment || 'test') }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}

  terraform:
    name: Deploy Infrastructure (${{ inputs.environment || github.event.inputs.environment || 'test' }})
    needs: resource-cleanup
    runs-on: ubuntu-latest
    timeout-minutes: 25
    environment: ${{ inputs.environment || github.event.inputs.environment || 'test' }}

    outputs:
      cognitive_search_endpoint: ${{ steps.terraform-outputs.outputs.cognitive_search_endpoint }}
      openai_endpoint: ${{ steps.terraform-outputs.outputs.openai_endpoint }}
      openai_embeddings_deployment_name: ${{ steps.terraform-outputs.outputs.openai_embeddings_deployment_name }}
      document_intelligence_endpoint: ${{ steps.terraform-outputs.outputs.document_intelligence_endpoint }}
      app_service_name: ${{ steps.terraform-outputs.outputs.app_service_name }}
      app_service_hostname: ${{ steps.terraform-outputs.outputs.app_service_default_site_hostname }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref || github.event.inputs.ref || github.ref }}

      - name: List files in workspace root
        run: ls -R $GITHUB_WORKSPACE
        shell: bash

      - name: List files in intended working directory
        run: ls -R Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        shell: bash
        continue-on-error: true # Allow this to fail if the path is indeed wrong

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Azure Login
        uses: azure/login@v2.1.0
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      # Note: Resource cleanup is handled by the resource-cleanup job

      - name: Terraform Init
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: |
          # Initialize Terraform with remote backend for state persistence
          terraform init \
            -backend=true \
            -backend-config="resource_group_name=terraform-state-rg" \
            -backend-config="storage_account_name=konveyortfstate" \
            -backend-config="container_name=tfstate" \
            -input=false

      - name: Select Terraform Workspace
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: terraform workspace select integration-test || terraform workspace new integration-test

      - name: Create Terraform Variables
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: |
          # Create terraform.tfvars file securely using GitHub Secrets
          # This approach prevents secrets from being exposed in logs
          cat > terraform.tfvars << 'EOF'
          allowed_hosts="${{ secrets.ALLOWED_HOSTS || 'localhost,127.0.0.1, *' }}"
          microsoft_app_id="${{ secrets.TF_VAR_MICROSOFT_APP_ID || 'c8218a52-681c-4df2-b558-5fa8e5067b43' }}"
          microsoft_app_password="${{ secrets.TF_VAR_MICROSOFT_APP_PASSWORD }}"
          slack_bot_token="${{ secrets.TF_VAR_SLACK_BOT_TOKEN }}"
          slack_app_token="${{ secrets.TF_VAR_SLACK_APP_TOKEN }}"
          slack_signing_secret="${{ secrets.TF_VAR_SLACK_SIGNING_SECRET }}"
          slack_client_id="${{ secrets.TF_VAR_SLACK_CLIENT_ID }}"
          slack_client_secret="${{ secrets.TF_VAR_SLACK_CLIENT_SECRET }}"
          slack_test_channel_id="${{ secrets.TF_VAR_SLACK_TEST_CHANNEL_ID || 'D08KQ0WPGR2' }}"
          GHCR_PAT="${{ secrets.GHCR_PAT }}"
          DJANGO_SECRET_KEY="${{ secrets.DJANGO_SECRET_KEY || 'django-insecure-test-key-for-ci' }}"
          github_repository="${{ github.repository }}"
          docker_registry_username="${{ github.actor }}"
          docker_image_tag="${{ inputs.image_tag || 'latest' }}"
          EOF

          # Debug: Confirm the file was created (without showing contents)
          echo "terraform.tfvars file created successfully"

      - name: Terraform Validate
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: terraform validate

      - name: Terraform Plan
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: terraform plan -var-file=terraform.tfvars -out=tfplan

      - name: Terraform Apply
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: timeout 15m terraform apply -auto-approve tfplan

      - name: Extract Terraform Outputs
        id: terraform-outputs
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}
        run: |
          # Extract non-sensitive outputs
          echo "cognitive_search_endpoint=$(terraform output -raw cognitive_search_endpoint)" >> $GITHUB_OUTPUT
          echo "openai_endpoint=$(terraform output -raw openai_endpoint)" >> $GITHUB_OUTPUT
          echo "openai_embeddings_deployment_name=$(terraform output -raw openai_embeddings_deployment_name)" >> $GITHUB_OUTPUT
          echo "document_intelligence_endpoint=$(terraform output -raw document_intelligence_endpoint)" >> $GITHUB_OUTPUT
          echo "app_service_name=$(terraform output -raw app_service_name)" >> $GITHUB_OUTPUT
          echo "app_service_default_site_hostname=$(terraform output -raw app_service_default_site_hostname)" >> $GITHUB_OUTPUT

          # Extract sensitive outputs to GitHub environment variables
          echo "AZURE_SEARCH_API_KEY=$(terraform output -raw cognitive_search_primary_key)" >> $GITHUB_ENV
          echo "AZURE_OPENAI_API_KEY=$(terraform output -raw openai_primary_key)" >> $GITHUB_ENV
          echo "AZURE_DOCUMENT_INTELLIGENCE_API_KEY=$(terraform output -raw document_intelligence_primary_key)" >> $GITHUB_ENV
          echo "AZURE_STORAGE_CONNECTION_STRING=$(terraform output -raw storage_connection_string)" >> $GITHUB_ENV

      - name: Save Sensitive Outputs to GitHub Secrets
        uses: gliech/create-github-secret-action@v1
        with:
          name: AZURE_SEARCH_API_KEY_${{ inputs.environment || github.event.inputs.environment || 'TEST' }}
          value: ${{ env.AZURE_SEARCH_API_KEY }}
          pa_token: ${{ secrets.GHCR_PAT }}

      - name: Save OpenAI API Key
        uses: gliech/create-github-secret-action@v1
        with:
          name: AZURE_OPENAI_API_KEY_${{ inputs.environment || github.event.inputs.environment || 'TEST' }}
          value: ${{ env.AZURE_OPENAI_API_KEY }}
          pa_token: ${{ secrets.GHCR_PAT }}

      - name: Save Document Intelligence API Key
        uses: gliech/create-github-secret-action@v1
        with:
          name: AZURE_DOCUMENT_INTELLIGENCE_API_KEY_${{ inputs.environment || github.event.inputs.environment || 'TEST' }}
          value: ${{ env.AZURE_DOCUMENT_INTELLIGENCE_API_KEY }}
          pa_token: ${{ secrets.GH_PAT }}

      - name: Save Storage Connection String
        uses: gliech/create-github-secret-action@v1
        with:
          name: AZURE_STORAGE_CONNECTION_STRING_${{ inputs.environment || github.event.inputs.environment || 'TEST' }}
          value: ${{ env.AZURE_STORAGE_CONNECTION_STRING }}
          pa_token: ${{ secrets.GH_PAT }}

      - name: Save Non-Sensitive Outputs to GitHub Variables
        uses: actions/github-script@v6
        with:
          script: |
            const environment = '${{ inputs.environment || github.event.inputs.environment || 'test' }}'.toUpperCase();
            const variables = {
              [`AZURE_SEARCH_ENDPOINT_${environment}`]: '${{ steps.terraform-outputs.outputs.cognitive_search_endpoint }}',
              [`AZURE_OPENAI_ENDPOINT_${environment}`]: '${{ steps.terraform-outputs.outputs.openai_endpoint }}',
              [`AZURE_OPENAI_EMBEDDING_DEPLOYMENT_${environment}`]: '${{ steps.terraform-outputs.outputs.openai_embeddings_deployment_name }}',
              [`AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT_${environment}`]: '${{ steps.terraform-outputs.outputs.document_intelligence_endpoint }}',
              [`APP_SERVICE_NAME_${environment}`]: '${{ steps.terraform-outputs.outputs.app_service_name }}',
              [`APP_SERVICE_HOSTNAME_${environment}`]: '${{ steps.terraform-outputs.outputs.app_service_default_site_hostname }}'
            };

            for (const [name, value] of Object.entries(variables)) {
              await github.rest.actions.createOrUpdateRepoVariable({
                owner: context.repo.owner,
                repo: context.repo.repo,
                name,
                value
              });
            }

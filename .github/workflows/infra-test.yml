name: "Deploy Infrastructure"

permissions:
  contents: read # Default required for checkout

on:
  push:
    tags:
      - '*infra*'
  workflow_dispatch:
    inputs:
      ref:
        description: 'The branch, tag or SHA to deploy (e.g., main, v1.0.0)'
        required: true
        type: string
      environment:
        description: 'Environment to deploy to (dev, test, prod)'
        required: false
        default: 'test'
        type: choice
        options:
          - dev
          - test
          - prod
  workflow_call:
    inputs:
      ref:
        description: 'The branch, tag or SHA to deploy (e.g., main, v1.0.0)'
        required: false
        type: string
      environment:
        description: 'Environment to deploy to (dev, test, prod)'
        required: false
        default: 'test'
        type: string

env:
  TF_VERSION: '1.5.7'
  AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
  AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}

jobs:
  terraform:
    name: Deploy Infrastructure (${{ inputs.environment || github.event.inputs.environment || 'test' }})
    runs-on: ubuntu-latest
    timeout-minutes: 25

    defaults:
      run:
        # Default to test environment, but allow override from inputs
        working-directory: Konveyor-infra/environments/${{ inputs.environment || github.event.inputs.environment || 'test' }}

    steps:
      - name: Checkout repository (push/tag)
        if: github.event_name == 'push' && github.ref_type == 'tag'
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Checkout repository (workflow_dispatch)
        if: github.event_name == 'workflow_dispatch'
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref }}

      - name: Checkout repository (workflow_call)
        if: github.event_name == 'workflow_call'
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref || github.ref }}

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Azure Login
        uses: azure/login@v2.1.0
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        run: terraform init

      - name: Create Terraform Variables
        run: |
          # Create terraform.tfvars file securely using GitHub Secrets
          # This approach prevents secrets from being exposed in logs
          cat > terraform.tfvars << 'EOF'
          allowed_hosts="${{ secrets.ALLOWED_HOSTS || 'localhost,127.0.0.1, *' }}"
          microsoft_app_id="${{ secrets.TF_VAR_MICROSOFT_APP_ID || 'c8218a52-681c-4df2-b558-5fa8e5067b43' }}"
          microsoft_app_password="${{ secrets.TF_VAR_MICROSOFT_APP_PASSWORD }}"
          slack_bot_token="${{ secrets.TF_VAR_SLACK_BOT_TOKEN }}"
          slack_app_token="${{ secrets.TF_VAR_SLACK_APP_TOKEN }}"
          slack_signing_secret="${{ secrets.TF_VAR_SLACK_SIGNING_SECRET }}"
          slack_client_id="${{ secrets.TF_VAR_SLACK_CLIENT_ID }}"
          slack_client_secret="${{ secrets.TF_VAR_SLACK_CLIENT_SECRET }}"
          slack_test_channel_id="${{ secrets.TF_VAR_SLACK_TEST_CHANNEL_ID || 'D08KQ0WPGR2' }}"
          GHCR_PAT="${{ secrets.GHCR_PAT }}"
          DJANGO_SECRET_KEY="${{ secrets.DJANGO_SECRET_KEY || 'django-insecure-test-key-for-ci' }}"
          github_repository="${{ github.repository }}"
          docker_registry_username="${{ github.actor }}"
          EOF

          # Debug: Confirm the file was created (without showing contents)
          echo "terraform.tfvars file created successfully"

      - name: Terraform Validate
        run: terraform validate

      - name: Terraform Plan
        run: terraform plan -var-file=terraform.tfvars -out=tfplan

      - name: Terraform Apply
        if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' || github.event_name == 'workflow_call'
        run: timeout 15m terraform apply -auto-approve tfplan

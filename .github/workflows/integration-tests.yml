name: "Integration Tests"

permissions:
  contents: read   # Required for checkout
  id-token: write  # Required for Azure login
  actions: read    # Required for workflow calls
  packages: read   # Required for pulling from GHCR
  pages: write     # Required for GitHub Pages deployment

on:
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'mock'
        type: choice
        options:
          - mock
          - real
          - both
      environment:
        description: 'Environment to run tests in'
        required: true
        default: 'test'
        type: choice
        options:
          - dev
          - test
          - prod
      test_category:
        description: 'Category of tests to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - real
          - search
          - document
          - slack
      fast_track:
        description: 'Run only critical tests for quick deployment'
        required: false
        default: 'false'
        type: string
      deploy_infrastructure:
        description: 'Whether to deploy infrastructure'
        required: false
        default: 'auto'
        type: choice
        options:
          - auto
          - always
          - never
  workflow_call:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: false
        default: 'real'
        type: string
      environment:
        description: 'Environment to run tests in'
        required: false
        default: 'test'
        type: string
      test_category:
        description: 'Category of tests to run'
        required: false
        default: 'all'
        type: string
      fast_track:
        description: 'Run only critical tests for quick deployment'
        required: false
        default: 'false'
        type: string
      deploy_infrastructure:
        description: 'Whether to deploy infrastructure'
        required: false
        default: 'auto'
        type: string
      image_tag:
        description: 'Docker image tag to use'
        required: false
        default: 'latest'
        type: string
    secrets:
      AZURE_CREDENTIALS:
        description: 'Azure credentials for authentication'
        required: false
    outputs:
      success:
        description: 'Whether all tests passed'
        value: ${{ jobs.integration-tests.outputs.tests_passed }}

jobs:
  resource-cleanup:
    name: "Clean up soft-deleted resources"
    uses: ./.github/workflows/resource-cleanup.yml
    with:
      environment: ${{ github.event.inputs.environment || 'test' }}
      resource_group: ${{ format('konveyor-{0}-rg', github.event.inputs.environment || 'test') }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}

  integration-tests:
    name: Run Integration Tests
    needs: resource-cleanup
    runs-on: ubuntu-latest
    timeout-minutes: 25
    outputs:
      tests_passed: ${{ steps.test-result.outputs.passed }}

    env:
      # Django settings
      DJANGO_SETTINGS_MODULE: konveyor.settings.${{ inputs.environment || github.event.inputs.environment || 'test' }}
      DJANGO_ENV: ${{ inputs.environment || github.event.inputs.environment || 'test' }}
      DJANGO_DEBUG: 'False'
      DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY || 'django-insecure-test-key-for-ci' }}

      # Database settings (for non-SQLite tests)
      DB_USER: ${{ secrets.DB_USER || 'postgres' }}
      DB_PASSWORD: ${{ secrets.DB_PASSWORD || 'postgres' }}
      DB_HOST: ${{ secrets.DB_HOST || 'localhost' }}
      DB_PORT: ${{ secrets.DB_PORT || '5432' }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Debug repository contents
        run: |
          echo "Current directory: $(pwd)"
          echo "Repository contents:"
          ls -la
          echo "Konveyor-infra directory:"
          ls -la Konveyor-infra || echo "Konveyor-infra directory not found"
          echo "Git status:"
          git status

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'

      # Set default parameters based on context
      - name: Set default parameters
        id: defaults
        run: |
          # Initialize with base defaults for feature branches/other pushes
          TEST_TYPE="mock"
          ENVIRONMENT="test" # Default environment for mock, can be overridden
          TEST_CATEGORY="all"
          FAST_TRACK="false"
          DEPLOY_INFRA="auto" # For mock on feature branches, infra deployment should be skipped by later conditions

          # Override for PRs
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            if [[ "${{ github.base_ref }}" == "main" || "${{ github.base_ref }}" == "dev" ]]; then
              # PR to main or dev - use REAL tests in TEST env
              TEST_TYPE="real"
              ENVIRONMENT="test"
              DEPLOY_INFRA="auto" # Real tests in test env might need infra
            fi
          # Override for pushes to main or dev
          elif [[ "${{ github.event_name }}" == "push" ]]; then
            if [[ "${{ github.ref }}" == "refs/heads/main" || "${{ github.ref }}" == "refs/heads/dev" ]]; then
              # Push to main or dev - use REAL tests in TEST env
              TEST_TYPE="real"
              ENVIRONMENT="test"
              DEPLOY_INFRA="auto" # Real tests in test env might need infra
            fi
          fi

          # Override with workflow inputs if they exist (for workflow_dispatch/workflow_call)
          if [[ "${{ github.event.inputs.test_type }}" != "" ]]; then
            TEST_TYPE="${{ github.event.inputs.test_type }}"
          elif [[ "${{ inputs.test_type }}" != "" ]]; then
            TEST_TYPE="${{ inputs.test_type }}"
          fi

          if [[ "${{ github.event.inputs.environment }}" != "" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
          elif [[ "${{ inputs.environment }}" != "" ]]; then
            ENVIRONMENT="${{ inputs.environment }}"
          fi

          if [[ "${{ github.event.inputs.test_category }}" != "" ]]; then
            TEST_CATEGORY="${{ github.event.inputs.test_category }}"
          elif [[ "${{ inputs.test_category }}" != "" ]]; then
            TEST_CATEGORY="${{ inputs.test_category }}"
          fi

          # Handle fast_track as a string
          if [[ "${{ github.event.inputs.fast_track }}" == "true" || "${{ inputs.fast_track }}" == "true" ]]; then
            FAST_TRACK="true"
          fi

          if [[ "${{ github.event.inputs.deploy_infrastructure }}" != "" ]]; then
            DEPLOY_INFRA="${{ github.event.inputs.deploy_infrastructure }}"
          elif [[ "${{ inputs.deploy_infrastructure }}" != "" ]]; then
            DEPLOY_INFRA="${{ inputs.deploy_infrastructure }}"
          fi

          # Set outputs for use in conditions
          echo "test_type=${TEST_TYPE}" >> $GITHUB_OUTPUT
          echo "environment=${ENVIRONMENT}" >> $GITHUB_OUTPUT
          echo "test_category=${TEST_CATEGORY}" >> $GITHUB_OUTPUT
          echo "fast_track=${FAST_TRACK}" >> $GITHUB_OUTPUT
          echo "deploy_infra=${DEPLOY_INFRA}" >> $GITHUB_OUTPUT

          # Set environment variables for use in steps
          echo "TEST_TYPE=${TEST_TYPE}" >> $GITHUB_ENV
          echo "ENVIRONMENT=${ENVIRONMENT}" >> $GITHUB_ENV
          echo "TEST_CATEGORY=${TEST_CATEGORY}" >> $GITHUB_ENV
          echo "FAST_TRACK=${FAST_TRACK}" >> $GITHUB_ENV
          echo "DEPLOY_INFRA=${DEPLOY_INFRA}" >> $GITHUB_ENV

          echo "Using TEST_TYPE: ${TEST_TYPE}"
          echo "Using ENVIRONMENT: ${ENVIRONMENT}"
          echo "Using TEST_CATEGORY: ${TEST_CATEGORY}"
          echo "Using FAST_TRACK: ${FAST_TRACK}"
          echo "Using DEPLOY_INFRA: ${DEPLOY_INFRA}"

      # Azure Login for real tests
      - name: Debug Azure Credentials
        run: |
          echo "Event name: ${{ github.event_name }}"
          echo "Test type: ${{ env.TEST_TYPE || 'not specified' }}"
          echo "Has AZURE_CREDENTIALS secret: ${{ secrets.AZURE_CREDENTIALS != '' }}"

          # Print the structure of the AZURE_CREDENTIALS (without revealing sensitive values)
          if [[ "${{ secrets.AZURE_CREDENTIALS }}" != "" ]]; then
            echo "AZURE_CREDENTIALS structure:"
            # Extract keys from the JSON without showing values
            echo "${{ secrets.AZURE_CREDENTIALS }}" | grep -o '"[^"]*":' | tr -d '":' | sort
          fi

      # Create a dummy Azure credentials file if the secret is missing
      - name: Check Azure Credentials
        if: ${{ github.event_name == 'push' || github.event_name == 'pull_request' || env.TEST_TYPE == 'real' || contains(env.TEST_TYPE, 'both') }}
        id: check-azure-creds
        run: |
          if [[ -n "${{ secrets.AZURE_CREDENTIALS }}" ]]; then
            echo "has_credentials=true" >> $GITHUB_OUTPUT
          else
            echo "has_credentials=false" >> $GITHUB_OUTPUT
            echo "::warning::AZURE_CREDENTIALS secret is missing. Creating a dummy credentials file for testing."
            # Create a dummy credentials file
            echo '{"clientId":"dummy-client-id","clientSecret":"dummy-client-secret","subscriptionId":"dummy-subscription-id","tenantId":"dummy-tenant-id"}' > azure-credentials.json
            # Set the AZURE_CREDENTIALS environment variable
            echo "AZURE_CREDENTIALS_JSON=$(cat azure-credentials.json)" >> $GITHUB_ENV
          fi

      - name: Azure Login
        # Always run Azure login for PR and push events, or when real tests are specified
        id: azure-login
        if: ${{ github.event_name == 'push' || github.event_name == 'pull_request' || env.TEST_TYPE == 'real' || contains(env.TEST_TYPE, 'both') }}
        uses: azure/login@v2.1.0
        with:
          creds: ${{ steps.check-azure-creds.outputs.has_credentials == 'true' && secrets.AZURE_CREDENTIALS || env.AZURE_CREDENTIALS_JSON }}
        continue-on-error: true

      # Report login status
      - name: Report Azure Login Status
        if: ${{ github.event_name == 'push' || github.event_name == 'pull_request' || env.TEST_TYPE == 'real' || contains(env.TEST_TYPE, 'both') }}
        run: |
          if [[ "${{ steps.azure-login.outcome }}" == "success" ]]; then
            echo "✅ Azure login succeeded"
          else
            echo "❌ Azure login failed"
          fi

      - name: Set Terraform Auth Variables
        if: steps.azure-login.outcome == 'success'
        run: |
          echo "Setting Terraform ARM environment variables for Service Principal auth..."
          echo "ARM_CLIENT_ID=${{ secrets.AZURE_CLIENT_ID || fromJson(secrets.AZURE_CREDENTIALS).clientId }}" >> $GITHUB_ENV
          echo "ARM_CLIENT_SECRET=${{ secrets.AZURE_CLIENT_SECRET || fromJson(secrets.AZURE_CREDENTIALS).clientSecret }}" >> $GITHUB_ENV
          echo "ARM_SUBSCRIPTION_ID=${{ secrets.AZURE_SUBSCRIPTION_ID || fromJson(secrets.AZURE_CREDENTIALS).subscriptionId }}" >> $GITHUB_ENV
          echo "ARM_TENANT_ID=${{ secrets.AZURE_TENANT_ID || fromJson(secrets.AZURE_CREDENTIALS).tenantId }}" >> $GITHUB_ENV
          echo "ARM_USE_AZUREAD=true" >> $GITHUB_ENV # Ensures Azure AD based auth is used.

      # Determine test parameters
      - name: Determine test parameters
        id: test-params
        run: |
          # Use environment variables set by the 'Set default parameters' step
          # These are available as env.VAR_NAME in shell scripts
          CURRENT_TEST_TYPE="${{ env.TEST_TYPE }}"
          CURRENT_ENVIRONMENT="${{ env.ENVIRONMENT }}"
          CURRENT_TEST_CATEGORY="${{ env.TEST_CATEGORY }}"
          CURRENT_FAST_TRACK="${{ env.FAST_TRACK }}"

          # Override with workflow_dispatch or workflow_call inputs if they were the trigger
          if [[ "${{ github.event_name }}" == "workflow_dispatch" || "${{ github.event_name }}" == "workflow_call" ]]; then
            # For explicit calls, inputs take precedence over branch-derived defaults
            CURRENT_TEST_TYPE="${{ inputs.test_type || github.event.inputs.test_type || env.TEST_TYPE }}"
            CURRENT_ENVIRONMENT="${{ inputs.environment || github.event.inputs.environment || env.ENVIRONMENT }}"
            CURRENT_TEST_CATEGORY="${{ inputs.test_category || github.event.inputs.test_category || env.TEST_CATEGORY }}"
            # Handle fast_track as a string
            CURRENT_FAST_TRACK="${{ inputs.fast_track || github.event.inputs.fast_track || env.FAST_TRACK }}"
          fi

          # Set command-line arguments for the test runner
          ARGS="--category ${CURRENT_TEST_CATEGORY} --env ${CURRENT_ENVIRONMENT}"

          if [[ "${CURRENT_TEST_TYPE}" == "mock" ]]; then
            ARGS="${ARGS} --mock"
          elif [[ "${CURRENT_TEST_TYPE}" == "real" ]]; then
            ARGS="${ARGS} --real"
          elif [[ "${CURRENT_TEST_TYPE}" == "both" ]]; then
            # Both mock and real
            ARGS="${ARGS} --mock --real"
          fi

          if [[ "${CURRENT_FAST_TRACK}" == "true" ]]; then
            # For fast track, only run critical tests
            if [[ "${CURRENT_TEST_CATEGORY}" == "all" ]]; then
              # Override category to unit for fast track, but keep environment and real/mock status
              ARGS="--category unit --env ${CURRENT_ENVIRONMENT}"
              if [[ "${CURRENT_TEST_TYPE}" == "mock" ]]; then
                ARGS="${ARGS} --mock"
              elif [[ "${CURRENT_TEST_TYPE}" == "real" ]]; then
                ARGS="${ARGS} --real"
              elif [[ "${CURRENT_TEST_TYPE}" == "both" ]];then
                ARGS="${ARGS} --mock --real"
              fi
            fi
          fi

          echo "args=${ARGS}" >> $GITHUB_OUTPUT
          # Output the environment that will actually be used by tests for clarity
          echo "env=${CURRENT_ENVIRONMENT}" >> $GITHUB_OUTPUT
          echo "Test parameters for runner: ${ARGS}"
          echo "Test environment for runner: ${CURRENT_ENVIRONMENT}"

      # Set up environment variables from GitHub secrets/variables based on environment
      - name: Set up environment variables
        id: env-vars
        run: |
          # Determine environment-specific variables
          ENV_SUFFIX="${{ steps.test-params.outputs.env }}"
          ENV_SUFFIX_UPPER=$(echo $ENV_SUFFIX | tr '[:lower:]' '[:upper:]')

          # Use environment-specific secrets and variables
          if [[ "${{ steps.test-params.outputs.args }}" == *"--real"* ]]; then
            # For real tests, use the infrastructure-provided values
            echo "Using real Azure services from environment: $ENV_SUFFIX"

            # Search service
            echo "AZURE_SEARCH_ENDPOINT=${{ vars[format('AZURE_SEARCH_ENDPOINT_{0}', env.ENV_SUFFIX_UPPER)] || vars.AZURE_SEARCH_ENDPOINT || secrets.AZURE_SEARCH_ENDPOINT }}" >> $GITHUB_ENV
            echo "AZURE_COGNITIVE_SEARCH_ENDPOINT=${{ vars[format('AZURE_SEARCH_ENDPOINT_{0}', env.ENV_SUFFIX_UPPER)] || vars.AZURE_SEARCH_ENDPOINT || secrets.AZURE_SEARCH_ENDPOINT }}" >> $GITHUB_ENV
            echo "AZURE_SEARCH_API_KEY=${{ secrets[format('AZURE_SEARCH_API_KEY_{0}', env.ENV_SUFFIX_UPPER)] || secrets.AZURE_SEARCH_API_KEY }}" >> $GITHUB_ENV
            echo "AZURE_SEARCH_INDEX_NAME=konveyor-documents" >> $GITHUB_ENV

            # OpenAI service
            echo "AZURE_OPENAI_ENDPOINT=${{ vars[format('AZURE_OPENAI_ENDPOINT_{0}', env.ENV_SUFFIX_UPPER)] || vars.AZURE_OPENAI_ENDPOINT || secrets.AZURE_OPENAI_ENDPOINT }}" >> $GITHUB_ENV
            echo "AZURE_OPENAI_API_KEY=${{ secrets[format('AZURE_OPENAI_API_KEY_{0}', env.ENV_SUFFIX_UPPER)] || secrets.AZURE_OPENAI_API_KEY }}" >> $GITHUB_ENV
            echo "AZURE_OPENAI_API_VERSION=2024-02-01-preview" >> $GITHUB_ENV
            echo "AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-4o" >> $GITHUB_ENV
            echo "AZURE_OPENAI_EMBEDDING_DEPLOYMENT=${{ vars[format('AZURE_OPENAI_EMBEDDING_DEPLOYMENT_{0}', env.ENV_SUFFIX_UPPER)] || vars.AZURE_OPENAI_EMBEDDING_DEPLOYMENT || 'text-embedding-ada-002' }}" >> $GITHUB_ENV

            # Document Intelligence
            echo "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=${{ vars[format('AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT_{0}', env.ENV_SUFFIX_UPPER)] || vars.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT || secrets.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT }}" >> $GITHUB_ENV
            echo "AZURE_DOCUMENT_INTELLIGENCE_API_KEY=${{ secrets[format('AZURE_DOCUMENT_INTELLIGENCE_API_KEY_{0}', env.ENV_SUFFIX_UPPER)] || secrets.AZURE_DOCUMENT_INTELLIGENCE_API_KEY }}" >> $GITHUB_ENV

            # Storage
            echo "AZURE_STORAGE_CONNECTION_STRING=${{ secrets[format('AZURE_STORAGE_CONNECTION_STRING_{0}', env.ENV_SUFFIX_UPPER)] || secrets.AZURE_STORAGE_CONNECTION_STRING }}" >> $GITHUB_ENV
            echo "AZURE_STORAGE_CONTAINER_NAME=documents" >> $GITHUB_ENV

            # Print debug info (without exposing secrets)
            echo "Azure Search Endpoint: ${AZURE_SEARCH_ENDPOINT:-Not set}"
            echo "Azure OpenAI Endpoint: ${AZURE_OPENAI_ENDPOINT:-Not set}"
            echo "Azure Document Intelligence Endpoint: ${AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT:-Not set}"
            echo "Azure Storage Container: ${AZURE_STORAGE_CONTAINER_NAME:-Not set}"

            # Check if any required variables are missing
            if [ -z "${AZURE_SEARCH_ENDPOINT}" ] || [ -z "${AZURE_SEARCH_API_KEY}" ] || \
               [ -z "${AZURE_OPENAI_ENDPOINT}" ] || [ -z "${AZURE_OPENAI_API_KEY}" ] || \
               [ -z "${AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT}" ] || [ -z "${AZURE_DOCUMENT_INTELLIGENCE_API_KEY}" ] || \
               [ -z "${AZURE_STORAGE_CONNECTION_STRING}" ]; then
              echo "::warning::Some Azure credentials are missing. Tests requiring real services may fail."
              # Fall back to mock values for any missing credentials
              [ -z "${AZURE_SEARCH_ENDPOINT}" ] && echo "AZURE_SEARCH_ENDPOINT=https://mock-search-endpoint.search.windows.net" >> $GITHUB_ENV
              [ -z "${AZURE_COGNITIVE_SEARCH_ENDPOINT}" ] && echo "AZURE_COGNITIVE_SEARCH_ENDPOINT=https://mock-search-endpoint.search.windows.net" >> $GITHUB_ENV
              [ -z "${AZURE_SEARCH_API_KEY}" ] && echo "AZURE_SEARCH_API_KEY=mock-search-api-key" >> $GITHUB_ENV
              [ -z "${AZURE_OPENAI_ENDPOINT}" ] && echo "AZURE_OPENAI_ENDPOINT=https://mock-openai-endpoint.openai.azure.com" >> $GITHUB_ENV
              [ -z "${AZURE_OPENAI_API_KEY}" ] && echo "AZURE_OPENAI_API_KEY=mock-openai-api-key" >> $GITHUB_ENV
              [ -z "${AZURE_OPENAI_EMBEDDING_DEPLOYMENT}" ] && echo "AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002" >> $GITHUB_ENV
              [ -z "${AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT}" ] && echo "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=https://mock-docint-endpoint.cognitiveservices.azure.com" >> $GITHUB_ENV
              [ -z "${AZURE_DOCUMENT_INTELLIGENCE_API_KEY}" ] && echo "AZURE_DOCUMENT_INTELLIGENCE_API_KEY=mock-docint-api-key" >> $GITHUB_ENV
              [ -z "${AZURE_STORAGE_CONNECTION_STRING}" ] && echo "AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=mockaccount;AccountKey=mock-key;EndpointSuffix=core.windows.net" >> $GITHUB_ENV
            fi
          else
            # For mock tests, use dummy values
            echo "Using mock Azure services"
            echo "AZURE_SEARCH_ENDPOINT=https://mock-search-endpoint.search.windows.net" >> $GITHUB_ENV
            echo "AZURE_COGNITIVE_SEARCH_ENDPOINT=https://mock-search-endpoint.search.windows.net" >> $GITHUB_ENV
            echo "AZURE_SEARCH_API_KEY=mock-search-api-key" >> $GITHUB_ENV
            echo "AZURE_SEARCH_INDEX_NAME=konveyor-documents" >> $GITHUB_ENV

            echo "AZURE_OPENAI_ENDPOINT=https://mock-openai-endpoint.openai.azure.com" >> $GITHUB_ENV
            echo "AZURE_OPENAI_API_KEY=mock-openai-api-key" >> $GITHUB_ENV
            echo "AZURE_OPENAI_API_VERSION=2024-02-01-preview" >> $GITHUB_ENV
            echo "AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo" >> $GITHUB_ENV
            echo "AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002" >> $GITHUB_ENV

            echo "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=https://mock-docint-endpoint.cognitiveservices.azure.com" >> $GITHUB_ENV
            echo "AZURE_DOCUMENT_INTELLIGENCE_API_KEY=mock-docint-api-key" >> $GITHUB_ENV

            echo "AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=mockaccount;AccountKey=mock-key;EndpointSuffix=core.windows.net" >> $GITHUB_ENV
            echo "AZURE_STORAGE_CONTAINER_NAME=documents" >> $GITHUB_ENV
          fi

      # Run the tests with the determined parameters
      - name: Build Docker image for testing
        run: |
          # Create a .env file with all necessary environment variables
          echo "DJANGO_SETTINGS_MODULE=konveyor.settings.test" > .env
          echo "DJANGO_ENV=${{ steps.test-params.outputs.env }}" >> .env
          echo "DJANGO_DEBUG=False" >> .env
          echo "DJANGO_SECRET_KEY=django-insecure-test-key-for-ci" >> .env
          echo "AZURE_SEARCH_ENDPOINT=${AZURE_SEARCH_ENDPOINT:-https://mock-search-endpoint.search.windows.net}" >> .env
          echo "AZURE_COGNITIVE_SEARCH_ENDPOINT=${AZURE_COGNITIVE_SEARCH_ENDPOINT:-https://mock-search-endpoint.search.windows.net}" >> .env
          echo "AZURE_SEARCH_API_KEY=${AZURE_SEARCH_API_KEY:-mock-search-api-key}" >> .env
          echo "AZURE_SEARCH_INDEX_NAME=konveyor-documents" >> .env
          echo "AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT:-https://mock-openai-endpoint.openai.azure.com}" >> .env
          echo "AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-mock-openai-api-key}" >> .env
          echo "AZURE_OPENAI_API_VERSION=2024-02-01-preview" >> .env
          echo "AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-4o" >> .env
          echo "AZURE_OPENAI_EMBEDDING_DEPLOYMENT=${AZURE_OPENAI_EMBEDDING_DEPLOYMENT:-text-embedding-ada-002}" >> .env
          echo "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=${AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT:-https://mock-docint-endpoint.cognitiveservices.azure.com}" >> .env
          echo "AZURE_DOCUMENT_INTELLIGENCE_API_KEY=${AZURE_DOCUMENT_INTELLIGENCE_API_KEY:-mock-docint-api-key}" >> .env
          echo "AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING:-DefaultEndpointsProtocol=https;AccountName=mockaccount;AccountKey=mock-key;EndpointSuffix=core.windows.net}" >> .env
          echo "AZURE_STORAGE_CONTAINER_NAME=documents" >> .env

          # Build the Docker image with the environment file
          docker build --build-arg DJANGO_SETTINGS_MODULE=konveyor.settings.test -t konveyor:latest .

      - name: Run tests in Docker
        id: run-tests
        run: |
          mkdir -p tests/results
          # Set proper permissions on the results directory
          chmod -R 777 tests/results

          # Run tests in Docker container with environment file
          # Use a different approach to handle permissions
          docker run --rm \
            --env-file .env \
            -v $(pwd)/tests/results:/app/test-output \
            -v $(pwd)/tests/run_all_tests.py:/app/tests/run_all_tests.py \
            konveyor:latest \
            sh -c "cd /app && mkdir -p /app/tests/results && python tests/run_all_tests.py ${{ steps.test-params.outputs.args }} -v; echo 'run_all_tests.py exit code: $?'; echo 'Listing /app/tests/results:'; ls -la /app/tests/results; cp -r /app/tests/results/* /app/test-output/ 2>/dev/null || true && echo 'Test results copied to host'"

          # Store exit code
          echo "exit_code=$?" >> $GITHUB_OUTPUT

      # Set test result output
      - name: Set test result
        id: test-result
        run: |
          if [[ "${{ steps.run-tests.outputs.exit_code }}" == "0" ]]; then
            echo "passed=true" >> $GITHUB_OUTPUT
          else
            echo "passed=false" >> $GITHUB_OUTPUT
          fi

      # Create test results directory if it doesn't exist
      - name: Create test results directory
        if: always()
        run: mkdir -p tests/results

      # Upload test results as artifacts
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-integration
          path: tests/results/**/*.xml
          retention-days: 5

      # Clean up infrastructure if it was deployed for real tests
# Removed GitHub CLI installation step

      - name: Azure Login for Cleanup
        id: azure-login-cleanup
        if: ${{ always() && (github.event_name == 'push' || github.event_name == 'pull_request' || env.TEST_TYPE == 'real' || contains(env.TEST_TYPE, 'both')) }}
        uses: azure/login@v2.1.0
        with:
          creds: ${{ steps.check-azure-creds.outputs.has_credentials == 'true' && secrets.AZURE_CREDENTIALS || env.AZURE_CREDENTIALS_JSON }}
        continue-on-error: true

      # Report cleanup login status
      - name: Report Cleanup Login Status
        if: ${{ always() && (github.event_name == 'push' || github.event_name == 'pull_request' || env.TEST_TYPE == 'real' || contains(env.TEST_TYPE, 'both')) }}
        run: |
          if [[ "${{ steps.azure-login-cleanup.outcome }}" == "success" ]]; then
            echo "✅ Azure login for cleanup succeeded"
          else
            echo "❌ Azure login for cleanup failed"
          fi

      - name: Set up Terraform for Cleanup
        if: >-
          ${{
            always() &&
            (env.DEPLOY_INFRA == 'always' ||
            (env.DEPLOY_INFRA == 'auto' && (env.TEST_TYPE == 'real' || (github.event_name == 'pull_request' && github.base_ref == 'main'))))
          }}
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: '1.5.7'

#TODO: Assumption: Ensure run_all_tests.py script or the pytest command calls would be updated to generate --html=test_report/index.html when we uncomment this.
#TODO: The actual github pages step will be added when we are successful with Upload Pages Artifact in code-quality

Instructions/*

# Trunk (keep config, ignore generated files)
.trunk/actions
.trunk/logs
.trunk/notifications
.trunk/out
.trunk/plugins
.trunk/tools

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media
staticfiles/

# Virtual Environment
venv/
ENV/
.env
.env.*
!.env.example

## IDE
.idea/
.vscode/
*.swp
*.swo

# Cursor
.cursorignore
.cursor/

# Windsurf Rules
.windsurfrules

# OS specific
.DS_Store
Thumbs.db

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/

# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Task files
tasks.json
tasks/

# Utility scripts
fix_*.py
tests/results/

# Terraform
.terraform
.terraform.lock.hcl

# Terraform state
terraform.tfstate
terraform.tfstate.backup

# Terraform variables
variables.tfvars

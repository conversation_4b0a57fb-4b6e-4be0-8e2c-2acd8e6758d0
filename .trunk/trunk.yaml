# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.22.15
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.7.0
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - node@22.16.0
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
# Configured for gradual integration - avoiding overlap with existing pre-commit setup
lint:
  enabled:
    # Security and Infrastructure (NEW capabilities)
    - actionlint@1.7.7          # GitHub Actions linting
    - bandit@1.8.3              # Python security scanning
    - checkov@3.2.435           # Infrastructure security
    - hadolint@2.12.1           # Dockerfile linting
    - osv-scanner@2.0.2         # Vulnerability scanning
    - shellcheck@0.10.0         # Shell script linting
    - shfmt@3.6.0               # Shell script formatting
    - tflint@0.58.0             # Terraform linting
    - trufflehog@3.88.34        # Secret scanning

    # Documentation and Assets
    - markdownlint@0.45.0       # Markdown linting
    - oxipng@9.1.5              # PNG optimization
    - prettier@3.5.3            # JS/TS/JSON formatting
    - taplo@0.9.3               # TOML formatting
    - yamllint@1.37.1           # YAML linting

    # Basic file checks (complement pre-commit)
    - git-diff-check            # Git diff validation

  disabled:
    # Disabled to avoid conflicts with existing pre-commit setup
    - black                     # Pre-commit uses ruff for formatting
    - isort                     # Pre-commit uses ruff for import sorting
    - mypy                      # Pre-commit already handles this
    - ruff                      # Pre-commit already handles this
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available

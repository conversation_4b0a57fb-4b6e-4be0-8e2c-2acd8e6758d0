resource "azurerm_cognitive_account" "document_intelligence" {
  name                = var.name
  location            = var.location
  resource_group_name = var.resource_group_name
  kind                = "FormRecognizer"
  sku_name            = var.sku_name
  tags                = var.tags

  identity {
    type = "SystemAssigned"
  }

  lifecycle {
    # Prevent destroy during testing to avoid soft-delete issues
    prevent_destroy = true
  }
}

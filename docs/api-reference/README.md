# 🔌 API Reference

Complete reference documentation for the Konveyor REST API.

## 📚 API Documentation Structure

### Core APIs
- **[REST API](rest-api.md)** - Main HTTP API endpoints
- **[Authentication](authentication.md)** - API authentication and authorization
- **[Rate Limiting](rate-limiting.md)** - API usage limits and throttling

### Specialized APIs
- **[Skills API](skills-api.md)** - Semantic Kernel skills interface
- **[Webhooks](webhooks.md)** - Event-driven integrations
- **[Bot Framework](bot-framework.md)** - Conversational AI endpoints

### Integration Guides
- **[Client Libraries](client-libraries.md)** - SDKs and client libraries
- **[Examples](examples.md)** - Code examples and tutorials
- **[Postman Collection](postman-collection.md)** - Ready-to-use API collection

## 🚀 Quick Start

### Base URL

```
Production: https://your-konveyor-instance.azurewebsites.net
Development: http://localhost:8000
```

### Authentication

All API requests require authentication:

```bash
# Using API key
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/documents/

# Using Azure AD token
curl -H "Authorization: Bearer YOUR_AZURE_TOKEN" \
  https://your-instance.azurewebsites.net/api/documents/
```

### Basic Example

```bash
# Health check
curl https://your-instance.azurewebsites.net/healthz/

# Ask a question
curl -X POST https://your-instance.azurewebsites.net/api/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "message": "How do I get started?",
    "conversation_id": "user-123"
  }'
```

## 📋 API Overview

### Core Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/healthz/` | GET | Health check |
| `/api/azure-openai-status/` | GET | Azure OpenAI service status |
| `/` | GET | Application index |

### Document Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/documents/` | GET | Document management interface |
| `/documents/upload/` | POST | Upload document |

### Search API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/search/api/query/` | POST | Semantic search with full features |
| `/search/api/index-document/` | POST | Index specific document |
| `/search/api/reindex-all/` | POST | Reindex all documents |
| `/search/simple/` | GET, POST | Simple search interface |

### Bot Integration

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/bot/` | GET | Bot root handler |
| `/api/bot/slack/events/` | POST | Slack event webhook |
| `/api/bot/slack/commands/` | POST | Slack slash commands |
| `/api/bot/debug/` | GET | Bot debug endpoint |

### Feedback API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/bot/feedback/stats/` | GET | Feedback statistics |
| `/api/bot/feedback/by-skill/` | GET | Feedback by skill |
| `/api/bot/feedback/export/` | GET | Export feedback data |

## 🔧 Request/Response Format

### Standard Request Format

```json
{
  "data": {
    // Request payload
  },
  "metadata": {
    "request_id": "uuid",
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0"
  }
}
```

### Standard Response Format

```json
{
  "success": true,
  "data": {
    // Response payload
  },
  "metadata": {
    "request_id": "uuid",
    "timestamp": "2024-01-01T00:00:00Z",
    "processing_time_ms": 150
  },
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "message",
      "issue": "Required field is missing"
    }
  },
  "metadata": {
    "request_id": "uuid",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

## 🎯 Common Use Cases

### 1. Document Upload and Processing

```bash
# Upload a document
curl -X POST https://your-instance.azurewebsites.net/documents/upload/ \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -F "file=@document.pdf" \
  -F "title=User Manual" \
  -F "category=documentation"

# Check processing status
curl https://your-instance.azurewebsites.net/documents/123/ \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 2. Conversational AI

```bash
# Start a conversation
curl -X POST https://your-instance.azurewebsites.net/api/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "message": "How do I deploy to production?",
    "conversation_id": "conv-123",
    "user_id": "user-456"
  }'

# Continue conversation
curl -X POST https://your-instance.azurewebsites.net/api/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "message": "What about rollback procedures?",
    "conversation_id": "conv-123",
    "user_id": "user-456"
  }'
```

### 3. Search and Retrieval

```bash
# Semantic search
curl -X POST https://your-instance.azurewebsites.net/api/search/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "query": "database configuration",
    "top_k": 5,
    "filters": {
      "category": "technical",
      "date_range": "last_30_days"
    }
  }'

# Hybrid search (semantic + keyword)
curl -X POST https://your-instance.azurewebsites.net/api/search/hybrid/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "query": "PostgreSQL setup",
    "semantic_weight": 0.7,
    "keyword_weight": 0.3,
    "top_k": 10
  }'
```

## 📊 Response Examples

### Chat Response

```json
{
  "success": true,
  "data": {
    "response": "To deploy to production, follow these steps:\n\n1. Create a release branch\n2. Run all tests\n3. Get approval from team lead\n4. Deploy using the CI/CD pipeline\n\nFor detailed instructions, see the Deployment Guide.",
    "sources": [
      {
        "document_id": "doc-123",
        "title": "Deployment Guide",
        "chunk_id": "chunk-456",
        "relevance_score": 0.95,
        "url": "/documents/123/"
      }
    ],
    "conversation_id": "conv-123",
    "message_id": "msg-789",
    "suggested_followups": [
      "What if the deployment fails?",
      "How do I rollback a deployment?",
      "What are the pre-deployment checks?"
    ]
  },
  "metadata": {
    "request_id": "req-abc123",
    "timestamp": "2024-01-01T12:00:00Z",
    "processing_time_ms": 1250,
    "model_used": "gpt-35-turbo",
    "tokens_used": 150
  }
}
```

### Search Response

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "document_id": "doc-123",
        "title": "Database Setup Guide",
        "content": "PostgreSQL configuration for production environments...",
        "relevance_score": 0.92,
        "metadata": {
          "category": "technical",
          "last_updated": "2024-01-01T00:00:00Z",
          "author": "DevOps Team"
        },
        "highlights": [
          "PostgreSQL <em>setup</em> requires careful configuration",
          "Production <em>database</em> settings differ from development"
        ]
      }
    ],
    "total_results": 15,
    "query_time_ms": 45
  },
  "pagination": {
    "page": 1,
    "per_page": 10,
    "total": 15,
    "total_pages": 2
  }
}
```

## 🔒 Security

### API Key Management

```bash
# Generate API key
curl -X POST https://your-instance.azurewebsites.net/api/auth/keys/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "Production Client",
    "permissions": ["read", "write"],
    "expires_at": "2024-12-31T23:59:59Z"
  }'

# Revoke API key
curl -X DELETE https://your-instance.azurewebsites.net/api/auth/keys/key-123/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Rate Limiting

```bash
# Rate limit headers in response
HTTP/1.1 200 OK
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## 📈 Monitoring

### Health Endpoints

```bash
# Basic health check
curl https://your-instance.azurewebsites.net/healthz/

# Detailed health check
curl https://your-instance.azurewebsites.net/api/health/detailed/ \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Metrics Endpoints

```bash
# API usage metrics
curl https://your-instance.azurewebsites.net/api/metrics/usage/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Performance metrics
curl https://your-instance.azurewebsites.net/api/metrics/performance/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 🛠️ Development Tools

### OpenAPI Specification

```bash
# Get OpenAPI spec
curl https://your-instance.azurewebsites.net/api/schema/

# Interactive API docs
open https://your-instance.azurewebsites.net/api/docs/
```

### SDK Examples

```python
# Python SDK
from konveyor_client import KonveyorClient

client = KonveyorClient(
    base_url="https://your-instance.azurewebsites.net",
    api_key="YOUR_API_KEY"
)

# Ask a question
response = client.chat.send_message(
    message="How do I get started?",
    conversation_id="conv-123"
)
print(response.data.response)
```

```javascript
// JavaScript SDK
import { KonveyorClient } from '@konveyor/client';

const client = new KonveyorClient({
  baseUrl: 'https://your-instance.azurewebsites.net',
  apiKey: 'YOUR_API_KEY'
});

// Ask a question
const response = await client.chat.sendMessage({
  message: 'How do I get started?',
  conversationId: 'conv-123'
});
console.log(response.data.response);
```

## 🆘 Troubleshooting

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 400 | Bad Request | Check request format and parameters |
| 401 | Unauthorized | Verify API key or authentication token |
| 403 | Forbidden | Check permissions for the requested resource |
| 404 | Not Found | Verify endpoint URL and resource ID |
| 429 | Rate Limited | Reduce request frequency or upgrade plan |
| 500 | Server Error | Check service status or contact support |

### Debug Mode

```bash
# Enable debug headers
curl -H "X-Debug: true" \
  https://your-instance.azurewebsites.net/api/chat/ \
  ...
```

---

**Next**: [REST API Details](rest-api.md) | [Authentication Guide](authentication.md)

# 🔐 Authentication Guide

Complete guide to authentication and authorization for the Konveyor API.

## 📋 Table of Contents

- [Authentication Methods](#authentication-methods)
- [API Key Authentication](#api-key-authentication)
- [Azure AD Authentication](#azure-ad-authentication)
- [Bot Authentication](#bot-authentication)
- [Authorization and Permissions](#authorization-and-permissions)
- [Security Best Practices](#security-best-practices)
- [Troubleshooting](#troubleshooting)

## 🔑 Authentication Methods

Konveyor supports multiple authentication methods depending on your use case:

| Method | Use Case | Security Level |
|--------|----------|----------------|
| **API Key** | Direct API access, automation | Medium |
| **Azure AD** | Enterprise integration | High |
| **Bot Token** | Slack/Teams integration | Medium |
| **No Auth** | Health checks, public endpoints | Low |

## 🗝️ API Key Authentication

### Overview

API keys provide simple authentication for direct API access and automation scenarios.

### Obtaining an API Key

#### Method 1: Django Admin Interface

1. **Access Django Admin**: Go to `https://your-instance.azurewebsites.net/admin/`
2. **Login**: Use your superuser credentials
3. **Navigate**: Go to "Authentication and Authorization" → "Tokens"
4. **Create Token**: Click "Add Token" and select a user
5. **Copy Key**: Save the generated token securely

#### Method 2: Django Shell

```python
# Access Django shell
python manage.py shell

# Create API key for user
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

user = User.objects.get(username='your-username')
token, created = Token.objects.get_or_create(user=user)
print(f"API Key: {token.key}")
```

#### Method 3: Management Command

```bash
# Create API key via management command
python manage.py drf_create_token your-username
```

### Using API Keys

Include the API key in the `Authorization` header:

```bash
curl -H "Authorization: Token YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/
```

### API Key Management

#### Rotating API Keys

```python
# Rotate API key
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import User

user = User.objects.get(username='your-username')
# Delete old token
Token.objects.filter(user=user).delete()
# Create new token
token = Token.objects.create(user=user)
print(f"New API Key: {token.key}")
```

#### Revoking API Keys

```python
# Revoke API key
from rest_framework.authtoken.models import Token
Token.objects.filter(key='YOUR_API_KEY').delete()
```

### API Key Security

- **Store securely**: Never commit API keys to version control
- **Use environment variables**: Store in `.env` files or secure vaults
- **Rotate regularly**: Change API keys periodically
- **Limit scope**: Use different keys for different applications
- **Monitor usage**: Track API key usage and detect anomalies

## 🏢 Azure AD Authentication

### Overview

Azure Active Directory integration provides enterprise-grade authentication with single sign-on (SSO) capabilities.

### Azure AD Setup

#### 1. Register Application

```bash
# Create Azure AD app registration
az ad app create \
  --display-name "Konveyor API" \
  --web-redirect-uris "https://your-instance.azurewebsites.net/auth/callback/" \
  --required-resource-accesses @manifest.json
```

#### 2. Configure Permissions

Required permissions for Konveyor:
- `User.Read` - Read user profile
- `Directory.Read.All` - Read directory data (optional)

#### 3. Environment Configuration

```bash
# Azure AD configuration
AZURE_AD_TENANT_ID=your-tenant-id
AZURE_AD_CLIENT_ID=your-client-id
AZURE_AD_CLIENT_SECRET=your-client-secret
AZURE_AD_REDIRECT_URI=https://your-instance.azurewebsites.net/auth/callback/
```

### Using Azure AD Tokens

#### Obtaining Access Token

```python
import requests
from msal import ConfidentialClientApplication

app = ConfidentialClientApplication(
    client_id="your-client-id",
    client_credential="your-client-secret",
    authority="https://login.microsoftonline.com/your-tenant-id"
)

# Get token for application
result = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
access_token = result['access_token']
```

#### Using Token with API

```bash
curl -H "Authorization: Bearer YOUR_AZURE_AD_TOKEN" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/
```

### Token Validation

Konveyor validates Azure AD tokens by:

1. **Signature verification**: Validates JWT signature
2. **Issuer validation**: Ensures token is from correct tenant
3. **Audience validation**: Confirms token is for this application
4. **Expiration check**: Verifies token is not expired

## 🤖 Bot Authentication

### Slack Bot Authentication

#### Bot Token Setup

1. **Create Slack App**: Go to [api.slack.com/apps](https://api.slack.com/apps)
2. **Install to Workspace**: Get bot token (starts with `xoxb-`)
3. **Configure Environment**:

```bash
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_SIGNING_SECRET=your-signing-secret
```

#### Request Verification

Slack requests are verified using:

```python
import hmac
import hashlib
import time

def verify_slack_request(request_body, timestamp, signature, signing_secret):
    # Check timestamp (prevent replay attacks)
    if abs(time.time() - int(timestamp)) > 60 * 5:
        return False
    
    # Verify signature
    sig_basestring = f"v0:{timestamp}:{request_body}"
    expected_signature = 'v0=' + hmac.new(
        signing_secret.encode(),
        sig_basestring.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(expected_signature, signature)
```

### Teams Bot Authentication

#### Bot Framework Setup

```bash
# Bot Framework configuration
MICROSOFT_APP_ID=your-app-id
MICROSOFT_APP_PASSWORD=your-app-password
BOT_ENDPOINT=https://your-instance.azurewebsites.net/api/messages
```

## 👥 Authorization and Permissions

### User Roles

Konveyor implements role-based access control (RBAC):

| Role | Permissions | Description |
|------|-------------|-------------|
| **Viewer** | Read documents, search, ask questions | Basic user access |
| **Contributor** | Viewer + upload documents | Can add content |
| **Admin** | Contributor + manage users, view analytics | Full access |
| **System** | All permissions | Service accounts |

### Permission Checks

```python
from django.contrib.auth.decorators import permission_required
from rest_framework.permissions import IsAuthenticated

@permission_required('documents.add_document')
def upload_document(request):
    # Only users with document upload permission can access
    pass

class DocumentViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    
    def get_permissions(self):
        if self.action == 'create':
            return [permissions.IsAuthenticated(), 
                   permissions.DjangoModelPermissions()]
        return super().get_permissions()
```

### API Endpoint Permissions

| Endpoint | Required Permission | Role |
|----------|-------------------|------|
| `GET /healthz/` | None | Public |
| `GET /api/azure-openai-status/` | `api.view_status` | Viewer+ |
| `POST /documents/upload/` | `documents.add_document` | Contributor+ |
| `POST /search/api/query/` | `search.query_documents` | Viewer+ |
| `GET /api/bot/feedback/stats/` | `bot.view_feedback` | Admin |

## 🔒 Security Best Practices

### API Key Security

```bash
# ✅ Good: Use environment variables
export KONVEYOR_API_KEY="your-api-key"
curl -H "Authorization: Token $KONVEYOR_API_KEY" ...

# ❌ Bad: Hardcode in scripts
curl -H "Authorization: Token abc123..." ...
```

### Token Storage

```python
# ✅ Good: Use secure storage
import keyring
keyring.set_password("konveyor", "api_key", "your-api-key")
api_key = keyring.get_password("konveyor", "api_key")

# ❌ Bad: Store in plain text files
with open("api_key.txt", "w") as f:
    f.write("your-api-key")
```

### Network Security

```bash
# ✅ Good: Always use HTTPS
curl https://your-instance.azurewebsites.net/api/...

# ❌ Bad: HTTP in production
curl http://your-instance.azurewebsites.net/api/...
```

### Request Validation

```python
# Validate all incoming requests
def validate_request(request):
    # Check content type
    if request.content_type not in ['application/json', 'multipart/form-data']:
        raise ValidationError("Invalid content type")
    
    # Check request size
    if len(request.body) > MAX_REQUEST_SIZE:
        raise ValidationError("Request too large")
    
    # Validate authentication
    if not request.user.is_authenticated:
        raise AuthenticationError("Authentication required")
```

## 🚨 Troubleshooting

### Common Authentication Issues

#### Invalid API Key

**Error:**
```json
{
  "detail": "Invalid token."
}
```

**Solutions:**
1. Verify API key is correct
2. Check if token exists in database
3. Ensure proper header format: `Authorization: Token YOUR_KEY`

#### Expired Azure AD Token

**Error:**
```json
{
  "error": "invalid_token",
  "error_description": "The token has expired"
}
```

**Solutions:**
1. Refresh the access token
2. Check token expiration time
3. Implement automatic token refresh

#### Slack Signature Verification Failed

**Error:**
```json
{
  "error": "Invalid signature"
}
```

**Solutions:**
1. Verify signing secret is correct
2. Check timestamp is within 5 minutes
3. Ensure request body is not modified

### Debug Authentication

#### Enable Debug Logging

```python
# settings/development.py
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'rest_framework.authentication': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

#### Test Authentication

```bash
# Test API key authentication
curl -v -H "Authorization: Token YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/

# Test Azure AD authentication
curl -v -H "Authorization: Bearer YOUR_AZURE_TOKEN" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/
```

#### Validate Token Format

```python
# Check API key format
import re
api_key_pattern = r'^[a-f0-9]{40}$'
if not re.match(api_key_pattern, api_key):
    print("Invalid API key format")

# Check JWT token format
jwt_pattern = r'^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$'
if not re.match(jwt_pattern, jwt_token):
    print("Invalid JWT token format")
```

### Authentication Flow Debugging

```python
# Debug authentication middleware
class AuthDebugMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log authentication headers
        auth_header = request.META.get('HTTP_AUTHORIZATION', 'None')
        print(f"Auth header: {auth_header[:20]}...")
        
        # Log user after authentication
        response = self.get_response(request)
        print(f"Authenticated user: {request.user}")
        
        return response
```

## 📚 Integration Examples

### Python Client with Authentication

```python
import requests
from typing import Optional

class AuthenticatedKonveyorClient:
    def __init__(self, base_url: str, api_key: Optional[str] = None, 
                 azure_token: Optional[str] = None):
        self.base_url = base_url
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({
                'Authorization': f'Token {api_key}'
            })
        elif azure_token:
            self.session.headers.update({
                'Authorization': f'Bearer {azure_token}'
            })
    
    def health_check(self):
        response = self.session.get(f"{self.base_url}/healthz/")
        return response.json()
    
    def search(self, query: str, top: int = 5):
        data = {"query": query, "top": top}
        response = self.session.post(
            f"{self.base_url}/search/api/query/",
            json=data
        )
        response.raise_for_status()
        return response.json()

# Usage
client = AuthenticatedKonveyorClient(
    "https://your-instance.azurewebsites.net",
    api_key="your-api-key"
)
results = client.search("deployment guide")
```

### JavaScript with Token Refresh

```javascript
class KonveyorAuthClient {
  constructor(baseUrl, clientId, clientSecret, tenantId) {
    this.baseUrl = baseUrl;
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.tenantId = tenantId;
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  async getAccessToken() {
    if (this.accessToken && this.tokenExpiry > Date.now()) {
      return this.accessToken;
    }

    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
    const params = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      scope: 'https://graph.microsoft.com/.default',
      grant_type: 'client_credentials'
    });

    const response = await fetch(tokenUrl, {
      method: 'POST',
      body: params
    });

    const data = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000);
    
    return this.accessToken;
  }

  async apiCall(endpoint, options = {}) {
    const token = await this.getAccessToken();
    
    return fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
  }
}
```

---

**Next**: [Rate Limiting Guide](rate-limiting.md) | [Error Codes Reference](error-codes.md)

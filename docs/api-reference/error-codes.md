# ❌ Error Codes Reference

Comprehensive reference for all error codes and status responses in the Konveyor API.

## 📋 Table of Contents

- [Error Response Format](#error-response-format)
- [HTTP Status Codes](#http-status-codes)
- [Application Error Codes](#application-error-codes)
- [Service-Specific Errors](#service-specific-errors)
- [Troubleshooting Guide](#troubleshooting-guide)
- [Error Handling Best Practices](#error-handling-best-practices)

## 📄 Error Response Format

All API errors follow a consistent JSON format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "specific_field",
      "value": "invalid_value",
      "constraint": "validation_rule"
    }
  },
  "metadata": {
    "request_id": "req-123e4567-e89b-12d3-a456-************",
    "timestamp": "2024-01-01T12:00:00Z",
    "endpoint": "/api/search/query/",
    "method": "POST"
  }
}
```

### Response Fields

- **`success`**: Always `false` for error responses
- **`error.code`**: Machine-readable error code
- **`error.message`**: Human-readable error description
- **`error.details`**: Additional context about the error
- **`metadata.request_id`**: Unique identifier for tracking
- **`metadata.timestamp`**: When the error occurred
- **`metadata.endpoint`**: API endpoint that generated the error
- **`metadata.method`**: HTTP method used

## 🌐 HTTP Status Codes

### 2xx Success Codes

| Code | Status | Description |
|------|--------|-------------|
| 200 | OK | Request successful |
| 201 | Created | Resource created successfully |
| 202 | Accepted | Request accepted for processing |
| 204 | No Content | Request successful, no content returned |

### 4xx Client Error Codes

| Code | Status | Description | Common Causes |
|------|--------|-------------|---------------|
| 400 | Bad Request | Invalid request format or parameters | Missing required fields, invalid JSON |
| 401 | Unauthorized | Authentication required or failed | Missing/invalid API key or token |
| 403 | Forbidden | Insufficient permissions | User lacks required permissions |
| 404 | Not Found | Resource not found | Invalid endpoint or resource ID |
| 405 | Method Not Allowed | HTTP method not supported | Using GET on POST-only endpoint |
| 406 | Not Acceptable | Requested format not supported | Invalid Accept header |
| 409 | Conflict | Resource conflict | Duplicate resource creation |
| 410 | Gone | Resource permanently deleted | Accessing deleted document |
| 413 | Payload Too Large | Request body too large | File upload exceeds size limit |
| 415 | Unsupported Media Type | Content type not supported | Invalid file format |
| 422 | Unprocessable Entity | Valid format but semantic errors | Business logic validation failure |
| 429 | Too Many Requests | Rate limit exceeded | Too many API calls |

### 5xx Server Error Codes

| Code | Status | Description | Common Causes |
|------|--------|-------------|---------------|
| 500 | Internal Server Error | Unexpected server error | Application bug, unhandled exception |
| 501 | Not Implemented | Feature not implemented | Endpoint under development |
| 502 | Bad Gateway | Upstream service error | Azure service unavailable |
| 503 | Service Unavailable | Service temporarily unavailable | Maintenance, overload |
| 504 | Gateway Timeout | Upstream service timeout | Azure service slow response |

## 🔧 Application Error Codes

### Authentication Errors (AUTH_*)

#### `AUTH_MISSING_TOKEN`
**HTTP Status:** 401  
**Description:** No authentication token provided

```json
{
  "error": {
    "code": "AUTH_MISSING_TOKEN",
    "message": "Authentication token is required",
    "details": {
      "header": "Authorization",
      "expected_format": "Token YOUR_API_KEY or Bearer YOUR_JWT_TOKEN"
    }
  }
}
```

#### `AUTH_INVALID_TOKEN`
**HTTP Status:** 401  
**Description:** Invalid or malformed authentication token

```json
{
  "error": {
    "code": "AUTH_INVALID_TOKEN",
    "message": "Invalid authentication token",
    "details": {
      "token_type": "api_key",
      "issue": "Token not found in database"
    }
  }
}
```

#### `AUTH_EXPIRED_TOKEN`
**HTTP Status:** 401  
**Description:** Authentication token has expired

```json
{
  "error": {
    "code": "AUTH_EXPIRED_TOKEN",
    "message": "Authentication token has expired",
    "details": {
      "expired_at": "2024-01-01T10:00:00Z",
      "current_time": "2024-01-01T12:00:00Z"
    }
  }
}
```

#### `AUTH_INSUFFICIENT_PERMISSIONS`
**HTTP Status:** 403  
**Description:** User lacks required permissions

```json
{
  "error": {
    "code": "AUTH_INSUFFICIENT_PERMISSIONS",
    "message": "Insufficient permissions for this operation",
    "details": {
      "required_permission": "documents.add_document",
      "user_permissions": ["documents.view_document"]
    }
  }
}
```

### Validation Errors (VALIDATION_*)

#### `VALIDATION_REQUIRED_FIELD`
**HTTP Status:** 400  
**Description:** Required field is missing

```json
{
  "error": {
    "code": "VALIDATION_REQUIRED_FIELD",
    "message": "Required field is missing",
    "details": {
      "field": "query",
      "location": "request_body"
    }
  }
}
```

#### `VALIDATION_INVALID_FORMAT`
**HTTP Status:** 400  
**Description:** Field value has invalid format

```json
{
  "error": {
    "code": "VALIDATION_INVALID_FORMAT",
    "message": "Invalid field format",
    "details": {
      "field": "top",
      "value": "invalid",
      "expected_type": "integer",
      "constraints": "1 <= value <= 20"
    }
  }
}
```

#### `VALIDATION_INVALID_FILE_TYPE`
**HTTP Status:** 415  
**Description:** Uploaded file type not supported

```json
{
  "error": {
    "code": "VALIDATION_INVALID_FILE_TYPE",
    "message": "File type not supported",
    "details": {
      "filename": "document.xlsx",
      "detected_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "supported_types": ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/markdown", "text/plain"]
    }
  }
}
```

#### `VALIDATION_FILE_TOO_LARGE`
**HTTP Status:** 413  
**Description:** Uploaded file exceeds size limit

```json
{
  "error": {
    "code": "VALIDATION_FILE_TOO_LARGE",
    "message": "File size exceeds maximum limit",
    "details": {
      "file_size": 15728640,
      "max_size": 10485760,
      "max_size_mb": 10
    }
  }
}
```

### Resource Errors (RESOURCE_*)

#### `RESOURCE_NOT_FOUND`
**HTTP Status:** 404  
**Description:** Requested resource does not exist

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Resource not found",
    "details": {
      "resource_type": "document",
      "resource_id": "123e4567-e89b-12d3-a456-************"
    }
  }
}
```

#### `RESOURCE_ALREADY_EXISTS`
**HTTP Status:** 409  
**Description:** Resource already exists

```json
{
  "error": {
    "code": "RESOURCE_ALREADY_EXISTS",
    "message": "Resource already exists",
    "details": {
      "resource_type": "document",
      "conflicting_field": "filename",
      "existing_id": "456e7890-e89b-12d3-a456-************"
    }
  }
}
```

#### `RESOURCE_PROCESSING`
**HTTP Status:** 202  
**Description:** Resource is being processed

```json
{
  "error": {
    "code": "RESOURCE_PROCESSING",
    "message": "Resource is currently being processed",
    "details": {
      "resource_id": "123e4567-e89b-12d3-a456-************",
      "status": "PROCESSING",
      "estimated_completion": "2024-01-01T12:05:00Z"
    }
  }
}
```

### Rate Limiting Errors (RATE_LIMIT_*)

#### `RATE_LIMIT_EXCEEDED`
**HTTP Status:** 429  
**Description:** API rate limit exceeded

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded",
    "details": {
      "limit": 100,
      "window_seconds": 60,
      "retry_after_seconds": 30,
      "reset_time": "2024-01-01T12:01:00Z"
    }
  }
}
```

## ☁️ Service-Specific Errors

### Azure OpenAI Errors (AZURE_OPENAI_*)

#### `AZURE_OPENAI_UNAVAILABLE`
**HTTP Status:** 502  
**Description:** Azure OpenAI service is unavailable

```json
{
  "error": {
    "code": "AZURE_OPENAI_UNAVAILABLE",
    "message": "Azure OpenAI service is temporarily unavailable",
    "details": {
      "service": "Azure OpenAI",
      "endpoint": "https://your-openai.openai.azure.com/",
      "retry_after_seconds": 60
    }
  }
}
```

#### `AZURE_OPENAI_QUOTA_EXCEEDED`
**HTTP Status:** 429  
**Description:** Azure OpenAI quota exceeded

```json
{
  "error": {
    "code": "AZURE_OPENAI_QUOTA_EXCEEDED",
    "message": "Azure OpenAI quota exceeded",
    "details": {
      "quota_type": "tokens_per_minute",
      "limit": 10000,
      "reset_time": "2024-01-01T12:01:00Z"
    }
  }
}
```

#### `AZURE_OPENAI_MODEL_NOT_FOUND`
**HTTP Status:** 404  
**Description:** Requested model deployment not found

```json
{
  "error": {
    "code": "AZURE_OPENAI_MODEL_NOT_FOUND",
    "message": "Model deployment not found",
    "details": {
      "model_name": "gpt-4",
      "available_models": ["gpt-35-turbo", "text-embedding-ada-002"]
    }
  }
}
```

### Azure Cognitive Search Errors (AZURE_SEARCH_*)

#### `AZURE_SEARCH_UNAVAILABLE`
**HTTP Status:** 502  
**Description:** Azure Cognitive Search service unavailable

```json
{
  "error": {
    "code": "AZURE_SEARCH_UNAVAILABLE",
    "message": "Azure Cognitive Search service is unavailable",
    "details": {
      "service": "Azure Cognitive Search",
      "endpoint": "https://your-search.search.windows.net",
      "last_successful_connection": "2024-01-01T11:30:00Z"
    }
  }
}
```

#### `AZURE_SEARCH_INDEX_NOT_FOUND`
**HTTP Status:** 404  
**Description:** Search index does not exist

```json
{
  "error": {
    "code": "AZURE_SEARCH_INDEX_NOT_FOUND",
    "message": "Search index not found",
    "details": {
      "index_name": "konveyor-documents",
      "available_indexes": []
    }
  }
}
```

#### `AZURE_SEARCH_QUERY_ERROR`
**HTTP Status:** 400  
**Description:** Invalid search query

```json
{
  "error": {
    "code": "AZURE_SEARCH_QUERY_ERROR",
    "message": "Invalid search query",
    "details": {
      "query": "invalid syntax",
      "error_details": "Syntax error in search expression"
    }
  }
}
```

### Azure Storage Errors (AZURE_STORAGE_*)

#### `AZURE_STORAGE_UNAVAILABLE`
**HTTP Status:** 502  
**Description:** Azure Storage service unavailable

```json
{
  "error": {
    "code": "AZURE_STORAGE_UNAVAILABLE",
    "message": "Azure Storage service is unavailable",
    "details": {
      "service": "Azure Blob Storage",
      "container": "documents",
      "operation": "upload"
    }
  }
}
```

#### `AZURE_STORAGE_QUOTA_EXCEEDED`
**HTTP Status:** 507  
**Description:** Storage quota exceeded

```json
{
  "error": {
    "code": "AZURE_STORAGE_QUOTA_EXCEEDED",
    "message": "Storage quota exceeded",
    "details": {
      "current_usage_gb": 95,
      "quota_gb": 100,
      "available_gb": 5
    }
  }
}
```

### Slack Integration Errors (SLACK_*)

#### `SLACK_INVALID_SIGNATURE`
**HTTP Status:** 401  
**Description:** Slack request signature verification failed

```json
{
  "error": {
    "code": "SLACK_INVALID_SIGNATURE",
    "message": "Invalid Slack request signature",
    "details": {
      "timestamp_age_seconds": 45,
      "max_age_seconds": 300
    }
  }
}
```

#### `SLACK_WEBHOOK_ERROR`
**HTTP Status:** 400  
**Description:** Invalid Slack webhook payload

```json
{
  "error": {
    "code": "SLACK_WEBHOOK_ERROR",
    "message": "Invalid Slack webhook payload",
    "details": {
      "missing_fields": ["event", "team_id"],
      "event_type": "unknown"
    }
  }
}
```

## 🔍 Troubleshooting Guide

### Common Error Scenarios

#### Authentication Issues

**Symptom:** Getting 401 errors  
**Possible Causes:**
- Missing Authorization header
- Invalid API key format
- Expired token
- Wrong authentication method

**Debug Steps:**
```bash
# Check if API key exists
curl -v -H "Authorization: Token YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/

# Verify token format (should be 40 hex characters)
echo "YOUR_API_KEY" | grep -E '^[a-f0-9]{40}$'
```

#### File Upload Issues

**Symptom:** Getting 413 or 415 errors  
**Possible Causes:**
- File too large
- Unsupported file type
- Corrupted file

**Debug Steps:**
```bash
# Check file size
ls -lh your-file.pdf

# Check file type
file your-file.pdf

# Test with small text file
echo "test content" > test.txt
curl -X POST -F "file=@test.txt" \
  https://your-instance.azurewebsites.net/documents/upload/
```

#### Search Not Working

**Symptom:** Getting empty search results  
**Possible Causes:**
- No documents indexed
- Search index not created
- Azure Search service down

**Debug Steps:**
```bash
# Check if documents exist
curl https://your-instance.azurewebsites.net/documents/

# Test simple search
curl -X POST -H "Content-Type: application/json" \
  -d '{"query": "*", "top": 1}' \
  https://your-instance.azurewebsites.net/search/simple/
```

### Error Logging

#### Enable Debug Logging

```python
# settings/development.py
LOGGING = {
    'version': 1,
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'debug.log',
        },
    },
    'loggers': {
        'konveyor': {
            'handlers': ['file'],
            'level': 'DEBUG',
        },
    },
}
```

#### Log Analysis

```bash
# Search for specific error codes
grep "AUTH_INVALID_TOKEN" logs/konveyor.log

# Find rate limiting issues
grep "RATE_LIMIT_EXCEEDED" logs/konveyor.log

# Check Azure service errors
grep "AZURE_.*_UNAVAILABLE" logs/konveyor.log
```

## 🛠️ Error Handling Best Practices

### Client-Side Error Handling

#### Python Example

```python
import requests
from typing import Dict, Any

class KonveyorAPIError(Exception):
    def __init__(self, error_code: str, message: str, details: Dict[str, Any] = None):
        self.error_code = error_code
        self.message = message
        self.details = details or {}
        super().__init__(f"{error_code}: {message}")

class KonveyorClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key

    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        if response.status_code == 200:
            return response.json()
        
        try:
            error_data = response.json()
            if not error_data.get('success', True):
                error = error_data.get('error', {})
                raise KonveyorAPIError(
                    error_code=error.get('code', 'UNKNOWN_ERROR'),
                    message=error.get('message', 'Unknown error occurred'),
                    details=error.get('details', {})
                )
        except ValueError:
            # Response is not JSON
            pass
        
        # Fallback for non-JSON errors
        response.raise_for_status()
        return response.json()

    def search(self, query: str, top: int = 5) -> Dict[str, Any]:
        try:
            response = requests.post(
                f"{self.base_url}/search/api/query/",
                json={"query": query, "top": top},
                headers={"Authorization": f"Token {self.api_key}"}
            )
            return self._handle_response(response)
        except KonveyorAPIError as e:
            if e.error_code == "RATE_LIMIT_EXCEEDED":
                retry_after = e.details.get('retry_after_seconds', 60)
                print(f"Rate limited. Retry after {retry_after} seconds")
                raise
            elif e.error_code == "AUTH_INVALID_TOKEN":
                print("API key is invalid. Please check your credentials")
                raise
            else:
                print(f"API error: {e}")
                raise
```

#### JavaScript Example

```javascript
class KonveyorAPIError extends Error {
  constructor(errorCode, message, details = {}) {
    super(`${errorCode}: ${message}`);
    this.errorCode = errorCode;
    this.details = details;
  }
}

class KonveyorClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async handleResponse(response) {
    if (response.ok) {
      return response.json();
    }

    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (!errorData.success) {
      const error = errorData.error || {};
      throw new KonveyorAPIError(
        error.code || 'UNKNOWN_ERROR',
        error.message || 'Unknown error occurred',
        error.details || {}
      );
    }

    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  async search(query, top = 5) {
    try {
      const response = await fetch(`${this.baseUrl}/search/api/query/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${this.apiKey}`
        },
        body: JSON.stringify({ query, top })
      });

      return await this.handleResponse(response);
    } catch (error) {
      if (error instanceof KonveyorAPIError) {
        switch (error.errorCode) {
          case 'RATE_LIMIT_EXCEEDED':
            const retryAfter = error.details.retry_after_seconds || 60;
            console.log(`Rate limited. Retry after ${retryAfter} seconds`);
            break;
          case 'AUTH_INVALID_TOKEN':
            console.log('API key is invalid. Please check your credentials');
            break;
          default:
            console.log(`API error: ${error.message}`);
        }
      }
      throw error;
    }
  }
}
```

### Retry Logic

```python
import time
import random
from typing import Callable, Any

def retry_with_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True
) -> Any:
    """Retry function with exponential backoff."""
    
    for attempt in range(max_retries + 1):
        try:
            return func()
        except KonveyorAPIError as e:
            if e.error_code in ['RATE_LIMIT_EXCEEDED', 'AZURE_OPENAI_UNAVAILABLE']:
                if attempt == max_retries:
                    raise
                
                # Calculate delay
                delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                if jitter:
                    delay *= (0.5 + random.random() * 0.5)
                
                # Use retry_after if provided
                if e.error_code == 'RATE_LIMIT_EXCEEDED':
                    retry_after = e.details.get('retry_after_seconds')
                    if retry_after:
                        delay = max(delay, retry_after)
                
                print(f"Retrying in {delay:.1f} seconds (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)
            else:
                # Don't retry for non-retryable errors
                raise
        except Exception as e:
            # Don't retry for unexpected errors
            raise

# Usage
def search_with_retry(client, query):
    return retry_with_backoff(lambda: client.search(query))
```

---

**Next**: [Rate Limiting Guide](rate-limiting.md) | [API Examples](examples.md)

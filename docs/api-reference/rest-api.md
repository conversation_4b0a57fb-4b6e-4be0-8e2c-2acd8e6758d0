# 🔌 REST API Reference

Complete reference for all Konveyor REST API endpoints.

## 📋 Table of Contents

- [Authentication](#authentication)
- [Core Endpoints](#core-endpoints)
- [Document Management](#document-management)
- [Search API](#search-api)
- [Bot Integration](#bot-integration)
- [Feedback API](#feedback-api)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## 🔐 Authentication

### API Key Authentication

Include your API key in the Authorization header:

```bash
Authorization: Bearer YOUR_API_KEY
```

### Azure AD Authentication

For Azure AD integration:

```bash
Authorization: Bearer YOUR_AZURE_AD_TOKEN
```

### No Authentication Required

These endpoints don't require authentication:
- `GET /healthz/` - Health check
- `GET /` - Application index

## 🏠 Core Endpoints

### Health Check

#### `GET /healthz/`

Check the health status of the Konveyor service and its dependencies.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "database": "ok",
    "azure_openai": "ok",
    "azure_search": "ok",
    "azure_storage": "ok"
  },
  "version": "0.2.0"
}
```

**Status Codes:**
- `200` - Service is healthy
- `503` - Service is unhealthy

**Example:**
```bash
curl https://your-instance.azurewebsites.net/healthz/
```

### Azure OpenAI Status

#### `GET /api/azure-openai-status/`

Get the status of Azure OpenAI integration.

**Response:**
```json
{
  "status": "ok",
  "integration": "Azure OpenAI",
  "configured": true,
  "message": "Azure OpenAI service is operational",
  "models": {
    "chat": "gpt-35-turbo",
    "embedding": "text-embedding-ada-002"
  }
}
```

**Example:**
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/azure-openai-status/
```

## 📄 Document Management

### Document Index

#### `GET /documents/`

Get the document management interface.

**Response:**
```html
<!-- HTML page for document management -->
```

**Example:**
```bash
curl https://your-instance.azurewebsites.net/documents/
```

### Upload Document

#### `POST /documents/upload/`

Upload a document for processing and indexing.

**Request:**
- **Content-Type:** `multipart/form-data`
- **Body Parameters:**
  - `file` (required): Document file
  - `title` (optional): Document title
  - `category` (optional): Document category
  - `tags` (optional): Comma-separated tags

**Supported File Types:**
- PDF (`.pdf`)
- Microsoft Word (`.docx`)
- Markdown (`.md`)
- Plain Text (`.txt`)

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "title": "User Manual",
  "filename": "manual.pdf",
  "status": "success",
  "size": 1024000,
  "content_type": "application/pdf",
  "uploaded_at": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200` - Document uploaded successfully
- `400` - Invalid file or parameters
- `413` - File too large
- `415` - Unsupported file type

**Example:**
```bash
curl -X POST https://your-instance.azurewebsites.net/documents/upload/ \
  -F "file=@document.pdf" \
  -F "title=User Manual" \
  -F "category=documentation"
```

## 🔍 Search API

### Semantic Search

#### `POST /search/api/query/`

Perform semantic search across indexed documents.

**Request:**
```json
{
  "query": "How do I deploy to production?",
  "top": 5,
  "filters": {
    "category": "deployment",
    "date_range": "last_30_days"
  }
}
```

**Parameters:**
- `query` (required): Search query string
- `top` (optional): Number of results to return (default: 5, max: 20)
- `filters` (optional): Search filters

**Response:**
```json
{
  "query_id": "123e4567-e89b-12d3-a456-************",
  "results": [
    {
      "id": "chunk-456",
      "document_id": "doc-123",
      "title": "Deployment Guide",
      "content": "To deploy to production, follow these steps...",
      "score": 0.95,
      "metadata": {
        "category": "deployment",
        "last_updated": "2024-01-01T00:00:00Z"
      },
      "highlights": [
        "deploy to <em>production</em>",
        "follow these <em>steps</em>"
      ]
    }
  ],
  "total_results": 15,
  "processing_time_ms": 45
}
```

**Status Codes:**
- `200` - Search completed successfully
- `400` - Invalid query parameters
- `500` - Search service error

**Example:**
```bash
curl -X POST https://your-instance.azurewebsites.net/search/api/query/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "query": "database configuration",
    "top": 10
  }'
```

### Simple Search

#### `GET /search/simple/`

Simple search interface with query parameters.

**Parameters:**
- `q` (required): Search query
- `top` (optional): Number of results (default: 5)

**Example:**
```bash
curl "https://your-instance.azurewebsites.net/search/simple/?q=deployment&top=5"
```

#### `POST /search/simple/`

Simple search with JSON body.

**Request:**
```json
{
  "query": "authentication setup",
  "top": 5
}
```

**Example:**
```bash
curl -X POST https://your-instance.azurewebsites.net/search/simple/ \
  -H "Content-Type: application/json" \
  -d '{"query": "authentication setup", "top": 5}'
```

### Index Document

#### `POST /search/api/index-document/`

Index a specific document in the search service.

**Request:**
```json
{
  "document_id": "123e4567-e89b-12d3-a456-************"
}
```

**Response:**
```json
{
  "status": "Document indexed successfully",
  "details": {
    "document_id": "123e4567-e89b-12d3-a456-************",
    "chunks_indexed": 15,
    "processing_time_ms": 2500
  }
}
```

**Example:**
```bash
curl -X POST https://your-instance.azurewebsites.net/search/api/index-document/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{"document_id": "123e4567-e89b-12d3-a456-************"}'
```

### Reindex All Documents

#### `POST /search/api/reindex-all/`

Reindex all documents in the database.

**Request:**
```json
{}
```

**Response:**
```json
{
  "status": "All documents indexed successfully",
  "results": {
    "total_documents": 50,
    "successful": 48,
    "failed": 2,
    "processing_time_ms": 30000,
    "failed_documents": [
      {
        "document_id": "doc-123",
        "error": "Document not found in storage"
      }
    ]
  }
}
```

**Example:**
```bash
curl -X POST https://your-instance.azurewebsites.net/search/api/reindex-all/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{}'
```

## 🤖 Bot Integration

### Slack Events Webhook

#### `POST /api/bot/slack/events/`

Webhook endpoint for Slack events.

**Headers:**
- `X-Slack-Signature`: Slack signature for verification
- `X-Slack-Request-Timestamp`: Request timestamp

**Request:**
```json
{
  "type": "event_callback",
  "event": {
    "type": "message",
    "channel": "C1234567890",
    "user": "U1234567890",
    "text": "How do I get started?",
    "ts": "1234567890.123456"
  }
}
```

**Response:**
```json
{
  "status": "ok"
}
```

### Slack Slash Commands

#### `POST /api/bot/slack/commands/`

Handle Slack slash commands.

**Request:**
```
token=verification_token&
team_id=T1234567890&
team_domain=example&
channel_id=C1234567890&
channel_name=general&
user_id=U1234567890&
user_name=john&
command=/konveyor&
text=How do I deploy?&
response_url=https://hooks.slack.com/commands/1234/5678
```

**Response:**
```json
{
  "response_type": "in_channel",
  "text": "Here's how to deploy...",
  "attachments": [
    {
      "color": "good",
      "fields": [
        {
          "title": "Source",
          "value": "Deployment Guide",
          "short": true
        }
      ]
    }
  ]
}
```

### Bot Debug

#### `GET /api/bot/debug/`

Debug endpoint for bot troubleshooting.

**Response:**
```json
{
  "status": "debug",
  "message": "Debug view - request logged",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 📊 Feedback API

### Feedback Statistics

#### `GET /api/bot/feedback/stats/`

Get feedback statistics.

**Parameters:**
- `days` (optional): Number of days to include (default: 30)

**Response:**
```json
{
  "total_feedback": 150,
  "positive_feedback": 120,
  "negative_feedback": 30,
  "average_rating": 4.2,
  "feedback_by_day": [
    {
      "date": "2024-01-01",
      "positive": 5,
      "negative": 1
    }
  ]
}
```

**Example:**
```bash
curl "https://your-instance.azurewebsites.net/api/bot/feedback/stats/?days=7" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Feedback by Skill

#### `GET /api/bot/feedback/by-skill/`

Get feedback statistics grouped by skill.

**Parameters:**
- `days` (optional): Number of days to include (default: 30)

**Response:**
```json
{
  "skill_feedback": [
    {
      "skill_name": "DocumentationNavigator",
      "total_feedback": 50,
      "positive_feedback": 45,
      "negative_feedback": 5,
      "average_rating": 4.5
    },
    {
      "skill_name": "CodeUnderstanding",
      "total_feedback": 30,
      "positive_feedback": 25,
      "negative_feedback": 5,
      "average_rating": 4.0
    }
  ]
}
```

### Export Feedback

#### `GET /api/bot/feedback/export/`

Export feedback data.

**Parameters:**
- `format` (optional): Export format (`json`, `csv`) (default: `json`)
- `days` (optional): Number of days to include (default: 30)

**Response:**
```json
{
  "export_url": "https://storage.example.com/exports/feedback-2024-01-01.json",
  "expires_at": "2024-01-02T12:00:00Z",
  "record_count": 150
}
```

## ❌ Error Handling

### Standard Error Response

All API endpoints return errors in this format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "query",
      "issue": "Required field is missing"
    }
  },
  "metadata": {
    "request_id": "req-123",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `AUTHENTICATION_ERROR` | 401 | Invalid or missing authentication |
| `AUTHORIZATION_ERROR` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `FILE_TOO_LARGE` | 413 | Uploaded file exceeds size limit |
| `UNSUPPORTED_FILE_TYPE` | 415 | File type not supported |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `AZURE_SERVICE_ERROR` | 502 | Azure service unavailable |
| `INTERNAL_ERROR` | 500 | Internal server error |

### Common Error Scenarios

#### File Upload Errors

```json
{
  "success": false,
  "error": {
    "code": "UNSUPPORTED_FILE_TYPE",
    "message": "File type not supported",
    "details": {
      "supported_types": [".pdf", ".docx", ".md", ".txt"],
      "received_type": ".xlsx"
    }
  }
}
```

#### Search Errors

```json
{
  "success": false,
  "error": {
    "code": "AZURE_SERVICE_ERROR",
    "message": "Search service temporarily unavailable",
    "details": {
      "service": "Azure Cognitive Search",
      "retry_after": 30
    }
  }
}
```

## 🚦 Rate Limiting

### Rate Limit Headers

All responses include rate limiting information:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
X-RateLimit-Window: 3600
```

### Rate Limits by Endpoint

| Endpoint Category | Limit | Window |
|------------------|-------|--------|
| Health checks | 100/minute | 60 seconds |
| Document upload | 10/minute | 60 seconds |
| Search queries | 100/minute | 60 seconds |
| Bot interactions | 50/minute | 60 seconds |
| Feedback API | 20/minute | 60 seconds |

### Rate Limit Exceeded Response

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded",
    "details": {
      "limit": 100,
      "window": 60,
      "retry_after": 30
    }
  }
}
```

## 📚 SDK Examples

### Python

```python
import requests

class KonveyorClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
    
    def upload_document(self, file_path, title=None):
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'title': title} if title else {}
            response = requests.post(
                f"{self.base_url}/documents/upload/",
                files=files,
                data=data,
                headers=self.headers
            )
        return response.json()
    
    def search(self, query, top=5):
        data = {"query": query, "top": top}
        response = requests.post(
            f"{self.base_url}/search/api/query/",
            json=data,
            headers=self.headers
        )
        return response.json()

# Usage
client = KonveyorClient("https://your-instance.azurewebsites.net", "your-api-key")
result = client.search("deployment guide")
```

### JavaScript

```javascript
class KonveyorClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  async uploadDocument(file, title) {
    const formData = new FormData();
    formData.append('file', file);
    if (title) formData.append('title', title);

    const response = await fetch(`${this.baseUrl}/documents/upload/`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': this.headers.Authorization
      }
    });
    return response.json();
  }

  async search(query, top = 5) {
    const response = await fetch(`${this.baseUrl}/search/api/query/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({ query, top })
    });
    return response.json();
  }
}

// Usage
const client = new KonveyorClient('https://your-instance.azurewebsites.net', 'your-api-key');
const results = await client.search('deployment guide');
```

---

**Next**: [Authentication Guide](authentication.md) | [Error Codes Reference](error-codes.md)

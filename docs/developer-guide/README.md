# 🛠️ Developer Guide

Welcome to the Konveyor Developer Guide! This guide is for developers who want to contribute to, extend, or deploy Konveyor.

## 🎯 Who This Guide Is For

- **Contributors** wanting to improve Konveyor
- **DevOps Engineers** deploying and maintaining Konveyor
- **Developers** extending Konveyor with custom skills
- **System Administrators** managing Konveyor infrastructure

## 📚 Guide Structure

### 🏗️ Architecture and Design
- **[Architecture Overview](architecture.md)** - System design and components
- **[Directory Structure](directory-structure/)** - Complete project organization guide
- **[RAG Implementation](rag-implementation.md)** - Detailed RAG system documentation
- **[Data Flow](data-flow.md)** - How information flows through the system
- **[Security Model](security.md)** - Security considerations and implementation

### 🚀 Development
- **[Development Setup](development-setup.md)** - Complete development environment setup
- **[Semantic Kernel Setup](semantic-kernel-setup.md)** - AI skills and Semantic Kernel configuration
- **[Contributing](contributing.md)** - Contribution guidelines and workflow
- **[Testing](testing.md)** - Comprehensive testing guide and best practices
- **[GitHub Workflows](github-workflows.md)** - CI/CD pipeline documentation
- **[Code Style](code-style.md)** - Coding standards and conventions

### 🔍 Search & Vector Databases
- **[Search Provider Migration](search-provider-migration.md)** - Migrate between vector database providers
- **[Search Provider Implementation](search-provider-implementation.md)** - Add new vector database providers

### 🔧 Customization and Extension
- **[Custom Skills](custom-skills.md)** - Creating new AI skills
- **[API Extensions](api-extensions.md)** - Extending the REST API
- **[Integration Patterns](integration-patterns.md)** - Common integration approaches

### 🚀 Deployment and Operations
- **[Deployment Guide](deployment.md)** - Production deployment strategies
- **[Monitoring](monitoring.md)** - Observability and monitoring setup
- **[Scaling](scaling.md)** - Scaling Konveyor for larger teams

### 📋 Planning and Workflows
- **[Feedback Analysis Workflow](feedback-analysis-workflow.md)** - Process for analyzing user feedback
- **[Future Improvements](future-improvements.md)** - Planned enhancements and roadmap

## 🏗️ Architecture Overview

Konveyor follows a modular, microservices-inspired architecture built on Django:

```mermaid
graph TD
    subgraph "User Interfaces"
        UI[Web Interface]
        Slack[Slack Bot]
        API[REST API]
    end

    subgraph "Application Layer"
        Bot[Bot App]
        RAG[RAG App]
        Search[Search App]
        Docs[Documents App]
    end

    subgraph "Core Services"
        Agent[Agent Orchestrator]
        Skills[Semantic Kernel Skills]
        Memory[Memory System]
    end

    subgraph "Azure Services"
        OpenAI[Azure OpenAI]
        CogSearch[Cognitive Search]
        Storage[Blob Storage]
        KeyVault[Key Vault]
    end

    UI --> API
    Slack --> Bot
    API --> RAG
    API --> Search
    API --> Docs
    Bot --> Agent
    RAG --> Agent
    Agent --> Skills
    Skills --> Memory
    Skills --> OpenAI
    Search --> CogSearch
    Docs --> Storage
    Memory --> Storage
```

### Key Components

- **Apps Layer**: Django applications providing specific functionality
- **Core Layer**: Shared services and business logic
- **Skills Layer**: Semantic Kernel skills for AI capabilities
- **Azure Integration**: Cloud services for AI, search, and storage

## 🚀 Quick Start for Developers

### 1. Set Up Development Environment

```bash
# Clone the repository
git clone https://github.com/sdamache/konveyor.git
cd konveyor

# Set up development environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements/development.txt

# Set up pre-commit hooks
pre-commit install

# Copy environment template
cp .env.example .env.dev
```

### 2. Configure for Development

Edit `.env.dev` with development settings:

```bash
# Development settings
DEBUG=True
ENVIRONMENT=development

# Use local SQLite for development
DATABASE_URL=sqlite:///db.sqlite3

# Azure services (use development instances)
AZURE_OPENAI_ENDPOINT=https://your-dev-openai.openai.azure.com/
AZURE_SEARCH_ENDPOINT=https://your-dev-search.search.windows.net
```

### 3. Run Development Server

```bash
# Run migrations
python manage.py migrate

# Start development server
python manage.py runserver

# In another terminal, run tests
python manage.py test
```

## 🧪 Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
python manage.py test
python manage.py lint

# Commit with conventional commits
git commit -m "feat: add new documentation skill"
```

### 2. Testing

```bash
# Run all tests
python manage.py test

# Run specific test suite
python manage.py test konveyor.apps.documents

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

### 3. Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
pylint konveyor/

# Type checking
mypy konveyor/
```

## 🔧 Common Development Tasks

### Adding a New Skill

1. **Create skill class**:
```python
# konveyor/skills/my_new_skill.py
from semantic_kernel.functions import kernel_function

class MyNewSkill:
    @kernel_function(
        description="Description of what this skill does",
        name="my_function"
    )
    def my_function(self, input: str) -> str:
        # Implementation here
        return f"Processed: {input}"
```

2. **Register the skill**:
```python
# In your app's ready() method or management command
from konveyor.core.agent import SkillRegistry
from .skills.my_new_skill import MyNewSkill

registry = SkillRegistry()
registry.register_skill(
    MyNewSkill(),
    name="my_new_skill",
    description="A skill that does something useful"
)
```

### Adding a New API Endpoint

1. **Create view**:
```python
# konveyor/apps/api/views.py
from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['POST'])
def my_new_endpoint(request):
    # Implementation here
    return Response({"result": "success"})
```

2. **Add URL pattern**:
```python
# konveyor/apps/api/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('my-endpoint/', views.my_new_endpoint, name='my_endpoint'),
]
```

### Extending the Document Processing

1. **Create custom processor**:
```python
# konveyor/core/documents/processors.py
class MyDocumentProcessor:
    def process(self, document):
        # Custom processing logic
        return processed_document
```

2. **Register processor**:
```python
# In settings or app config
DOCUMENT_PROCESSORS = [
    'konveyor.core.documents.processors.MyDocumentProcessor',
]
```

## 📊 Monitoring and Debugging

### Development Logging

```python
# settings/development.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'konveyor': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

### Debug Tools

```bash
# Django debug toolbar (already included in dev requirements)
# Access at /__debug__/ when DEBUG=True

# Django shell for testing
python manage.py shell

# Database shell
python manage.py dbshell
```

### Performance Profiling

```python
# Use Django's profiling middleware
MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    # ... other middleware
]

# Or use cProfile for specific functions
import cProfile
cProfile.run('your_function()')
```

## 🔒 Security Considerations

### Development Security

- **Never commit secrets** to version control
- **Use environment variables** for configuration
- **Validate all inputs** in API endpoints
- **Follow Django security best practices**

### Azure Security

- **Use managed identities** when possible
- **Rotate keys regularly**
- **Implement least privilege access**
- **Monitor access logs**

## 📦 Packaging and Distribution

### Building Docker Images

```bash
# Build development image
docker build -t konveyor:dev .

# Build production image
docker build -f Dockerfile.prod -t konveyor:prod .
```

### Creating Releases

```bash
# Tag release
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# Build and push Docker image
docker build -t konveyor:v1.0.0 .
docker push your-registry/konveyor:v1.0.0
```

## 🎯 Next Steps

Choose your path based on what you want to do:

- **Start Contributing**: Read [Contributing Guide](contributing.md)
- **Understand Architecture**: See [Architecture Overview](architecture.md)
- **Set Up Development**: Follow [Development Setup](development-setup.md)
- **Deploy to Production**: Check [Deployment Guide](deployment.md)
- **Create Custom Skills**: Learn [Custom Skills](custom-skills.md)

## 📚 Additional Resources

### Documentation
- [Django Documentation](https://docs.djangoproject.com/)
- [Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)
- [Azure OpenAI Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/)

### Tools and Libraries
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Azure SDK for Python](https://docs.microsoft.com/en-us/azure/developer/python/)
- [Bot Framework SDK](https://docs.microsoft.com/en-us/azure/bot-service/)

## 🆘 Getting Help

### Development Issues
- Check [Troubleshooting](../user-guide/troubleshooting.md)
- Review [Testing Guide](testing.md)
- Look at existing code examples

### Contributing Questions
- Read [Contributing Guidelines](contributing.md)
- Check open issues on GitHub
- Join development discussions

---

**Ready to start developing?** 👉 [Set up your development environment](development-setup.md)

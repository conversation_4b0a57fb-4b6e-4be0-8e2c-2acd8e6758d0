# 🏗️ System Architecture

This document provides a comprehensive overview of Konveyor's architecture, including system design, component interactions, and technical decisions.

## 📋 Table of Contents

- [Overview](#overview)
- [High-Level Architecture](#high-level-architecture)
- [Component Details](#component-details)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Design Principles](#design-principles)
- [Security Architecture](#security-architecture)
- [Scalability Considerations](#scalability-considerations)

## 🎯 Overview

Konveyor is an AI-powered knowledge transfer agent built on a modern, cloud-native architecture. The system follows a layered approach with clear separation of concerns:

- **Presentation Layer**: Web UI, Slack Bot, REST API
- **Application Layer**: Django apps for specific domains
- **Business Logic Layer**: Core services and AI orchestration
- **Data Layer**: Azure cloud services for storage and AI
- **Infrastructure Layer**: Terraform-managed Azure resources

### Key Architectural Goals

- **Modularity**: Clear separation between components
- **Scalability**: Horizontal scaling capabilities
- **Maintainability**: Clean code and well-defined interfaces
- **Extensibility**: Easy to add new skills and integrations
- **Reliability**: Fault tolerance and graceful degradation
- **Security**: Defense in depth with Azure security services

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "User Interfaces"
        UI[Web Interface]
        Slack[Slack Bot]
        API[REST API]
    end

    subgraph "Application Layer (Django Apps)"
        BotApp[Bot App]
        RAGApp[RAG App]
        SearchApp[Search App]
        DocsApp[Documents App]
        CoreApp[Core App]
    end

    subgraph "Business Logic Layer"
        Agent[Agent Orchestrator]
        Skills[Semantic Kernel Skills]
        Memory[Memory System]
    end

    subgraph "Core Services"
        DocService[Document Service]
        SearchService[Unified Search Service]
        RAGService[RAG Service]
        BotService[Bot Service]
    end

    subgraph "Search Abstraction Layer"
        SearchFactory[Search Provider Factory]
        VectorInterface[Vector Search Interface]
        EmbeddingInterface[Embedding Interface]
    end

    subgraph "Search Providers"
        AzureProvider[Azure AI Search Provider]
        PineconeProvider[Pinecone Provider]
        LangChainAdapter[LangChain Adapter]
    end

    subgraph "Vector Databases"
        CogSearch[Azure AI Search]
        Pinecone[Pinecone Serverless]
        Qdrant[Qdrant]
        FAISS[FAISS]
    end

    subgraph "Azure Services"
        OpenAI[Azure OpenAI]
        BlobStorage[Blob Storage]
        KeyVault[Key Vault]
        PostgreSQL[PostgreSQL]
        AppService[App Service]
    end

    UI --> API
    Slack --> BotApp
    API --> RAGApp
    API --> SearchApp
    API --> DocsApp
    BotApp --> Agent
    RAGApp --> Agent
    Agent --> Skills
    Skills --> Memory
    Skills --> DocService
    Skills --> SearchService
    Skills --> RAGService
    DocService --> BlobStorage
    SearchService --> SearchFactory
    SearchFactory --> VectorInterface
    SearchFactory --> EmbeddingInterface
    VectorInterface --> AzureProvider
    VectorInterface --> PineconeProvider
    VectorInterface --> LangChainAdapter
    AzureProvider --> CogSearch
    PineconeProvider --> Pinecone
    LangChainAdapter --> Qdrant
    LangChainAdapter --> FAISS
    EmbeddingInterface --> OpenAI
    RAGService --> OpenAI
    Memory --> BlobStorage
    BotService --> KeyVault

    subgraph "Infrastructure"
        Terraform[Terraform IaC]
        Monitoring[Azure Monitor]
        Security[Azure Security]
    end

    Terraform -.-> Azure Services
    Monitoring -.-> Azure Services
    Security -.-> Azure Services
```

## 🧩 Component Details

### Application Layer (Django Apps)

#### 📄 Documents App (`konveyor/apps/documents/`)

Handles document lifecycle management:

**Responsibilities**:

- Document upload and validation
- File parsing (PDF, DOCX, Markdown, text)
- Content chunking and preprocessing
- Metadata extraction and storage
- Integration with Azure Blob Storage

**Key Components**:

```python
# Models
class Document(models.Model):
    title = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500)
    content_type = models.CharField(max_length=100)
    upload_date = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)

class DocumentChunk(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    content = models.TextField()
    chunk_index = models.IntegerField()
    embedding_vector = models.JSONField(null=True)
```

**API Endpoints**:

- `POST /documents/upload/` - Upload new document
- `GET /documents/` - List documents
- `GET /documents/{id}/` - Get document details
- `DELETE /documents/{id}/` - Delete document

#### 🔍 Search App (`konveyor/apps/search/`)

Manages semantic and hybrid search capabilities with provider abstraction:

**Responsibilities**:

- Search index management across multiple providers
- Query processing and optimization
- Result ranking and filtering
- Provider-agnostic search operations
- Migration support between vector databases

**Key Features**:

- **Multi-Provider Support**: Azure AI Search, Pinecone, Qdrant, FAISS
- **Runtime Provider Switching**: Via environment variables
- **Unified Search Interface**: Consistent API across all providers
- **Vector similarity search**: Semantic search with embeddings
- **Hybrid search**: Combined vector + keyword search
- **Backward Compatibility**: Existing code continues to work
- **Migration Tools**: Seamless data migration between providers

**Provider Configuration**:
```python
# Environment-based provider selection
SEARCH_PROVIDER=pinecone  # or azure, qdrant, etc.
EMBEDDING_PROVIDER=azure_openai  # or openai

# Factory creates appropriate provider
search_service = get_search_service()
```

#### 🧠 RAG App (`konveyor/apps/rag/`)

Orchestrates Retrieval-Augmented Generation:

**Responsibilities**:

- Query understanding and preprocessing
- Context retrieval from multiple sources
- Prompt engineering and optimization
- Response generation and post-processing

**Workflow**:

1. Analyze user query
2. Retrieve relevant context
3. Construct optimized prompt
4. Generate response via Azure OpenAI
5. Post-process and format response

#### 🤖 Bot App (`konveyor/apps/bot/`)

Handles conversational interfaces:

**Responsibilities**:

- Slack Bot Framework integration
- Message routing and processing
- Conversation state management
- Multi-platform support (extensible)

### Business Logic Layer

#### 🎭 Agent Orchestrator (`konveyor/core/agent/`)

Central coordination system for AI capabilities:

```python
class AgentOrchestratorSkill:
    def __init__(self, kernel, skill_registry):
        self.kernel = kernel
        self.skill_registry = skill_registry
        self.conversation_memory = ConversationMemory()

    async def process_request(self, user_input: str) -> str:
        # 1. Analyze intent
        intent = await self.analyze_intent(user_input)

        # 2. Route to appropriate skill
        skill = self.skill_registry.get_skill_for_intent(intent)

        # 3. Execute skill with context
        response = await skill.execute(user_input, self.get_context())

        # 4. Update memory
        self.conversation_memory.add_interaction(user_input, response)

        return response
```

#### 🛠️ Semantic Kernel Skills

Specialized AI capabilities:

**Documentation Navigator Skill**:

- Searches documentation with semantic understanding
- Provides contextual answers with citations
- Suggests related topics

**Code Understanding Skill**:

- Analyzes code snippets and repositories
- Explains functionality and architecture
- Identifies patterns and best practices

**Knowledge Analyzer Skill**:

- Detects knowledge gaps in conversations
- Suggests learning paths
- Tracks user progress

### Core Services Layer

#### 📚 Document Service (`konveyor/core/documents/`)

Core document processing logic:

```python
class DocumentService:
    def __init__(self, storage_client, search_client):
        self.storage = storage_client
        self.search = search_client
        self.processors = self._load_processors()

    async def process_document(self, document_path: str) -> ProcessedDocument:
        # 1. Load and parse document
        content = await self.load_document(document_path)

        # 2. Extract metadata
        metadata = await self.extract_metadata(content)

        # 3. Chunk content
        chunks = await self.chunk_content(content)

        # 4. Generate embeddings
        embeddings = await self.generate_embeddings(chunks)

        # 5. Store in search index
        await self.index_document(chunks, embeddings, metadata)

        return ProcessedDocument(chunks, metadata, embeddings)
```

#### 🔍 Search Service (`konveyor/core/search/`)

**New Architecture: Provider-Agnostic Search Abstraction Layer**

The search service has been redesigned with a provider abstraction layer that supports multiple vector database backends:

```python
# Unified Search Service with Provider Abstraction
class UnifiedSearchService:
    def __init__(self, vector_search: VectorSearchInterface, embedding_service: EmbeddingInterface):
        self.vector_search = vector_search
        self.embedding_service = embedding_service

    async def semantic_search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        # 1. Generate query embedding
        embedding = await self.embedding_service.generate_embedding(query)

        # 2. Create standardized search query
        search_query = SearchQuery(
            text=query,
            search_type=SearchType.VECTOR,
            top_k=top_k,
            embedding=embedding
        )

        # 3. Perform search using current provider
        return await self.vector_search.search(search_query)

# Provider Factory for Runtime Switching
search_service = get_search_service(
    vector_provider="pinecone",  # or "azure", "qdrant", etc.
    embedding_provider="azure_openai"
)
```

**Supported Providers:**
- **Azure AI Search**: Current production provider
- **Pinecone Serverless**: Primary migration target (70% cost reduction)
- **LangChain Adapter**: Support for Qdrant, FAISS, Chroma, etc.

**Key Features:**
- Runtime provider switching via environment variables
- Backward compatibility with existing SearchService API
- Standardized data structures across all providers
- Async-first design with comprehensive error handling

## 🔄 Data Flow

### Document Ingestion Flow

```mermaid
sequenceDiagram
    participant User
    participant API
    participant DocsApp
    participant DocService
    participant BlobStorage
    participant SearchService
    participant CogSearch

    User->>API: Upload Document
    API->>DocsApp: Process Upload
    DocsApp->>DocService: Parse Document
    DocService->>DocService: Extract Content & Metadata
    DocService->>DocService: Chunk Content
    DocService->>BlobStorage: Store Original File
    DocService->>SearchService: Index Chunks
    SearchService->>CogSearch: Create Search Index
    SearchService-->>DocsApp: Index Complete
    DocsApp-->>API: Upload Success
    API-->>User: Success Response
```

### Query Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant Bot/API
    participant Agent
    participant Skills
    participant SearchService
    participant RAGService
    participant OpenAI

    User->>Bot/API: Ask Question
    Bot/API->>Agent: Process Query
    Agent->>Skills: Route to Skill
    Skills->>SearchService: Search Documents
    SearchService-->>Skills: Return Results
    Skills->>RAGService: Generate Response
    RAGService->>OpenAI: Call LLM
    OpenAI-->>RAGService: Generated Response
    RAGService-->>Skills: Formatted Response
    Skills-->>Agent: Final Response
    Agent-->>Bot/API: Response with Citations
    Bot/API-->>User: Answer
```

## 🛠️ Technology Stack

### Backend Framework

- **Django 4.2+**: Web framework and ORM
- **Django REST Framework**: API development
- **Celery**: Asynchronous task processing
- **Redis**: Caching and message broker

### AI and ML

- **Azure OpenAI**: Large language models
- **Semantic Kernel**: AI orchestration framework
- **Azure Cognitive Search**: Vector search and indexing
- **Transformers**: Text processing and embeddings

### Storage and Data

- **PostgreSQL**: Primary database
- **Azure Blob Storage**: File storage
- **Azure Key Vault**: Secrets management
- **Redis**: Caching and sessions

### Infrastructure

- **Azure App Service**: Application hosting
- **Azure Container Registry**: Container images
- **Terraform**: Infrastructure as Code
- **GitHub Actions**: CI/CD pipeline

### Monitoring and Observability

- **Azure Application Insights**: Application monitoring
- **Azure Monitor**: Infrastructure monitoring
- **Sentry**: Error tracking
- **Prometheus/Grafana**: Custom metrics (optional)

## 🎯 Design Principles

### 1. Separation of Concerns

- **Apps**: Handle HTTP requests and responses
- **Core Services**: Implement business logic
- **Skills**: Provide AI capabilities
- **Azure Services**: Handle infrastructure concerns

### 2. Dependency Injection

```python
# Services are injected, not instantiated
class DocumentView:
    def __init__(self, document_service: DocumentService):
        self.document_service = document_service
```

### 3. Configuration-Driven

```python
# All configuration via environment variables
class AzureConfig:
    OPENAI_ENDPOINT = os.getenv('AZURE_OPENAI_ENDPOINT')
    SEARCH_ENDPOINT = os.getenv('AZURE_SEARCH_ENDPOINT')
```

### 4. Async-First

```python
# All I/O operations are async
async def process_document(self, document: Document) -> ProcessedDocument:
    content = await self.storage.read(document.path)
    chunks = await self.chunker.chunk(content)
    return ProcessedDocument(chunks)
```

### 5. Error Handling and Resilience

```python
# Graceful degradation and retry logic
@retry(max_attempts=3, backoff=exponential_backoff)
async def call_openai(self, prompt: str) -> str:
    try:
        return await self.openai_client.complete(prompt)
    except RateLimitError:
        await asyncio.sleep(60)  # Wait and retry
        raise
    except Exception as e:
        logger.error(f"OpenAI call failed: {e}")
        return "I'm sorry, I'm having trouble processing your request right now."
```

## 🔒 Security Architecture

### Authentication and Authorization

- **Azure Active Directory**: Identity provider
- **JWT Tokens**: API authentication
- **Role-Based Access Control**: Permission management

### Data Protection

- **Encryption at Rest**: Azure Storage encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: Azure Key Vault for secrets

### Network Security

- **Virtual Network**: Isolated network environment
- **Private Endpoints**: Secure service connections
- **Web Application Firewall**: Protection against attacks

### Application Security

- **Input Validation**: All user inputs validated
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Content Security Policy headers

## 📈 Scalability Considerations

### Horizontal Scaling

- **Stateless Application**: Easy to scale horizontally
- **Load Balancing**: Azure Load Balancer for traffic distribution
- **Auto-scaling**: Based on CPU and memory metrics

### Database Scaling

- **Read Replicas**: For read-heavy workloads
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Indexed queries and caching

### Caching Strategy

- **Redis Cache**: For frequently accessed data
- **CDN**: For static assets
- **Application-Level Caching**: For expensive computations

### Performance Optimization

- **Async Processing**: Non-blocking I/O operations
- **Background Tasks**: Celery for heavy processing
- **Database Optimization**: Proper indexing and query optimization

## 🔮 Future Architecture Considerations

### Microservices Migration

- **Service Decomposition**: Split into smaller services
- **API Gateway**: Centralized API management
- **Service Mesh**: Inter-service communication

### Event-Driven Architecture

- **Event Sourcing**: For audit trails and replay capability
- **Message Queues**: For decoupled communication
- **CQRS**: Separate read and write models

### Multi-Cloud Strategy

- **Cloud Agnostic**: Reduce vendor lock-in
- **Disaster Recovery**: Cross-cloud backup
- **Cost Optimization**: Best pricing across providers

---

**Next**: [Data Flow Documentation](data-flow.md) | [Security Model](security.md)

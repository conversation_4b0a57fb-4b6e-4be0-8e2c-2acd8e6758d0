# 🤝 Contributing Guide

Welcome to the Konveyor project! This guide will help you contribute effectively to the codebase.

## 📋 Table of Contents

- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Standards](#code-standards)
- [Pull Request Process](#pull-request-process)
- [Testing Guidelines](#testing-guidelines)
- [Documentation Standards](#documentation-standards)
- [Community Guidelines](#community-guidelines)

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:

- **Python 3.10+** installed
- **Git** configured with your GitHub account
- **Azure account** for testing (optional but recommended)
- **Docker** for containerized development (optional)

### Development Setup

1. **Fork the Repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/konveyor.git
   cd konveyor
   ```

2. **Set Up Development Environment**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install development dependencies
   pip install -r requirements/development.txt
   
   # Install pre-commit hooks
   pre-commit install
   ```

3. **Configure Environment**
   ```bash
   # Copy example environment file
   cp .env.example .env.dev
   
   # Edit .env.dev with your configuration
   # See docs/getting-started/installation.md for details
   ```

4. **Verify Setup**
   ```bash
   # Run tests
   python manage.py test
   
   # Start development server
   python manage.py runserver
   
   # Check health endpoint
   curl http://localhost:8000/healthz/
   ```

## 🔄 Development Workflow

### Branch Strategy

We use a **task-based branching strategy** with these branch types:

- **`main`**: Production-ready code
- **`dev`**: Integration branch for features
- **`feat/task-<id>-<description>`**: New features and enhancements
- **`fix/task-<id>-<description>`**: Bug fixes
- **`docs/task-<id>-<description>`**: Documentation updates

### Creating a Feature Branch

```bash
# Start from dev branch
git checkout dev
git pull origin dev

# Create feature branch (required format)
git checkout -b feat/task-123-your-feature-description

# Make your changes
# ... code, test, commit ...

# Push to your fork
git push origin feat/task-123-your-feature-description
```

**⚠️ Important**: Branch naming is enforced by CI. All feature branches must follow the pattern `feat/task-<id>-<description>` or the PR will fail.

### Commit Message Format

We follow **Conventional Commits** specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

#### Examples

```bash
# Feature commit
git commit -m "feat(search): add hybrid search capability"

# Bug fix commit
git commit -m "fix(documents): handle empty file uploads gracefully"

# Documentation commit
git commit -m "docs(api): add authentication examples"

# Breaking change
git commit -m "feat(api)!: change search response format

BREAKING CHANGE: search results now return 'items' instead of 'results'"
```

## 📏 Code Standards

### Python Code Style

We use **Black** for code formatting and **isort** for import sorting:

```bash
# Format code
black .

# Sort imports
isort .

# Check formatting
black --check .
isort --check-only .
```

### Code Quality Tools

#### Linting with flake8

```bash
# Run linting
flake8 konveyor/

# Configuration in setup.cfg
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = migrations, venv, .git
```

#### Type Checking with mypy

```bash
# Run type checking
mypy konveyor/

# Configuration in mypy.ini
[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
```

### Code Structure Guidelines

#### File Organization

```python
# Standard import order
import os
import sys
from typing import Dict, List, Optional

# Third-party imports
import django
from rest_framework import serializers

# Local imports
from konveyor.core.services import DocumentService
from .models import Document
```

#### Class Structure

```python
class DocumentService:
    """Service for handling document operations.
    
    This service provides methods for:
    - Document parsing and processing
    - Storage management
    - Search indexing
    
    Attributes:
        storage_client: Azure storage client
        search_client: Azure search client
    """
    
    def __init__(self, storage_client=None, search_client=None):
        """Initialize the document service.
        
        Args:
            storage_client: Optional storage client
            search_client: Optional search client
        """
        self.storage_client = storage_client or self._get_storage_client()
        self.search_client = search_client or self._get_search_client()
    
    def process_document(self, file_obj: BinaryIO, filename: str) -> Document:
        """Process a document file.
        
        Args:
            file_obj: File object to process
            filename: Original filename
            
        Returns:
            Processed document instance
            
        Raises:
            ValidationError: If document format is invalid
            StorageError: If storage operation fails
        """
        # Implementation here
        pass
    
    def _get_storage_client(self):
        """Get Azure storage client."""
        # Private method implementation
        pass
```

#### Function Documentation

```python
def search_documents(
    query: str, 
    top: int = 5, 
    filters: Optional[Dict[str, Any]] = None
) -> List[SearchResult]:
    """Search documents using semantic search.
    
    Performs semantic search across indexed documents using Azure Cognitive Search.
    Results are ranked by relevance score and filtered based on provided criteria.
    
    Args:
        query: Search query string
        top: Maximum number of results to return (1-20)
        filters: Optional search filters
            - category: Document category filter
            - date_range: Date range filter ('last_7_days', 'last_30_days')
            - author: Author filter
    
    Returns:
        List of search results ordered by relevance score
        
    Raises:
        ValueError: If query is empty or top is out of range
        SearchServiceError: If search service is unavailable
        
    Example:
        >>> results = search_documents("deployment guide", top=10)
        >>> for result in results:
        ...     print(f"{result.title}: {result.score}")
    """
    if not query.strip():
        raise ValueError("Query cannot be empty")
    
    if not 1 <= top <= 20:
        raise ValueError("Top must be between 1 and 20")
    
    # Implementation here
    pass
```

### Error Handling

#### Custom Exceptions

```python
# konveyor/core/exceptions.py
class KonveyorError(Exception):
    """Base exception for Konveyor errors."""
    pass

class DocumentProcessingError(KonveyorError):
    """Raised when document processing fails."""
    pass

class SearchServiceError(KonveyorError):
    """Raised when search service operations fail."""
    pass

class AzureServiceError(KonveyorError):
    """Raised when Azure service operations fail."""
    
    def __init__(self, message: str, service: str, status_code: Optional[int] = None):
        super().__init__(message)
        self.service = service
        self.status_code = status_code
```

#### Error Handling Patterns

```python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def process_document_safely(file_obj: BinaryIO) -> Optional[Document]:
    """Process document with comprehensive error handling."""
    try:
        # Validate input
        if not file_obj:
            raise ValueError("File object is required")
        
        # Process document
        document = self._parse_document(file_obj)
        
        # Log success
        logger.info(f"Successfully processed document: {document.id}")
        return document
        
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise DocumentProcessingError(f"Invalid input: {e}")
        
    except AzureServiceError as e:
        logger.error(f"Azure service error: {e}")
        # Don't re-raise, return None to indicate failure
        return None
        
    except Exception as e:
        logger.exception(f"Unexpected error processing document: {e}")
        raise DocumentProcessingError(f"Processing failed: {e}")
```

### Logging Standards

```python
import logging

# Configure logger
logger = logging.getLogger(__name__)

class DocumentService:
    def process_document(self, file_obj: BinaryIO) -> Document:
        """Process document with proper logging."""
        logger.info(f"Starting document processing: {file_obj.name}")
        
        try:
            # Processing logic
            result = self._do_processing(file_obj)
            
            logger.info(
                f"Document processed successfully",
                extra={
                    'document_id': result.id,
                    'file_size': file_obj.size,
                    'processing_time_ms': 1500
                }
            )
            return result
            
        except Exception as e:
            logger.error(
                f"Document processing failed: {e}",
                extra={
                    'file_name': file_obj.name,
                    'file_size': file_obj.size,
                    'error_type': type(e).__name__
                },
                exc_info=True
            )
            raise
```

## 🔀 Pull Request Process

### Before Creating a PR

1. **Ensure your branch is up to date**
   ```bash
   git checkout dev
   git pull origin dev
   git checkout feat/task-123-your-feature
   git rebase dev
   ```

2. **Run all checks**
   ```bash
   # Format code
   black .
   isort .
   
   # Run linting
   flake8 konveyor/
   
   # Run type checking
   mypy konveyor/
   
   # Run tests
   python manage.py test
   
   # Check test coverage
   coverage run --source='.' manage.py test
   coverage report --show-missing
   ```

3. **Update documentation**
   - Update relevant documentation files
   - Add docstrings to new functions/classes
   - Update API documentation if needed

### Creating the Pull Request

1. **Push your branch**
   ```bash
   git push origin feat/task-123-your-feature
   ```

2. **Create PR on GitHub**
   - Use the PR template
   - Provide clear title and description
   - Link related issues
   - Add appropriate labels

### PR Template

```markdown
## Description
Brief description of the changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Related Issues
Fixes #(issue number)

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All tests pass

## Documentation
- [ ] Code comments added/updated
- [ ] API documentation updated
- [ ] User documentation updated

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is properly commented
- [ ] Tests added for new functionality
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

### Review Process

1. **Automated Checks**
   - CI/CD pipeline runs automatically
   - Code quality checks must pass
   - Test coverage must meet minimum threshold

2. **Code Review**
   - At least one reviewer required
   - Address all review comments
   - Maintain respectful discussion

3. **Approval and Merge**
   - All checks must pass
   - Required approvals obtained
   - Squash and merge to dev

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── unit/                   # Unit tests
│   ├── test_document_service.py
│   ├── test_search_service.py
│   └── test_models.py
├── integration/            # Integration tests
│   ├── test_api_endpoints.py
│   ├── test_azure_services.py
│   └── test_slack_integration.py
├── fixtures/               # Test data
│   ├── sample_documents/
│   └── test_data.json
└── conftest.py            # Pytest configuration
```

### Writing Unit Tests

```python
import pytest
from unittest.mock import Mock, patch
from django.test import TestCase

from konveyor.core.services import DocumentService
from konveyor.core.exceptions import DocumentProcessingError

class TestDocumentService(TestCase):
    """Test cases for DocumentService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_storage = Mock()
        self.mock_search = Mock()
        self.service = DocumentService(
            storage_client=self.mock_storage,
            search_client=self.mock_search
        )
    
    def test_process_document_success(self):
        """Test successful document processing."""
        # Arrange
        mock_file = Mock()
        mock_file.name = "test.pdf"
        mock_file.size = 1024
        
        # Act
        result = self.service.process_document(mock_file, "test.pdf")
        
        # Assert
        self.assertIsNotNone(result)
        self.assertEqual(result.filename, "test.pdf")
        self.mock_storage.upload.assert_called_once()
    
    def test_process_document_invalid_file(self):
        """Test processing with invalid file."""
        # Arrange
        invalid_file = None
        
        # Act & Assert
        with self.assertRaises(DocumentProcessingError):
            self.service.process_document(invalid_file, "test.pdf")
    
    @patch('konveyor.core.services.document_service.logger')
    def test_process_document_logs_error(self, mock_logger):
        """Test that errors are properly logged."""
        # Arrange
        mock_file = Mock()
        self.mock_storage.upload.side_effect = Exception("Storage error")
        
        # Act
        with self.assertRaises(DocumentProcessingError):
            self.service.process_document(mock_file, "test.pdf")
        
        # Assert
        mock_logger.error.assert_called()
```

### Integration Tests

```python
import pytest
from django.test import TransactionTestCase
from rest_framework.test import APIClient

class TestDocumentAPI(TransactionTestCase):
    """Integration tests for document API."""
    
    def setUp(self):
        """Set up test client and data."""
        self.client = APIClient()
        self.upload_url = '/documents/upload/'
    
    def test_upload_document_end_to_end(self):
        """Test complete document upload workflow."""
        # Arrange
        with open('tests/fixtures/sample.pdf', 'rb') as f:
            file_data = {'file': f, 'title': 'Test Document'}
        
        # Act
        response = self.client.post(self.upload_url, file_data, format='multipart')
        
        # Assert
        self.assertEqual(response.status_code, 200)
        self.assertIn('id', response.json())
        
        # Verify document was created
        doc_id = response.json()['id']
        self.assertTrue(Document.objects.filter(id=doc_id).exists())
```

### Test Configuration

```python
# conftest.py
import pytest
from django.conf import settings

@pytest.fixture
def api_client():
    """Provide API client for tests."""
    from rest_framework.test import APIClient
    return APIClient()

@pytest.fixture
def sample_document():
    """Provide sample document for tests."""
    return {
        'title': 'Test Document',
        'content': 'This is test content',
        'category': 'test'
    }

@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Allow database access for all tests."""
    pass

# pytest.ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = konveyor.settings.test
python_files = tests.py test_*.py *_tests.py
addopts = --tb=short --strict-markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

## 📚 Documentation Standards

### Code Documentation

- **All public functions/classes must have docstrings**
- **Use Google-style docstrings**
- **Include type hints for all parameters and return values**
- **Provide usage examples for complex functions**

### API Documentation

- **Update OpenAPI schema for new endpoints**
- **Include request/response examples**
- **Document error codes and responses**
- **Provide SDK examples**

### User Documentation

- **Update user guides for new features**
- **Include screenshots for UI changes**
- **Provide step-by-step tutorials**
- **Update troubleshooting guides**

## 🤝 Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment:

- **Be respectful** in all interactions
- **Be constructive** in feedback and criticism
- **Be collaborative** and help others learn
- **Be patient** with newcomers and questions

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code review and discussion
- **Documentation**: Inline comments and guides

### Getting Help

- **Check existing documentation** first
- **Search GitHub issues** for similar problems
- **Provide detailed information** when asking for help
- **Be patient** while waiting for responses

### Recognition

We recognize contributors through:

- **Contributor list** in README
- **Release notes** acknowledgments
- **GitHub contributor graphs**
- **Special recognition** for significant contributions

## 🎯 Next Steps

After reading this guide:

1. **Set up your development environment**
2. **Find a good first issue** labeled "good first issue"
3. **Join the community** discussions
4. **Start contributing** with documentation or small fixes
5. **Gradually take on** larger features

Thank you for contributing to Konveyor! 🚀

---

**Need help?** Check our [Development Setup Guide](development-setup.md) or ask in [GitHub Discussions](https://github.com/sdamache/konveyor/discussions).

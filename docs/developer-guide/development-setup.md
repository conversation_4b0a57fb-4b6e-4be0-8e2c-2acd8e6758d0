# 🛠️ Development Environment Setup

Complete guide to setting up your local development environment for Konveyor.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Detailed Setup](#detailed-setup)
- [Development Tools](#development-tools)
- [Environment Configuration](#environment-configuration)
- [Docker Development](#docker-development)
- [IDE Setup](#ide-setup)
- [Troubleshooting](#troubleshooting)

## 🎯 Prerequisites

### System Requirements

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Python**: 3.10 or higher
- **Node.js**: 18+ (for frontend tools, optional)
- **Git**: Latest version
- **Docker**: Latest version (optional but recommended)

### Required Accounts

- **Azure Account**: With permissions to create resources
- **GitHub Account**: For code contributions
- **Slack Workspace**: For testing Slack integration (optional)

### Development Tools

- **Code Editor**: VS Code (recommended) or your preferred editor
- **Terminal**: Command line interface
- **Azure CLI**: For Azure service management
- **Terraform**: For infrastructure management (optional)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/sdamache/konveyor.git
cd konveyor

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements/development.txt

# Install pre-commit hooks
pre-commit install
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# See Environment Configuration section below
```

### 3. Database Setup

```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 4. Start Development Server

```bash
# Start the server
python manage.py runserver

# Verify setup
curl http://localhost:8000/healthz/
```

## 🔧 Detailed Setup

### Python Environment Setup

#### Using Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # macOS/Linux
# or
venv\Scripts\activate     # Windows

# Upgrade pip
pip install --upgrade pip

# Install development dependencies
pip install -r requirements/development.txt
```

#### Using Conda (Alternative)

```bash
# Create conda environment
conda create -n konveyor python=3.10
conda activate konveyor

# Install dependencies
pip install -r requirements/development.txt
```

#### Using Poetry (Alternative)

```bash
# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Activate environment
poetry shell
```

### Git Configuration

```bash
# Configure Git (if not already done)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Install pre-commit hooks
pre-commit install

# Test pre-commit hooks
pre-commit run --all-files
```

### Azure CLI Setup

```bash
# Install Azure CLI
# Windows (using winget)
winget install Microsoft.AzureCLI

# macOS
brew install azure-cli

# Linux
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Login to Azure
az login

# Set default subscription (optional)
az account set --subscription "your-subscription-id"
```

## ⚙️ Environment Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Copy the example file
cp .env.example .env
```

### Required Configuration

Edit `.env` with your specific values:

```bash
# Django Configuration
DEBUG=True
DJANGO_SETTINGS_MODULE=konveyor.settings.development
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DATABASE_URL=sqlite:///db.sqlite3

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002
AZURE_OPENAI_API_VERSION=2024-12-01-preview

# Azure Cognitive Search
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-api-key
AZURE_SEARCH_INDEX_NAME=konveyor-documents

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=...

# Azure Key Vault (optional)
AZURE_KEY_VAULT_URL=https://your-keyvault.vault.azure.net/

# Slack Integration (optional)
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_APP_TOKEN=xapp-your-app-token
SLACK_SIGNING_SECRET=your-signing-secret
SLACK_CLIENT_ID=your-client-id
SLACK_CLIENT_SECRET=your-client-secret

# Development Settings
NGROK_URL=https://your-ngrok-url.ngrok-free.app
```

### Generating Secret Key

```bash
# Generate a secure secret key
python -c "
import secrets
print('SECRET_KEY=' + secrets.token_urlsafe(50))
"
```

### Environment Validation

```bash
# Test environment configuration
python manage.py check

# Test Azure connections
python -c "
from konveyor.core.services.azure_client_manager import AzureClientManager
manager = AzureClientManager()
print('Testing Azure connections...')
try:
    openai_client = manager.get_openai_client()
    print('✅ Azure OpenAI: Connected')
except Exception as e:
    print(f'❌ Azure OpenAI: {e}')

try:
    search_client = manager.get_search_client()
    print('✅ Azure Search: Connected')
except Exception as e:
    print(f'❌ Azure Search: {e}')
"
```

## 🐳 Docker Development

### Using Docker Compose (Recommended)

```bash
# Start development environment
docker-compose up konveyor-dev

# Start in background
docker-compose up -d konveyor-dev

# View logs
docker-compose logs -f konveyor-dev

# Stop services
docker-compose down
```

### Docker Compose Configuration

The `docker-compose.yml` includes:

```yaml
services:
  konveyor-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DJANGO_SETTINGS_MODULE=konveyor.settings.development
      - PYTHONUNBUFFERED=1
      - DEBUG=True
    command: python manage.py runserver 0.0.0.0:8000
```

### Building Custom Docker Image

```bash
# Build development image
docker build -t konveyor:dev .

# Run development container
docker run -p 8000:8000 -v $(pwd):/app konveyor:dev

# Run with environment file
docker run --env-file .env -p 8000:8000 konveyor:dev
```

### Dev Container (VS Code)

For VS Code users, the project includes a dev container configuration:

```bash
# Open in VS Code
code .

# Use Command Palette: "Dev Containers: Reopen in Container"
# Or click the notification to reopen in container
```

The dev container includes:
- Python 3.10+
- Azure CLI
- Terraform
- VS Code extensions
- Pre-configured environment

## 🎨 IDE Setup

### VS Code Configuration

#### Recommended Extensions

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "ms-azuretools.vscode-azureresourcegroups",
    "ms-vscode.azurecli",
    "hashicorp.terraform",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.test-adapter-converter"
  ]
}
```

#### VS Code Settings

Create `.vscode/settings.json`:

```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": [
    "tests"
  ],
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    ".pytest_cache": true,
    ".coverage": true,
    "htmlcov": true
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

#### Launch Configuration

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Django: Debug Server",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": ["runserver", "8000"],
      "django": true,
      "justMyCode": false,
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Django: Test",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": ["test"],
      "django": true,
      "justMyCode": false,
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
```

### PyCharm Configuration

#### Project Setup

1. **Open Project**: File → Open → Select konveyor directory
2. **Configure Interpreter**: 
   - File → Settings → Project → Python Interpreter
   - Add → Existing environment → Select `venv/bin/python`
3. **Django Configuration**:
   - File → Settings → Languages & Frameworks → Django
   - Enable Django support
   - Django project root: project root
   - Settings: `konveyor/settings/development.py`
   - Manage script: `manage.py`

#### Run Configurations

Create run configurations for:
- **Django Server**: `manage.py runserver`
- **Django Tests**: `manage.py test`
- **Pytest**: `pytest tests/`

## 🔧 Development Tools

### Code Quality Tools

#### Black (Code Formatting)

```bash
# Format all Python files
black .

# Check formatting without making changes
black --check .

# Format specific file
black konveyor/apps/documents/models.py
```

#### isort (Import Sorting)

```bash
# Sort imports in all files
isort .

# Check import sorting
isort --check-only .

# Sort imports in specific file
isort konveyor/apps/documents/models.py
```

#### flake8 (Linting)

```bash
# Lint all Python files
flake8 konveyor/

# Lint specific file
flake8 konveyor/apps/documents/models.py

# Configuration in setup.cfg
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = migrations, venv, .git
```

#### mypy (Type Checking)

```bash
# Type check all files
mypy konveyor/

# Type check specific file
mypy konveyor/apps/documents/models.py

# Configuration in mypy.ini
[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files

# Update hooks
pre-commit autoupdate
```

Pre-commit configuration (`.pre-commit-config.yaml`):

```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
```

### Testing Tools

```bash
# Run all tests
python manage.py test

# Run with pytest
pytest tests/

# Run with coverage
coverage run --source='.' manage.py test
coverage report
coverage html

# Run specific test
python manage.py test konveyor.apps.documents.tests.test_models
```

### Database Tools

```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Database shell
python manage.py dbshell

# Django shell
python manage.py shell

# Django shell with IPython
python manage.py shell_plus
```

## 🌐 Local Development Workflow

### Daily Development Routine

```bash
# 1. Start development session
cd konveyor
source venv/bin/activate  # or conda activate konveyor

# 2. Pull latest changes
git pull origin develop

# 3. Install any new dependencies
pip install -r requirements/development.txt

# 4. Apply any new migrations
python manage.py migrate

# 5. Start development server
python manage.py runserver

# 6. In another terminal, run tests
python manage.py test
```

### Feature Development Workflow

```bash
# 1. Create feature branch
git checkout -b feature/your-feature-name

# 2. Make changes
# ... edit code ...

# 3. Run tests
python manage.py test

# 4. Run code quality checks
black .
isort .
flake8 konveyor/
mypy konveyor/

# 5. Commit changes
git add .
git commit -m "feat: add new feature"

# 6. Push to GitHub
git push origin feature/your-feature-name
```

### Debugging

#### Django Debug Toolbar

```bash
# Install debug toolbar (included in development.txt)
pip install django-debug-toolbar

# Enable in settings/development.py (already configured)
```

#### Python Debugger

```python
# Add breakpoint in code
import pdb; pdb.set_trace()

# Or use built-in breakpoint() (Python 3.7+)
breakpoint()
```

#### VS Code Debugging

1. Set breakpoints in VS Code
2. Use "Django: Debug Server" launch configuration
3. Start debugging with F5

### Hot Reloading

The Django development server automatically reloads when you make changes to Python files. For other file types:

```bash
# Watch for changes in templates and static files
python manage.py runserver --settings=konveyor.settings.development
```

## 🔧 Advanced Configuration

### Multiple Environments

```bash
# Development environment
export DJANGO_SETTINGS_MODULE=konveyor.settings.development
python manage.py runserver

# Test environment
export DJANGO_SETTINGS_MODULE=konveyor.settings.test
python manage.py test

# Production-like environment
export DJANGO_SETTINGS_MODULE=konveyor.settings.production
python manage.py runserver
```

### Custom Management Commands

```bash
# Create custom management command
python manage.py startapp myapp
mkdir -p myapp/management/commands
touch myapp/management/__init__.py
touch myapp/management/commands/__init__.py

# Create command file
cat > myapp/management/commands/my_command.py << 'EOF'
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'My custom command'
    
    def handle(self, *args, **options):
        self.stdout.write('Hello, World!')
EOF

# Run custom command
python manage.py my_command
```

### Database Backends

#### PostgreSQL (Production-like)

```bash
# Install PostgreSQL
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS
brew install postgresql

# Start PostgreSQL service
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS

# Create database
sudo -u postgres createdb konveyor_dev

# Update .env
DATABASE_URL=postgresql://postgres:password@localhost:5432/konveyor_dev
```

#### Redis (Caching)

```bash
# Install Redis
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# Start Redis
sudo systemctl start redis  # Linux
brew services start redis   # macOS

# Update settings
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

## 🚨 Troubleshooting

### Common Issues

#### Python Version Issues

```bash
# Check Python version
python --version

# If wrong version, use pyenv
curl https://pyenv.run | bash
pyenv install 3.10.12
pyenv local 3.10.12
```

#### Virtual Environment Issues

```bash
# Remove and recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements/development.txt
```

#### Database Issues

```bash
# Reset database
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
```

#### Azure Connection Issues

```bash
# Test Azure CLI
az account show

# Re-login if needed
az login

# Test environment variables
python -c "
import os
print('AZURE_OPENAI_ENDPOINT:', os.getenv('AZURE_OPENAI_ENDPOINT'))
print('AZURE_SEARCH_ENDPOINT:', os.getenv('AZURE_SEARCH_ENDPOINT'))
"
```

#### Port Already in Use

```bash
# Find process using port 8000
lsof -i :8000

# Kill process
kill -9 <PID>

# Or use different port
python manage.py runserver 8001
```

#### Permission Issues

```bash
# Fix file permissions
chmod +x manage.py
chmod +x startup.sh

# Fix directory permissions
chmod -R 755 logs/
chmod -R 755 static/
```

### Debug Mode

```bash
# Enable debug logging
export DEBUG=True
export DJANGO_LOG_LEVEL=DEBUG

# Run with verbose output
python manage.py runserver --verbosity=2

# Check logs
tail -f logs/konveyor-dev.log
```

### Performance Issues

```bash
# Profile Django
pip install django-debug-toolbar
# Enable in settings/development.py

# Profile Python code
python -m cProfile manage.py runserver

# Memory profiling
pip install memory-profiler
python -m memory_profiler manage.py runserver
```

## 📚 Additional Resources

### Documentation

- [Django Documentation](https://docs.djangoproject.com/)
- [Azure SDK for Python](https://docs.microsoft.com/en-us/azure/developer/python/)
- [Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)

### Development Tools

- [VS Code Python Extension](https://marketplace.visualstudio.com/items?itemName=ms-python.python)
- [PyCharm](https://www.jetbrains.com/pycharm/)
- [Docker Desktop](https://www.docker.com/products/docker-desktop)

### Learning Resources

- [Django Tutorial](https://docs.djangoproject.com/en/stable/intro/tutorial01/)
- [Python Virtual Environments](https://docs.python.org/3/tutorial/venv.html)
- [Git Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows)

---

**Next**: [Contributing Guidelines](contributing.md) | [Testing Guide](testing.md)

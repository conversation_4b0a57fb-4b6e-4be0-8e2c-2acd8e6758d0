# 🏗️ Project Structure Overview

Welcome to the Konveyor project structure guide! This documentation helps you understand how the codebase is organized and where to find (or add) specific functionality.

## 📋 Table of Contents

- [Quick Navigation](#quick-navigation)
- [High-Level Architecture](#high-level-architecture)
- [Directory Overview](#directory-overview)
- [Module Relationships](#module-relationships)
- [Developer Quick Start](#developer-quick-start)

## 🚀 Quick Navigation

**Looking for something specific?**

| I want to... | Go to... |
|--------------|----------|
| **Understand the overall structure** | [Directory Overview](#directory-overview) below |
| **Learn about Django apps** | [Django Apps Structure](django-apps.md) |
| **Understand core modules** | [Core Modules](core-modules.md) |
| **See Azure integration patterns** | [Azure Integration](azure-integration.md) |
| **Find where to add new features** | [Quick Reference](quick-reference.md) |

## 🏗️ High-Level Architecture

Konveyor follows a **layered architecture** with clear separation of concerns:

```mermaid
graph TB
    subgraph "🌐 Presentation Layer"
        UI[Web Interface]
        Slack[Slack Bot]
        API[REST API]
    end

    subgraph "📱 Application Layer (Django Apps)"
        BotApp[Bot App]
        DocsApp[Documents App]
        SearchApp[Search App]
        RAGApp[RAG App]
        CoreApp[Core App]
    end

    subgraph "🧠 Business Logic Layer (Core)"
        Agent[Agent Orchestrator]
        Skills[Semantic Kernel Skills]
        Memory[Memory System]
        Services[Core Services]
    end

    subgraph "☁️ Azure Services Layer"
        OpenAI[Azure OpenAI]
        Search[Cognitive Search]
        Storage[Blob Storage]
        KeyVault[Key Vault]
    end

    UI --> API
    Slack --> BotApp
    API --> DocsApp
    API --> SearchApp
    API --> RAGApp
    BotApp --> Agent
    DocsApp --> Services
    SearchApp --> Services
    RAGApp --> Services
    Agent --> Skills
    Skills --> Memory
    Services --> OpenAI
    Services --> Search
    Services --> Storage
    Memory --> Storage

    style UI fill:#e1f5fe
    style Slack fill:#e1f5fe
    style API fill:#e1f5fe
    style BotApp fill:#f3e5f5
    style DocsApp fill:#f3e5f5
    style SearchApp fill:#f3e5f5
    style RAGApp fill:#f3e5f5
    style Agent fill:#e8f5e8
    style Skills fill:#e8f5e8
    style Services fill:#e8f5e8
    style OpenAI fill:#fff3e0
    style Search fill:#fff3e0
    style Storage fill:#fff3e0
```

## 📁 Directory Overview

### Root Structure

```
konveyor/
├── 📱 apps/                    # Django applications (UI, API, domain logic)
├── 🧠 core/                    # Framework-agnostic business logic
├── 🎯 skills/                  # Semantic Kernel AI skills
├── ⚙️ settings/                # Django configuration
├── 🌐 static/                  # Static web assets
├── 📄 templates/               # Django templates
├── 🧪 tests/                   # Integration tests
└── 📚 docs/                    # Documentation
```

### Key Principles

**🔄 Separation of Concerns**:
- **Apps**: Django-specific logic, models, views, URLs
- **Core**: Framework-agnostic business logic and services
- **Skills**: AI capabilities and Semantic Kernel integration

**📦 Modularity**:
- Each app handles a specific domain (documents, search, RAG, bot)
- Core modules provide shared functionality
- Skills are independent, reusable AI capabilities

**☁️ Cloud-Native Design**:
- Azure services integration throughout
- Configuration-driven deployment
- Scalable, stateless architecture

## 🔗 Module Relationships

```mermaid
graph LR
    subgraph "Django Apps"
        A1[Documents App]
        A2[Search App]
        A3[RAG App]
        A4[Bot App]
    end

    subgraph "Core Services"
        C1[Azure Utils]
        C2[Document Service]
        C3[Chat Service]
        C4[Agent Orchestrator]
    end

    subgraph "Skills"
        S1[Documentation Navigator]
        S2[Code Understanding]
        S3[Knowledge Analyzer]
    end

    A1 --> C2
    A2 --> C1
    A3 --> C3
    A4 --> C4
    C4 --> S1
    C4 --> S2
    C4 --> S3
    C1 --> C2
    C1 --> C3

    style A1 fill:#f3e5f5
    style A2 fill:#f3e5f5
    style A3 fill:#f3e5f5
    style A4 fill:#f3e5f5
    style C1 fill:#e8f5e8
    style C2 fill:#e8f5e8
    style C3 fill:#e8f5e8
    style C4 fill:#e8f5e8
    style S1 fill:#e1f5fe
    style S2 fill:#e1f5fe
    style S3 fill:#e1f5fe
```

## 🚀 Developer Quick Start

### 🆕 New to the Project?

1. **Start Here**: [Django Apps Structure](django-apps.md) - Understand the main application components
2. **Then Read**: [Core Modules](core-modules.md) - Learn about shared business logic
3. **Finally**: [Quick Reference](quick-reference.md) - Find where to add new features

### 🔍 Looking for Specific Information?

| Topic | Document | Key Sections |
|-------|----------|--------------|
| **Django Models & Views** | [Django Apps](django-apps.md) | Apps overview, models, API endpoints |
| **Azure Integration** | [Azure Integration](azure-integration.md) | Service clients, configuration, patterns |
| **Business Logic** | [Core Modules](core-modules.md) | Services, utilities, shared components |
| **Adding Features** | [Quick Reference](quick-reference.md) | Where to add code, common patterns |

### 🛠️ Common Development Tasks

**Adding a New Feature**:
1. Determine if it's Django-specific → Add to appropriate `apps/` directory
2. If it's business logic → Add to `core/` directory
3. If it's AI functionality → Add to `skills/` directory

**Understanding Data Flow**:
1. Start with [Django Apps](django-apps.md) for API endpoints
2. Follow to [Core Modules](core-modules.md) for business logic
3. Check [Azure Integration](azure-integration.md) for external services

**Debugging Issues**:
1. Check [Quick Reference](quick-reference.md) for file locations
2. Review [Core Modules](core-modules.md) for service interactions
3. Examine [Azure Integration](azure-integration.md) for cloud service issues

## 📚 Detailed Documentation

### 📱 Application Layer
- **[Django Apps Structure](django-apps.md)** - Complete guide to Django applications
  - Documents app (file processing)
  - Search app (semantic search)
  - RAG app (AI conversations)
  - Bot app (Slack/Teams integration)

### 🧠 Business Logic Layer
- **[Core Modules](core-modules.md)** - Framework-agnostic services
  - Azure utilities and clients
  - Document processing services
  - Chat and conversation management
  - Agent orchestration

### ☁️ Integration Layer
- **[Azure Integration](azure-integration.md)** - Cloud services integration
  - Service client patterns
  - Configuration management
  - Authentication and security

### 🎯 Reference Materials
- **[Quick Reference](quick-reference.md)** - Developer cheat sheet
  - File location finder
  - Common patterns
  - Where to add new functionality

## 🔧 Architecture Patterns

### 🏗️ Layered Architecture
- **Presentation** → **Application** → **Business Logic** → **Data/Services**
- Clear boundaries between layers
- Dependency injection for testability

### 🔌 Adapter Pattern
- Django apps adapt core services for web framework
- Azure adapters abstract cloud service clients
- Skills adapt AI capabilities for different use cases

### 🎯 Domain-Driven Design
- Each app represents a business domain
- Core modules contain domain logic
- Clear ubiquitous language throughout

## 🆘 Getting Help

### 📖 Documentation Issues
- **Missing information?** Check if it belongs in one of the detailed guides
- **Outdated content?** Create an issue or submit a PR
- **Need clarification?** Ask in team channels

### 🐛 Code Navigation Issues
- **Can't find a file?** Use [Quick Reference](quick-reference.md)
- **Don't understand relationships?** Review [Core Modules](core-modules.md)
- **Azure integration unclear?** Check [Azure Integration](azure-integration.md)

---

**Ready to dive deeper?** Choose your path:
- 🆕 **New Developer**: Start with [Django Apps Structure](django-apps.md)
- 🔧 **Adding Features**: Jump to [Quick Reference](quick-reference.md)
- 🧠 **Understanding Logic**: Explore [Core Modules](core-modules.md)
- ☁️ **Azure Integration**: Read [Azure Integration](azure-integration.md)

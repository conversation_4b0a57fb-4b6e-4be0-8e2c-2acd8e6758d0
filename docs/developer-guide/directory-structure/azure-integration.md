# ☁️ Azure Integration Structure

This guide explains how Azure services are integrated throughout the Konveyor codebase, including service clients, configuration patterns, and architectural decisions.

## 📋 Table of Contents

- [Overview](#overview)
- [Integration Architecture](#integration-architecture)
- [Azure Service Modules](#azure-service-modules)
- [Configuration Patterns](#configuration-patterns)
- [Development Guidelines](#development-guidelines)

## 🎯 Overview

Konveyor is built as a **cloud-native application** with deep Azure integration. The architecture separates Azure-specific code into dedicated modules while maintaining clean abstractions.

### Azure Services Used

```mermaid
graph TB
    subgraph "Konveyor Application"
        App[Django Apps]
        Core[Core Services]
        Utils[Azure Utils]
    end

    subgraph "Azure Services"
        OpenAI[Azure OpenAI]
        Search[Cognitive Search]
        Storage[Blob Storage]
        KeyVault[Key Vault]
        PostgreSQL[PostgreSQL]
        AppService[App Service]
        Monitor[Azure Monitor]
    end

    App --> Core
    Core --> Utils
    Utils --> OpenAI
    Utils --> Search
    Utils --> Storage
    Utils --> KeyVault
    App --> PostgreSQL
    App --> AppService
    AppService --> Monitor

    style OpenAI fill:#fff3e0
    style Search fill:#fff3e0
    style Storage fill:#fff3e0
    style KeyVault fill:#fff3e0
    style PostgreSQL fill:#fff3e0
    style AppService fill:#fff3e0
    style Monitor fill:#fff3e0
```

## 🏗️ Integration Architecture

### Layered Azure Integration

```
┌─────────────────────────────────────┐
│           Django Apps               │ ← Web layer, HTTP APIs
├─────────────────────────────────────┤
│          Core Services              │ ← Business logic
├─────────────────────────────────────┤
│         Azure Utils                 │ ← Azure abstractions
├─────────────────────────────────────┤
│       Azure SDK Clients            │ ← Direct Azure integration
└─────────────────────────────────────┘
```

### Design Principles

- **🔧 Centralized Configuration**: Single source of truth for Azure settings
- **🏭 Factory Pattern**: Consistent client creation and management
- **🔄 Retry Logic**: Robust handling of transient failures
- **🔒 Security First**: Secure credential management and access patterns
- **📊 Observability**: Comprehensive logging and monitoring

## 🔧 Azure Service Modules

### 🛠️ Azure Utils (`konveyor/core/azure_utils/`)

**Purpose**: Foundation layer for all Azure integrations.

```
azure_utils/
├── __init__.py
├── config.py              # AzureConfig - centralized configuration
├── clients.py             # AzureClientManager - client factory
├── mixins.py              # Common Azure service patterns
├── retry.py               # Retry logic for Azure APIs
└── service.py             # Base AzureService class
```

**Key Components**:

#### 🔧 AzureConfig (`config.py`)
```python
class AzureConfig:
    """Singleton configuration manager for Azure services."""
    
    def __init__(self):
        self.openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self.search_endpoint = os.getenv("AZURE_SEARCH_ENDPOINT")
        # ... other configurations
    
    def validate_required_config(self):
        """Validates all required Azure configurations."""
        required = [
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "AZURE_SEARCH_ENDPOINT",
            "AZURE_SEARCH_API_KEY"
        ]
        # Validation logic...
```

#### 🏭 AzureClientManager (`clients.py`)
```python
class AzureClientManager:
    """Factory for Azure SDK clients."""
    
    def __init__(self, config: AzureConfig):
        self.config = config
        self._clients = {}
    
    def get_openai_client(self) -> AzureOpenAI:
        """Get configured Azure OpenAI client."""
        if 'openai' not in self._clients:
            self._clients['openai'] = AzureOpenAI(
                azure_endpoint=self.config.openai_endpoint,
                api_key=self.config.openai_api_key,
                api_version=self.config.openai_api_version
            )
        return self._clients['openai']
    
    def get_search_client(self) -> SearchClient:
        """Get configured Azure Cognitive Search client."""
        # Implementation...
```

#### 🔄 Retry Logic (`retry.py`)
```python
@azure_retry
def call_azure_api():
    """Decorator provides automatic retry for Azure API calls."""
    # API call implementation
    pass

# Configuration
AZURE_RETRY_CONFIG = {
    'stop': stop_after_attempt(3),
    'wait': wait_exponential(multiplier=1, min=4, max=10),
    'retry': retry_if_exception_type((
        ServiceRequestError,
        HttpResponseError,
        ConnectionError
    ))
}
```

### 🤖 Azure Adapters (`konveyor/core/azure_adapters/`)

**Purpose**: Specific implementations for individual Azure services.

```
azure_adapters/
├── __init__.py
└── openai/
    ├── __init__.py
    ├── client.py          # AzureOpenAIClient implementation
    └── tests/
        └── test_integration.py
```

#### 🧠 OpenAI Adapter (`openai/client.py`)
```python
class AzureOpenAIClient:
    """Specialized client for Azure OpenAI operations."""
    
    def __init__(self, client_manager: AzureClientManager):
        self.client = client_manager.get_openai_client()
        self.config = client_manager.config
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using Azure OpenAI."""
        response = await self.client.embeddings.create(
            model=self.config.embedding_deployment,
            input=text
        )
        return response.data[0].embedding
    
    async def generate_completion(self, messages: List[Dict]) -> str:
        """Generate chat completion using Azure OpenAI."""
        response = await self.client.chat.completions.create(
            model=self.config.chat_deployment,
            messages=messages,
            temperature=0.7
        )
        return response.choices[0].message.content
```

### 💾 Storage Integration

**Azure Blob Storage** for document storage:

```python
class DocumentStorageService(AzureService):
    """Service for storing documents in Azure Blob Storage."""
    
    def __init__(self, client_manager: AzureClientManager):
        super().__init__(client_manager)
        self.blob_client = client_manager.get_blob_client()
    
    def store_document(self, content: bytes, filename: str) -> str:
        """Store document and return blob URL."""
        blob_name = f"documents/{uuid4()}/{filename}"
        
        blob_client = self.blob_client.get_blob_client(
            container="documents",
            blob=blob_name
        )
        
        blob_client.upload_blob(content, overwrite=True)
        return blob_client.url
```

### 🔍 Search Integration

**Azure Cognitive Search** for semantic search:

```python
class SearchIndexService(AzureService):
    """Service for managing Azure Cognitive Search indexes."""
    
    def __init__(self, client_manager: AzureClientManager):
        super().__init__(client_manager)
        self.search_client = client_manager.get_search_client()
        self.index_client = client_manager.get_search_index_client()
    
    def create_index(self, index_name: str):
        """Create search index with vector search capabilities."""
        fields = [
            SimpleField(name="id", type=SearchFieldDataType.String, key=True),
            SearchableField(name="content", type=SearchFieldDataType.String),
            SearchField(
                name="content_vector",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name="default"
            )
        ]
        
        index = SearchIndex(name=index_name, fields=fields)
        self.index_client.create_index(index)
```

## ⚙️ Configuration Patterns

### 🌍 Environment-Based Configuration

```python
# Development
AZURE_OPENAI_ENDPOINT = "https://dev-openai.openai.azure.com/"
AZURE_SEARCH_ENDPOINT = "https://dev-search.search.windows.net"

# Production
AZURE_OPENAI_ENDPOINT = "https://prod-openai.openai.azure.com/"
AZURE_SEARCH_ENDPOINT = "https://prod-search.search.windows.net"
```

### 🔐 Security Configuration

```python
# Key Vault integration (future)
class AzureConfig:
    def __init__(self):
        if self.use_key_vault:
            self.key_vault_client = SecretClient(
                vault_url=self.key_vault_url,
                credential=DefaultAzureCredential()
            )
            self.openai_api_key = self._get_secret("openai-api-key")
        else:
            self.openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
```

### 🔄 Service Registration

```python
# Django app configuration
class CoreConfig(AppConfig):
    def ready(self):
        # Initialize Azure services
        azure_config = AzureConfig()
        azure_config.validate_required_config()
        
        client_manager = AzureClientManager(azure_config)
        
        # Register services
        registry = ServiceRegistry()
        registry.register('azure_config', azure_config)
        registry.register('azure_clients', client_manager)
```

## 🛠️ Development Guidelines

### ✅ Best Practices

**1. Use Centralized Configuration**:
```python
# ✅ Good: Use AzureConfig
config = AzureConfig()
endpoint = config.get_openai_endpoint()

# ❌ Avoid: Direct environment access
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
```

**2. Implement Proper Error Handling**:
```python
# ✅ Good: Specific error handling
try:
    result = await openai_client.generate_completion(messages)
except ServiceRequestError as e:
    logger.error(f"Azure OpenAI service error: {e}")
    raise ServiceUnavailableError("AI service temporarily unavailable")
except HttpResponseError as e:
    logger.error(f"Azure OpenAI HTTP error: {e}")
    raise APIError("Invalid request to AI service")
```

**3. Use Async/Await for I/O Operations**:
```python
# ✅ Good: Async Azure operations
async def process_document(self, content: str):
    embedding = await self.openai_client.generate_embedding(content)
    await self.search_client.upload_documents([{
        'id': doc_id,
        'content': content,
        'content_vector': embedding
    }])

# ❌ Avoid: Blocking operations
def process_document(self, content: str):
    embedding = self.openai_client.generate_embedding(content)  # Blocks
```

**4. Implement Circuit Breaker Pattern**:
```python
class AzureServiceCircuitBreaker:
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
```

### 🧪 Testing Azure Integration

**Unit Tests with Mocking**:
```python
@patch('konveyor.core.azure_utils.clients.AzureOpenAI')
def test_openai_client(mock_openai):
    config = AzureConfig()
    client_manager = AzureClientManager(config)
    
    openai_client = AzureOpenAIClient(client_manager)
    result = openai_client.generate_embedding("test text")
    
    mock_openai.return_value.embeddings.create.assert_called_once()
```

**Integration Tests**:
```python
@pytest.mark.integration
async def test_azure_openai_integration():
    config = AzureConfig()
    if not config.openai_endpoint:
        pytest.skip("Azure OpenAI not configured")
    
    client_manager = AzureClientManager(config)
    openai_client = AzureOpenAIClient(client_manager)
    
    embedding = await openai_client.generate_embedding("test")
    assert len(embedding) == 1536  # Expected embedding dimension
```

### 🔍 Monitoring and Observability

**Logging Patterns**:
```python
import structlog

logger = structlog.get_logger(__name__)

class AzureOpenAIClient:
    async def generate_completion(self, messages):
        logger.info(
            "Generating completion",
            model=self.config.chat_deployment,
            message_count=len(messages)
        )
        
        start_time = time.time()
        try:
            response = await self.client.chat.completions.create(...)
            
            logger.info(
                "Completion generated successfully",
                response_time=time.time() - start_time,
                tokens_used=response.usage.total_tokens
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(
                "Completion generation failed",
                error=str(e),
                response_time=time.time() - start_time
            )
            raise
```

**Metrics Collection**:
```python
from azure.monitor.opentelemetry import configure_azure_monitor

# Configure Azure Monitor
configure_azure_monitor(
    connection_string=os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING")
)

# Custom metrics
from opentelemetry import metrics

meter = metrics.get_meter(__name__)
completion_counter = meter.create_counter(
    "azure_openai_completions_total",
    description="Total number of OpenAI completions"
)
```

---

**Next Steps**:
- 📱 **Django Apps**: See [Django Apps Structure](django-apps.md)
- 🧠 **Core Services**: Read [Core Modules](core-modules.md)
- 🎯 **Quick Reference**: Check [Quick Reference](quick-reference.md)

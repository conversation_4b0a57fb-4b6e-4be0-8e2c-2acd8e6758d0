# 🧠 Core Modules Structure

The `konveyor/core/` directory contains framework-agnostic business logic, shared utilities, and core services that power the entire application.

## 📋 Table of Contents

- [Overview](#overview)
- [Module Architecture](#module-architecture)
- [Core Modules](#core-modules)
- [Integration Patterns](#integration-patterns)
- [Development Guidelines](#development-guidelines)

## 🎯 Overview

The core modules follow **Domain-Driven Design** principles, providing:

- **Framework Independence**: Business logic separated from Django
- **Reusability**: Services can be used across different apps
- **Testability**: Clear interfaces and dependency injection
- **Maintainability**: Single responsibility and clear boundaries

### Design Principles

```mermaid
graph TD
    A[Framework Agnostic] --> B[Reusable Services]
    B --> C[Clear Interfaces]
    C --> D[Easy Testing]
    D --> E[Maintainable Code]
    
    style A fill:#e8f5e8
    style B fill:#e8f5e8
    style C fill:#e8f5e8
    style D fill:#e8f5e8
    style E fill:#e8f5e8
```

## 🏗️ Module Architecture

```
konveyor/core/
├── 🔧 azure_utils/          # Azure service foundations
├── 🤖 agent/               # AI agent orchestration
├── 💬 chat/                # Chat and conversation logic
├── 📄 documents/           # Document processing (legacy)
├── 🧠 kernel/              # Semantic Kernel configuration
├── 💾 memory/              # Memory and storage systems
├── 📱 slack/               # Slack integration utilities
└── 🔗 botframework/        # Bot Framework integration
```

## 🔧 Core Modules

### 🔧 Azure Utils (`azure_utils/`)

**Purpose**: Foundational Azure service utilities and configuration management.

```
azure_utils/
├── __init__.py
├── config.py              # AzureConfig singleton for settings
├── clients.py             # AzureClientManager for service clients
├── mixins.py              # Common Azure service mixins
├── retry.py               # Retry logic for Azure API calls
└── service.py             # Base AzureService class
```

**Key Components**:

- **`AzureConfig`**: Centralized configuration loading from environment
- **`AzureClientManager`**: Factory for Azure SDK clients (OpenAI, Search, Storage)
- **`AzureService`**: Base class with common Azure service patterns
- **Retry Logic**: Handles transient Azure API failures

**Usage Example**:
```python
from konveyor.core.azure_utils.config import AzureConfig
from konveyor.core.azure_utils.clients import AzureClientManager

config = AzureConfig()
client_manager = AzureClientManager(config)
openai_client = client_manager.get_openai_client()
```

### 🤖 Agent (`agent/`)

**Purpose**: AI agent orchestration and skill routing system.

```
agent/
├── __init__.py
├── orchestrator.py         # AgentOrchestratorSkill main class
├── registry.py             # SkillRegistry for skill management
└── types.py               # Type definitions and interfaces
```

**Key Components**:

- **`AgentOrchestratorSkill`**: Routes user requests to appropriate skills
- **`SkillRegistry`**: Manages available skills and their capabilities
- **Request Routing**: Intelligent routing based on user intent

**Integration Flow**:
```mermaid
sequenceDiagram
    participant User
    participant Agent
    participant Registry
    participant Skill
    participant LLM

    User->>Agent: Send Message
    Agent->>Registry: Find Appropriate Skill
    Registry-->>Agent: Return Skill
    Agent->>Skill: Execute with Context
    Skill->>LLM: Process with AI
    LLM-->>Skill: Return Result
    Skill-->>Agent: Formatted Response
    Agent-->>User: Final Answer
```

### 💬 Chat (`chat/`)

**Purpose**: Chat functionality and conversation management.

```
chat/
├── __init__.py
└── skill.py               # ChatSkill for general conversations
```

**Key Components**:

- **`ChatSkill`**: Handles general chat interactions
- **Conversation Context**: Maintains conversation state
- **Response Formatting**: Standardized response structure

### 🧠 Kernel (`kernel/`)

**Purpose**: Semantic Kernel configuration and factory methods.

```
kernel/
├── __init__.py
└── factory.py             # Kernel creation and configuration
```

**Key Components**:

- **Kernel Factory**: Creates configured Semantic Kernel instances
- **Service Registration**: Registers Azure services with kernel
- **Plugin Management**: Loads and configures skills as plugins

### 💾 Memory (`memory/`)

**Purpose**: Memory systems and conversation storage.

```
memory/
├── __init__.py
├── storage.py             # Memory storage implementations
└── types.py               # Memory-related type definitions
```

**Key Components**:

- **Storage Backends**: Multiple storage options (Redis, Cosmos DB, in-memory)
- **Conversation History**: Persistent conversation management
- **Memory Retrieval**: Context-aware memory access

### 📱 Slack (`slack/`)

**Purpose**: Direct Slack integration utilities.

```
slack/
├── __init__.py
├── client.py              # SlackService for API interactions
├── webhook.py             # Webhook event handling
└── README.md              # Slack integration documentation
```

**Key Components**:

- **`SlackService`**: Direct Slack API client
- **Webhook Handler**: Processes Slack events
- **Message Formatting**: Slack-specific message formatting

### 🔗 Bot Framework (`botframework/`)

**Purpose**: Microsoft Bot Framework integration.

```
botframework/
├── __init__.py
├── bot.py                 # KonveyorBot main implementation
└── adapter.py             # Bot Framework adapter configuration
```

**Key Components**:

- **`KonveyorBot`**: Main bot activity handler
- **Adapter Configuration**: Bot Framework adapter setup
- **Multi-Platform Support**: Handles Teams, Slack, and other channels

## 🔗 Integration Patterns

### 🔄 Service Layer Pattern

Core modules provide services that Django apps consume:

```mermaid
graph LR
    subgraph "Django Apps"
        A1[Documents App]
        A2[Search App]
        A3[Bot App]
    end

    subgraph "Core Services"
        C1[Document Service]
        C2[Azure Utils]
        C3[Agent Orchestrator]
    end

    A1 --> C1
    A2 --> C2
    A3 --> C3
    C1 --> C2
    C3 --> C2

    style A1 fill:#f3e5f5
    style A2 fill:#f3e5f5
    style A3 fill:#f3e5f5
    style C1 fill:#e8f5e8
    style C2 fill:#e8f5e8
    style C3 fill:#e8f5e8
```

### 🏭 Factory Pattern

Core modules use factories for object creation:

```python
# Kernel factory
kernel = create_kernel()

# Client factory
client_manager = AzureClientManager(config)
openai_client = client_manager.get_openai_client()

# Skill registry
registry = SkillRegistry()
registry.register_skill(skill_instance, "skill_name")
```

### 🔌 Dependency Injection

Services accept dependencies through constructors:

```python
class DocumentService:
    def __init__(self, azure_client: AzureClientManager):
        self.azure_client = azure_client
        self.storage_client = azure_client.get_storage_client()
```

## 🛠️ Development Guidelines

### ✅ Best Practices

**1. Framework Independence**:
```python
# ✅ Good: Framework-agnostic
class DocumentService:
    def process_document(self, content: bytes) -> ProcessedDocument:
        # Business logic here
        pass

# ❌ Avoid: Django-specific in core
class DocumentService:
    def process_document(self, uploaded_file: UploadedFile):
        # Django dependency in core
        pass
```

**2. Clear Interfaces**:
```python
# ✅ Good: Clear interface
from abc import ABC, abstractmethod

class StorageService(ABC):
    @abstractmethod
    def store(self, key: str, data: bytes) -> str:
        pass

# ✅ Good: Implementation
class AzureBlobStorage(StorageService):
    def store(self, key: str, data: bytes) -> str:
        # Azure-specific implementation
        pass
```

**3. Configuration Management**:
```python
# ✅ Good: Centralized configuration
config = AzureConfig()
endpoint = config.get_openai_endpoint()

# ❌ Avoid: Direct environment access
import os
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
```

### 🧪 Testing Patterns

**Unit Testing**:
```python
def test_document_service():
    # Mock dependencies
    mock_client = Mock()
    service = DocumentService(mock_client)
    
    # Test business logic
    result = service.process_document(test_content)
    assert result.status == "processed"
```

**Integration Testing**:
```python
def test_azure_integration():
    # Use test configuration
    config = AzureConfig(test_mode=True)
    service = DocumentService(config)
    
    # Test with real Azure services
    result = service.process_document(test_file)
    assert result.is_valid()
```

### 📁 Adding New Core Modules

**1. Create Module Structure**:
```
konveyor/core/new_module/
├── __init__.py
├── service.py             # Main service class
├── types.py               # Type definitions
├── exceptions.py          # Custom exceptions
└── tests/
    ├── __init__.py
    ├── test_service.py
    └── conftest.py
```

**2. Follow Naming Conventions**:
- **Services**: `SomethingService`
- **Managers**: `SomethingManager`
- **Factories**: `create_something()`
- **Configs**: `SomethingConfig`

**3. Add to Core Init**:
```python
# konveyor/core/__init__.py
from .new_module.service import NewModuleService

__all__ = ["NewModuleService"]
```

## 🔍 Troubleshooting

### Common Issues

**Import Errors**:
- Check module is added to `__init__.py`
- Verify circular import dependencies
- Use relative imports within core modules

**Configuration Issues**:
- Ensure `AzureConfig` is used for all Azure settings
- Check environment variables are properly loaded
- Verify test configurations for unit tests

**Service Integration**:
- Check dependency injection is properly configured
- Verify service interfaces are correctly implemented
- Ensure proper error handling and logging

---

**Next Steps**:
- 📱 **Django Integration**: See [Django Apps Structure](django-apps.md)
- ☁️ **Azure Services**: Read [Azure Integration](azure-integration.md)
- 🎯 **Quick Reference**: Check [Quick Reference](quick-reference.md)

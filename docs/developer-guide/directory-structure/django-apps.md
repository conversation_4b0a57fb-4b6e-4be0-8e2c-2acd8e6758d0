# 📱 Django Apps Structure

The `konveyor/apps/` directory contains Django applications that provide web interfaces, API endpoints, and Django-specific functionality.

## 📋 Table of Contents

- [Overview](#overview)
- [App Architecture](#app-architecture)
- [Individual Apps](#individual-apps)
- [Common Patterns](#common-patterns)
- [Development Guidelines](#development-guidelines)

## 🎯 Overview

Django apps in Konveyor follow **Domain-Driven Design**, where each app represents a specific business domain:

- **🗂️ Documents**: File processing and document management
- **🔍 Search**: Semantic search and indexing
- **🤖 RAG**: Retrieval-Augmented Generation workflows
- **💬 Bot**: Chat interfaces and bot integration
- **🏠 Core**: Shared Django functionality

### App Responsibilities

```mermaid
graph TD
    subgraph "Django Layer"
        A1[Documents App]
        A2[Search App]
        A3[RAG App]
        A4[Bot App]
        A5[Core App]
    end

    subgraph "Responsibilities"
        R1[HTTP APIs]
        R2[Django Models]
        R3[URL Routing]
        R4[Request/Response]
        R5[Django Admin]
    end

    subgraph "Core Services"
        C1[Business Logic]
        C2[Azure Integration]
        C3[AI Services]
    end

    A1 --> R1
    A2 --> R2
    A3 --> R3
    A4 --> R4
    A5 --> R5
    
    A1 --> C1
    A2 --> C1
    A3 --> C2
    A4 --> C3

    style A1 fill:#f3e5f5
    style A2 fill:#f3e5f5
    style A3 fill:#f3e5f5
    style A4 fill:#f3e5f5
    style A5 fill:#f3e5f5
    style C1 fill:#e8f5e8
    style C2 fill:#e8f5e8
    style C3 fill:#e8f5e8
```

## 🏗️ App Architecture

### Standard Django App Structure

```
konveyor/apps/example_app/
├── __init__.py
├── apps.py                 # App configuration
├── models.py               # Django models
├── views.py                # API views and endpoints
├── urls.py                 # URL routing
├── admin.py                # Django admin configuration
├── serializers.py          # DRF serializers (if using DRF)
├── services/               # App-specific services
│   ├── __init__.py
│   └── adapter.py          # Adapts core services for Django
├── migrations/             # Database migrations
│   └── __init__.py
└── tests/                  # App-specific tests
    ├── __init__.py
    ├── test_models.py
    ├── test_views.py
    └── test_services.py
```

## 📱 Individual Apps

### 🗂️ Documents App (`apps/documents/`)

**Purpose**: Document upload, processing, and management.

```
documents/
├── apps.py                 # DocumentsConfig
├── models.py               # Document, DocumentChunk models
├── views.py                # Upload, status, management endpoints
├── urls.py                 # /api/documents/ routes
├── serializers.py          # Document serializers
├── services/
│   ├── document_adapter.py # Adapts core DocumentService
│   └── chunk_service.py    # Chunking utilities
├── migrations/
└── tests/
    ├── test_document_upload.py
    ├── test_models.py
    └── test_services.py
```

**Key Models**:
```python
class Document(models.Model):
    title = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500)
    content_type = models.CharField(max_length=100)
    size = models.IntegerField()
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)

class DocumentChunk(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    chunk_index = models.IntegerField()
    content = models.TextField()
    embedding_vector = models.JSONField(null=True)
```

**API Endpoints**:
- `POST /api/documents/upload/` - Upload new document
- `GET /api/documents/` - List documents
- `GET /api/documents/{id}/` - Get document details
- `DELETE /api/documents/{id}/` - Delete document
- `GET /api/documents/{id}/status/` - Processing status

### 🔍 Search App (`apps/search/`)

**Purpose**: Semantic search, indexing, and search management.

```
search/
├── apps.py                 # SearchConfig
├── models.py               # SearchIndex, SearchResult models
├── views.py                # Search endpoints
├── urls.py                 # /api/search/ routes
├── serializers.py          # Search serializers
├── services/
│   ├── search_service.py   # Django search adapter
│   └── indexing_service.py # Indexing management
├── management/
│   └── commands/
│       └── setup_search_index.py
└── tests/
    ├── test_search.py
    ├── test_indexing.py
    └── test_management.py
```

**Key Models**:
```python
class SearchIndex(models.Model):
    name = models.CharField(max_length=100, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    document_count = models.IntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

class SearchResult(models.Model):
    query = models.TextField()
    results = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    response_time = models.FloatField()
```

**API Endpoints**:
- `POST /api/search/` - Execute search query
- `GET /api/search/indexes/` - List search indexes
- `POST /api/search/indexes/rebuild/` - Rebuild search index
- `GET /api/search/stats/` - Search statistics

### 🤖 RAG App (`apps/rag/`)

**Purpose**: RAG workflows, conversation management, and AI responses.

```
rag/
├── apps.py                 # RAGConfig
├── models.py               # Conversation, Message models
├── views.py                # RAG endpoints
├── urls.py                 # /api/rag/ routes
├── serializers.py          # RAG serializers
├── services/
│   ├── rag_adapter.py      # Adapts core RAG services
│   └── conversation_service.py # Conversation management
└── tests/
    ├── test_rag.py
    ├── test_conversations.py
    └── test_services.py
```

**Key Models**:
```python
class Conversation(models.Model):
    session_id = models.CharField(max_length=100, unique=True)
    user_id = models.CharField(max_length=100, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Message(models.Model):
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE)
    role = models.CharField(max_length=20)  # 'user' or 'assistant'
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict)
```

**API Endpoints**:
- `POST /api/rag/chat/` - Send chat message
- `GET /api/rag/conversations/` - List conversations
- `GET /api/rag/conversations/{id}/` - Get conversation history
- `DELETE /api/rag/conversations/{id}/` - Delete conversation

### 💬 Bot App (`apps/bot/`)

**Purpose**: Bot Framework integration, Slack connectivity, and chat interfaces.

```
bot/
├── apps.py                 # BotConfig
├── models.py               # BotUser, BotSession models
├── views.py                # Bot webhook endpoints
├── urls.py                 # /api/bot/ routes
├── services/
│   ├── bot_adapter.py      # Bot Framework adapter
│   ├── slack_service.py    # Slack integration
│   └── message_handler.py  # Message processing
├── webhooks/
│   ├── slack_webhook.py    # Slack event handler
│   └── teams_webhook.py    # Teams event handler
└── tests/
    ├── test_bot.py
    ├── test_slack.py
    └── test_webhooks.py
```

**Key Models**:
```python
class BotUser(models.Model):
    platform = models.CharField(max_length=20)  # 'slack', 'teams'
    platform_user_id = models.CharField(max_length=100)
    display_name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

class BotSession(models.Model):
    user = models.ForeignKey(BotUser, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=100)
    platform = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
```

**API Endpoints**:
- `POST /api/bot/messages/` - Bot Framework webhook
- `POST /api/bot/slack/events/` - Slack events webhook
- `POST /api/bot/slack/commands/` - Slack slash commands
- `GET /api/bot/users/` - List bot users

### 🏠 Core App (`apps/core/`)

**Purpose**: Shared Django functionality, common models, and utilities.

```
core/
├── apps.py                 # CoreConfig
├── models.py               # Base models, common fields
├── views.py                # Health check, status endpoints
├── urls.py                 # /api/ root routes
├── middleware.py           # Custom middleware
├── permissions.py          # Custom permissions
├── serializers.py          # Base serializers
└── tests/
    ├── test_middleware.py
    ├── test_permissions.py
    └── test_views.py
```

## 🔄 Common Patterns

### 🔌 Service Adapter Pattern

Django apps adapt core services for web framework use:

```python
# apps/documents/services/document_adapter.py
from konveyor.core.documents import DocumentService
from django.core.files.uploadedfile import UploadedFile

class DjangoDocumentService:
    def __init__(self):
        self.core_service = DocumentService()
    
    def process_uploaded_file(self, uploaded_file: UploadedFile):
        # Convert Django file to core service format
        content = uploaded_file.read()
        metadata = {
            'filename': uploaded_file.name,
            'content_type': uploaded_file.content_type,
            'size': uploaded_file.size
        }
        
        # Delegate to core service
        return self.core_service.process_document(content, metadata)
```

### 🗄️ Model-Service Separation

Models handle data persistence, services handle business logic:

```python
# models.py - Data persistence only
class Document(models.Model):
    title = models.CharField(max_length=255)
    processed = models.BooleanField(default=False)
    
    def mark_as_processed(self):
        self.processed = True
        self.save()

# services/document_adapter.py - Business logic
class DocumentAdapter:
    def process_document(self, document_id: int):
        document = Document.objects.get(id=document_id)
        
        # Use core service for processing
        result = self.core_service.process(document.file_path)
        
        # Update Django model
        document.mark_as_processed()
        
        return result
```

### 🌐 API Response Patterns

Consistent API response structure across all apps:

```python
# Successful response
{
    "success": true,
    "data": {...},
    "message": "Operation completed successfully"
}

# Error response
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input provided",
        "details": {...}
    }
}
```

## 🛠️ Development Guidelines

### ✅ Best Practices

**1. Keep Apps Focused**:
```python
# ✅ Good: App handles specific domain
class DocumentsConfig(AppConfig):
    name = 'konveyor.apps.documents'
    verbose_name = 'Document Management'

# ❌ Avoid: Mixed responsibilities
class DocumentsSearchConfig(AppConfig):  # Too broad
    name = 'konveyor.apps.documents_search'
```

**2. Use Service Adapters**:
```python
# ✅ Good: Adapter pattern
class DocumentAdapter:
    def __init__(self):
        self.core_service = DocumentService()
    
    def handle_upload(self, request):
        # Django-specific handling
        file = request.FILES['document']
        # Delegate to core service
        return self.core_service.process(file.read())

# ❌ Avoid: Business logic in views
def upload_view(request):
    # Business logic mixed with web logic
    file = request.FILES['document']
    # Process file directly in view...
```

**3. Consistent URL Patterns**:
```python
# urls.py - RESTful patterns
urlpatterns = [
    path('', views.list_create, name='list_create'),
    path('<int:pk>/', views.retrieve_update_destroy, name='detail'),
    path('<int:pk>/status/', views.status, name='status'),
]
```

### 🧪 Testing Strategies

**Unit Tests**:
```python
def test_document_model():
    document = Document.objects.create(title="Test Doc")
    assert document.title == "Test Doc"
    assert not document.processed

def test_document_adapter():
    adapter = DocumentAdapter()
    mock_file = Mock()
    result = adapter.process_uploaded_file(mock_file)
    assert result.success
```

**Integration Tests**:
```python
def test_document_upload_api(client):
    with open('test_file.pdf', 'rb') as f:
        response = client.post('/api/documents/upload/', {'file': f})
    
    assert response.status_code == 201
    assert Document.objects.count() == 1
```

### 📁 Adding New Apps

**1. Create App Structure**:
```bash
python manage.py startapp new_app apps/new_app
```

**2. Configure App**:
```python
# apps/new_app/apps.py
class NewAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'konveyor.apps.new_app'
    verbose_name = 'New App'
```

**3. Add to Settings**:
```python
# settings/base.py
INSTALLED_APPS = [
    # ...
    'konveyor.apps.new_app',
]
```

**4. Create URL Patterns**:
```python
# konveyor/urls.py
urlpatterns = [
    # ...
    path('api/new-app/', include('konveyor.apps.new_app.urls')),
]
```

---

**Next Steps**:
- 🧠 **Core Services**: See [Core Modules](core-modules.md)
- ☁️ **Azure Integration**: Read [Azure Integration](azure-integration.md)
- 🎯 **Quick Reference**: Check [Quick Reference](quick-reference.md)

# 🎯 Quick Reference Guide

Developer cheat sheet for finding files, understanding patterns, and adding new functionality to Konveyor.

## 📋 Table of Contents

- [File Finder](#file-finder)
- [Where to Add New Features](#where-to-add-new-features)
- [Common Patterns](#common-patterns)
- [Troubleshooting Guide](#troubleshooting-guide)

## 🔍 File Finder

### "Where do I find...?"

| Looking for... | Location | Example Files |
|----------------|----------|---------------|
| **API endpoints** | `konveyor/apps/*/views.py` | `apps/documents/views.py` |
| **Database models** | `konveyor/apps/*/models.py` | `apps/documents/models.py` |
| **URL routing** | `konveyor/apps/*/urls.py` | `apps/search/urls.py` |
| **Business logic** | `konveyor/core/*/` | `core/documents/document_service.py` |
| **Azure integration** | `konveyor/core/azure_utils/` | `azure_utils/clients.py` |
| **AI skills** | `konveyor/skills/` | `skills/documentation_navigator/` |
| **Configuration** | `konveyor/settings/` | `settings/base.py` |
| **Tests** | `*/tests/` or `test_*.py` | `apps/documents/tests/` |
| **Static files** | `konveyor/static/` | `static/css/`, `static/js/` |
| **Templates** | `konveyor/templates/` | `templates/base.html` |

### "Where is the code for...?"

| Feature | Primary Location | Supporting Files |
|---------|------------------|------------------|
| **Document upload** | `apps/documents/views.py` | `core/documents/document_service.py` |
| **Search functionality** | `apps/search/views.py` | `core/azure_utils/clients.py` |
| **Chat/RAG responses** | `apps/rag/views.py` | `core/chat/skill.py` |
| **Slack integration** | `apps/bot/services/slack_service.py` | `core/slack/client.py` |
| **Azure OpenAI calls** | `core/azure_adapters/openai/client.py` | `core/azure_utils/clients.py` |
| **User authentication** | `apps/core/views.py` | Django built-in auth |
| **Database migrations** | `apps/*/migrations/` | Auto-generated |
| **Admin interface** | `apps/*/admin.py` | Django admin |

## ➕ Where to Add New Features

### 🆕 Adding a New API Endpoint

**1. Determine the App**:
```
Documents-related → apps/documents/
Search-related → apps/search/
Chat/AI-related → apps/rag/
Bot-related → apps/bot/
General → apps/core/
```

**2. Add the View**:
```python
# apps/your_app/views.py
from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['POST'])
def your_new_endpoint(request):
    # Your logic here
    return Response({'success': True})
```

**3. Add URL Pattern**:
```python
# apps/your_app/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('your-endpoint/', views.your_new_endpoint, name='your_endpoint'),
]
```

**4. Include in Main URLs**:
```python
# konveyor/urls.py
path('api/your-app/', include('konveyor.apps.your_app.urls')),
```

### 🧠 Adding Business Logic

**1. Core Service** (framework-agnostic):
```python
# konveyor/core/your_domain/service.py
class YourService:
    def __init__(self, azure_client_manager):
        self.azure_client = azure_client_manager
    
    def your_business_logic(self, data):
        # Framework-agnostic logic
        return processed_data
```

**2. Django Adapter** (web framework integration):
```python
# konveyor/apps/your_app/services/adapter.py
from konveyor.core.your_domain.service import YourService

class DjangoYourService:
    def __init__(self):
        self.core_service = YourService()
    
    def handle_django_request(self, request):
        # Convert Django request to core service format
        data = self.extract_data(request)
        return self.core_service.your_business_logic(data)
```

### 🤖 Adding AI Skills

**1. Create Skill Directory**:
```
konveyor/skills/your_skill/
├── __init__.py
├── skill.py
└── tests/
    └── test_skill.py
```

**2. Implement Skill**:
```python
# konveyor/skills/your_skill/skill.py
from semantic_kernel.functions import kernel_function

class YourSkill:
    @kernel_function(
        description="What your skill does",
        name="your_function"
    )
    def your_function(self, input: str) -> str:
        # Your AI logic here
        return f"Processed: {input}"
```

**3. Register Skill**:
```python
# In app configuration or management command
from konveyor.core.agent import SkillRegistry
from konveyor.skills.your_skill.skill import YourSkill

registry = SkillRegistry()
registry.register_skill(YourSkill(), "your_skill")
```

### 🗄️ Adding Database Models

**1. Create Model**:
```python
# konveyor/apps/your_app/models.py
from django.db import models

class YourModel(models.Model):
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'your_app_your_model'
        verbose_name = 'Your Model'
        verbose_name_plural = 'Your Models'
```

**2. Create Migration**:
```bash
python manage.py makemigrations your_app
python manage.py migrate
```

**3. Add to Admin** (optional):
```python
# konveyor/apps/your_app/admin.py
from django.contrib import admin
from .models import YourModel

@admin.register(YourModel)
class YourModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']
```

## 🔄 Common Patterns

### 🏭 Service Factory Pattern

```python
# Creating services with dependencies
def create_document_service():
    config = AzureConfig()
    client_manager = AzureClientManager(config)
    return DocumentService(client_manager)

# Usage
document_service = create_document_service()
```

### 🔌 Adapter Pattern

```python
# Adapting core services for Django
class DjangoDocumentAdapter:
    def __init__(self):
        self.core_service = create_document_service()
    
    def process_uploaded_file(self, uploaded_file):
        # Convert Django UploadedFile to core service format
        content = uploaded_file.read()
        metadata = {
            'filename': uploaded_file.name,
            'content_type': uploaded_file.content_type
        }
        return self.core_service.process(content, metadata)
```

### 🔄 Async/Await Pattern

```python
# Async service methods
class AsyncDocumentService:
    async def process_document(self, content):
        # Async Azure operations
        embedding = await self.openai_client.generate_embedding(content)
        await self.search_client.upload_document(content, embedding)
        return result

# Usage in views
async def upload_view(request):
    service = AsyncDocumentService()
    result = await service.process_document(content)
    return JsonResponse(result)
```

### 🔒 Configuration Pattern

```python
# Always use centralized configuration
from konveyor.core.azure_utils.config import AzureConfig

config = AzureConfig()
endpoint = config.get_openai_endpoint()

# Never access environment directly in business logic
# ❌ endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
```

## 🐛 Troubleshooting Guide

### 🔍 Common Issues and Solutions

| Problem | Likely Cause | Solution |
|---------|--------------|----------|
| **Import errors** | Missing `__init__.py` or circular imports | Check package structure, use relative imports |
| **404 on API calls** | URL pattern not registered | Verify URL patterns in `urls.py` files |
| **Azure client errors** | Missing configuration | Check environment variables, validate `AzureConfig` |
| **Database errors** | Missing migrations | Run `python manage.py migrate` |
| **Skill not found** | Skill not registered | Check skill registration in `SkillRegistry` |
| **Static files not loading** | Static files not collected | Run `python manage.py collectstatic` |

### 🔧 Debugging Steps

**1. Check Logs**:
```bash
# Development
tail -f logs/konveyor-dev.log

# Production (Azure App Service)
az webapp log tail --name your-app --resource-group your-rg
```

**2. Verify Configuration**:
```python
# Check Azure configuration
from konveyor.core.azure_utils.config import AzureConfig
config = AzureConfig()
config.validate_required_config()
```

**3. Test Services Individually**:
```python
# Test document service
from konveyor.core.documents.document_service import DocumentService
service = DocumentService()
result = service.process("test content")
```

**4. Check Database**:
```bash
# Django shell
python manage.py shell

# Check models
>>> from konveyor.apps.documents.models import Document
>>> Document.objects.count()
```

### 📊 Performance Debugging

**1. Profile Views**:
```python
# Add to views for profiling
import cProfile
import pstats

def profile_view(request):
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Your view logic
    result = your_view_logic(request)
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)  # Top 10 functions
    
    return result
```

**2. Monitor Azure Calls**:
```python
# Add timing to Azure operations
import time
from konveyor.core.azure_utils.clients import AzureClientManager

start_time = time.time()
result = await openai_client.generate_completion(messages)
duration = time.time() - start_time

logger.info(f"OpenAI completion took {duration:.2f}s")
```

### 🧪 Testing Helpers

**Quick Test Commands**:
```bash
# Run specific app tests
python manage.py test konveyor.apps.documents

# Run with coverage
coverage run --source='.' manage.py test
coverage report

# Run integration tests
python manage.py test --tag=integration

# Run specific test method
python manage.py test konveyor.apps.documents.tests.test_views.TestDocumentUpload.test_upload_success
```

**Test Data Setup**:
```python
# Create test data
from konveyor.apps.documents.models import Document

def create_test_document():
    return Document.objects.create(
        title="Test Document",
        file_path="/test/path.pdf",
        content_type="application/pdf"
    )
```

## 📚 Quick Links

### 📖 Documentation
- [Django Apps Structure](django-apps.md) - Detailed app documentation
- [Core Modules](core-modules.md) - Business logic documentation
- [Azure Integration](azure-integration.md) - Cloud services documentation

### 🔧 Configuration
- [Configuration Reference](../../reference/configuration.md) - Complete config guide
- [Environment Variables](../../reference/environment-variables.md) - All environment variables
- [Settings Reference](../../reference/settings.md) - Django settings

### 🚀 Development
- [Development Setup](../development-setup.md) - Local development guide
- [Contributing](../contributing.md) - Contribution guidelines
- [Testing](../testing.md) - Testing strategies

---

**Need more help?** Check the detailed documentation or ask in team channels!

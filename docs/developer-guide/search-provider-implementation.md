# 🔧 Search Provider Implementation Guide

Guide for implementing new vector database providers in Konveyor's search abstraction layer.

## 📋 Table of Contents

- [Overview](#overview)
- [Interface Requirements](#interface-requirements)
- [Implementation Steps](#implementation-steps)
- [Provider Examples](#provider-examples)
- [Testing Guidelines](#testing-guidelines)
- [Registration and Configuration](#registration-and-configuration)
- [Best Practices](#best-practices)

## 🎯 Overview

Konveyor's search abstraction layer allows you to add support for any vector database by implementing the `VectorSearchInterface`. This guide walks through creating a new provider implementation.

### Architecture

```
Application Code
       ↓
SearchServiceInterface (High-level API)
       ↓
VectorSearchInterface (Provider abstraction)
       ↓
Your Provider Implementation
       ↓
Vector Database SDK/API
```

## 📝 Interface Requirements

### VectorSearchInterface

All vector search providers must implement this interface:

```python
from konveyor.core.search.interfaces import VectorSearchInterface

class YourSearchProvider(VectorSearchInterface):
    """Your custom vector search provider."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize provider with configuration."""
        pass
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform search operation."""
        pass
    
    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Add documents to the search index."""
        pass
    
    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in the search index."""
        pass
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from the search index."""
        pass
    
    async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
        """Create a search index."""
        pass
    
    async def delete_index(self, index_name: str) -> bool:
        """Delete a search index."""
        pass
    
    async def list_indexes(self) -> List[str]:
        """List available indexes."""
        pass
    
    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get index statistics."""
        pass
```

### Data Structures

Use these standardized data structures:

```python
from konveyor.core.search.interfaces import SearchQuery, SearchResult, DocumentChunk, SearchType

# Input: Search query
SearchQuery(
    text="search query",
    search_type=SearchType.HYBRID,
    top_k=5,
    filters={"category": "documentation"},
    embedding=[0.1, 0.2, ...],  # Optional pre-computed embedding
    min_score=0.7
)

# Output: Search results
SearchResult(
    id="doc-123",
    content="Document content...",
    score=0.95,
    metadata={"category": "documentation"},
    document_id="doc-123",
    chunk_id="chunk-1",
    chunk_index=0
)

# Input: Document for indexing
DocumentChunk(
    id="chunk-1",
    content="Document content...",
    embedding=[0.1, 0.2, ...],
    metadata={"category": "documentation"},
    document_id="doc-123",
    chunk_index=0
)
```

## 🚀 Implementation Steps

### Step 1: Create Provider Class

```python
# konveyor/core/search/providers/your_provider.py
import logging
from typing import Any, Dict, List, Optional

try:
    from your_vector_db_sdk import YourVectorDB, YourVectorDBException
    YOUR_DB_AVAILABLE = True
except ImportError:
    YOUR_DB_AVAILABLE = False
    YourVectorDBException = Exception

from ..interfaces import (
    VectorSearchInterface,
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)

logger = logging.getLogger(__name__)


class YourSearchProvider(VectorSearchInterface):
    """Your vector database provider implementation."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize provider with configuration."""
        if not YOUR_DB_AVAILABLE:
            raise ImportError("Your vector DB SDK not available. Install your-vector-db-sdk.")
        
        self.config = config
        self.api_key = config.get("your_db_api_key")
        self.endpoint = config.get("your_db_endpoint")
        self.index_name = config.get("your_db_index_name", "konveyor-documents")
        
        if not self.api_key:
            raise ValueError("Your DB API key is required")
        
        # Initialize client
        self.client = YourVectorDB(
            api_key=self.api_key,
            endpoint=self.endpoint
        )
```

### Step 2: Implement Search Method

```python
async def search(self, query: SearchQuery) -> List[SearchResult]:
    """Perform search operation."""
    try:
        # Prepare search parameters
        search_params = {
            "query_vector": query.embedding,
            "top_k": query.top_k,
            "include_metadata": True,
        }
        
        # Add filters if provided
        if query.filters:
            search_params["filter"] = self._convert_filters(query.filters)
        
        # Handle different search types
        if query.search_type == SearchType.HYBRID:
            search_params["hybrid_search"] = True
            search_params["query_text"] = query.text
        elif query.search_type == SearchType.KEYWORD:
            search_params["text_search"] = query.text
        
        # Perform search
        response = self.client.search(**search_params)
        
        # Convert results to standardized format
        search_results = []
        for result in response.results:
            search_result = SearchResult(
                id=result.id,
                content=result.metadata.get("content", ""),
                score=result.score,
                metadata=result.metadata,
                document_id=result.metadata.get("document_id"),
                chunk_id=result.metadata.get("chunk_id"),
                chunk_index=result.metadata.get("chunk_index"),
            )
            
            # Apply minimum score filter
            if query.min_score is None or search_result.score >= query.min_score:
                search_results.append(search_result)
        
        logger.info(f"Search completed: {len(search_results)} results")
        return search_results
        
    except YourVectorDBException as e:
        logger.error(f"Your DB search error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Search operation failed: {str(e)}")
        raise

def _convert_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
    """Convert generic filters to your DB filter format."""
    # Implement filter conversion logic specific to your vector DB
    converted_filters = {}
    for key, value in filters.items():
        if isinstance(value, str):
            converted_filters[key] = {"equals": value}
        elif isinstance(value, list):
            converted_filters[key] = {"in": value}
        # Add more filter types as needed
    return converted_filters
```

### Step 3: Implement Document Operations

```python
async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
    """Add documents to the search index."""
    try:
        # Convert to your DB format
        db_documents = []
        for doc in documents:
            if not doc.embedding:
                logger.warning(f"Document {doc.id} has no embedding, skipping")
                continue
            
            db_doc = {
                "id": doc.id,
                "vector": doc.embedding,
                "metadata": {
                    "content": doc.content,
                    "document_id": doc.document_id,
                    "chunk_id": doc.id,
                    "chunk_index": doc.chunk_index,
                    **(doc.metadata or {})
                }
            }
            db_documents.append(db_doc)
        
        # Batch insert with error handling
        successful_ids = []
        batch_size = 100  # Adjust based on your DB limits
        
        for i in range(0, len(db_documents), batch_size):
            batch = db_documents[i:i + batch_size]
            try:
                result = self.client.upsert(
                    index_name=self.index_name,
                    documents=batch
                )
                successful_ids.extend([doc["id"] for doc in batch])
            except YourVectorDBException as e:
                logger.error(f"Failed to upsert batch {i//batch_size + 1}: {str(e)}")
        
        logger.info(f"Successfully added {len(successful_ids)}/{len(documents)} documents")
        return successful_ids
        
    except Exception as e:
        logger.error(f"Document addition error: {str(e)}")
        raise

async def update_document(self, document: DocumentChunk) -> bool:
    """Update a document in the search index."""
    try:
        # Most vector DBs handle update via upsert
        result_ids = await self.add_documents([document])
        return len(result_ids) > 0
    except Exception as e:
        logger.error(f"Document update error: {str(e)}")
        return False

async def delete_document(self, document_id: str) -> bool:
    """Delete a document from the search index."""
    try:
        self.client.delete(
            index_name=self.index_name,
            ids=[document_id]
        )
        logger.info(f"Successfully deleted document {document_id}")
        return True
    except YourVectorDBException as e:
        logger.error(f"Document deletion error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Document deletion failed: {str(e)}")
        return False
```

### Step 4: Implement Index Management

```python
async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
    """Create a search index."""
    try:
        # Check if index exists
        existing_indexes = await self.list_indexes()
        if index_name in existing_indexes:
            logger.info(f"Index '{index_name}' already exists")
            return True
        
        # Create index with default or provided schema
        index_config = {
            "name": index_name,
            "dimension": 1536,  # Default for OpenAI embeddings
            "metric": "cosine",
            **(schema or {})
        }
        
        self.client.create_index(**index_config)
        logger.info(f"Successfully created index: {index_name}")
        return True
        
    except YourVectorDBException as e:
        logger.error(f"Index creation error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Index creation failed: {str(e)}")
        return False

async def delete_index(self, index_name: str) -> bool:
    """Delete a search index."""
    try:
        self.client.delete_index(index_name)
        logger.info(f"Successfully deleted index: {index_name}")
        return True
    except YourVectorDBException as e:
        logger.error(f"Index deletion error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Index deletion failed: {str(e)}")
        return False

async def list_indexes(self) -> List[str]:
    """List available indexes."""
    try:
        indexes = self.client.list_indexes()
        return [idx.name for idx in indexes]
    except YourVectorDBException as e:
        logger.error(f"List indexes error: {str(e)}")
        return []
    except Exception as e:
        logger.error(f"List indexes failed: {str(e)}")
        return []

async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
    """Get index statistics."""
    try:
        stats = self.client.describe_index(index_name)
        return {
            "document_count": stats.total_vector_count,
            "dimension": stats.dimension,
            "metric": stats.metric,
            "status": stats.status
        }
    except YourVectorDBException as e:
        logger.error(f"Index stats error: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"Get index stats failed: {str(e)}")
        return {}
```

## 🧪 Testing Guidelines

### Unit Tests

```python
# tests/test_your_provider.py
import pytest
from unittest.mock import Mock, AsyncMock
from konveyor.core.search.providers.your_provider import YourSearchProvider
from konveyor.core.search.interfaces import SearchQuery, DocumentChunk, SearchType

class TestYourSearchProvider:
    @pytest.fixture
    def provider(self):
        config = {
            "your_db_api_key": "test-key",
            "your_db_endpoint": "https://test.example.com",
            "your_db_index_name": "test-index"
        }
        return YourSearchProvider(config)
    
    @pytest.mark.asyncio
    async def test_search(self, provider):
        # Mock the client
        provider.client.search = AsyncMock(return_value=Mock(
            results=[
                Mock(
                    id="test-1",
                    score=0.95,
                    metadata={"content": "test content", "document_id": "doc-1"}
                )
            ]
        ))
        
        query = SearchQuery(
            text="test query",
            search_type=SearchType.VECTOR,
            top_k=5,
            embedding=[0.1, 0.2, 0.3]
        )
        
        results = await provider.search(query)
        
        assert len(results) == 1
        assert results[0].id == "test-1"
        assert results[0].score == 0.95
    
    @pytest.mark.asyncio
    async def test_add_documents(self, provider):
        provider.client.upsert = AsyncMock(return_value=True)
        
        documents = [
            DocumentChunk(
                id="test-1",
                content="test content",
                embedding=[0.1, 0.2, 0.3],
                metadata={"category": "test"}
            )
        ]
        
        result_ids = await provider.add_documents(documents)
        
        assert len(result_ids) == 1
        assert result_ids[0] == "test-1"
```

### Integration Tests

```python
# tests/integration/test_your_provider_integration.py
import pytest
from konveyor.core.search import get_search_service

@pytest.mark.integration
class TestYourProviderIntegration:
    @pytest.fixture
    def search_service(self):
        return get_search_service(
            vector_provider="your_provider",
            config={
                "your_db_api_key": "real-test-key",
                "your_db_endpoint": "https://test.example.com"
            }
        )
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, search_service):
        # Test document indexing
        success = await search_service.index_document_chunk(
            chunk_id="integration-test-1",
            document_id="integration-doc",
            content="Integration test content",
            chunk_index=0,
            metadata={"test": True}
        )
        assert success
        
        # Test search
        results = await search_service.semantic_search("integration test")
        assert len(results) > 0
        assert any("integration" in r.content.lower() for r in results)
```

## 📝 Registration and Configuration

### Register Your Provider

```python
# konveyor/core/search/factory.py
from .providers.your_provider import YourSearchProvider

# Register in factory
SearchProviderFactory.register_vector_provider("your_provider", YourSearchProvider)
```

### Configuration Settings

```python
# konveyor/settings/base.py
# Add your provider settings
YOUR_DB_API_KEY = os.getenv("YOUR_DB_API_KEY")
YOUR_DB_ENDPOINT = os.getenv("YOUR_DB_ENDPOINT")
YOUR_DB_INDEX_NAME = os.getenv("YOUR_DB_INDEX_NAME", "konveyor-documents")
```

### Environment Variables

```bash
# .env
SEARCH_PROVIDER=your_provider
YOUR_DB_API_KEY=your-api-key
YOUR_DB_ENDPOINT=https://api.yourdb.com
YOUR_DB_INDEX_NAME=konveyor-documents
```

## ✅ Best Practices

### Error Handling

```python
# Always wrap provider-specific exceptions
try:
    result = self.client.some_operation()
except YourVectorDBException as e:
    logger.error(f"Provider-specific error: {str(e)}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {str(e)}")
    raise
```

### Logging

```python
# Use structured logging
logger.info(f"Search completed", extra={
    "provider": "your_provider",
    "query_type": query.search_type.value,
    "results_count": len(results),
    "query_time_ms": query_time
})
```

### Configuration Validation

```python
def __init__(self, config: Dict[str, Any]):
    # Validate required configuration
    required_fields = ["your_db_api_key", "your_db_endpoint"]
    for field in required_fields:
        if not config.get(field):
            raise ValueError(f"{field} is required for YourSearchProvider")
```

### Async Best Practices

```python
# Use proper async patterns
async def batch_operation(self, items):
    # Process in batches to avoid overwhelming the API
    batch_size = 100
    results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        batch_results = await self._process_batch(batch)
        results.extend(batch_results)
        
        # Rate limiting
        if i + batch_size < len(items):
            await asyncio.sleep(0.1)
    
    return results
```

---

**Next Steps**:
- [Migration Guide](search-provider-migration.md)
- [Performance Optimization](search-performance-optimization.md)
- [Testing Framework](../testing/search-testing.md)

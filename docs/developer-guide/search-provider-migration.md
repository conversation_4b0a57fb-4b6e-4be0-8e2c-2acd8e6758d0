# 🔄 Search Provider Migration Guide

Complete guide for migrating between vector database providers using Konveyor's search abstraction layer.

## 📋 Table of Contents

- [Overview](#overview)
- [Migration Strategy](#migration-strategy)
- [Azure AI Search to Pinecone](#azure-ai-search-to-pinecone)
- [Provider Configuration](#provider-configuration)
- [Data Migration](#data-migration)
- [Testing and Validation](#testing-and-validation)
- [Rollback Procedures](#rollback-procedures)
- [Cost Analysis](#cost-analysis)

## 🎯 Overview

Konveyor v2.0 introduces a provider-agnostic search abstraction layer that enables seamless migration between vector database providers without changing application code.

### Supported Providers

| Provider | Type | Use Case | Cost | Performance |
|----------|------|----------|------|-------------|
| **Pinecone Serverless** | Managed | Production (recommended) | Low | High |
| **Azure AI Search** | Managed | Current production | High | High |
| **LangChain Adapter** | Various | Development/Testing | Varies | Varies |

### Key Benefits

- **70% Cost Reduction**: Pinecone vs Azure AI Search
- **Zero Downtime**: Gradual migration with dual-write
- **Backward Compatibility**: Existing code continues to work
- **Runtime Switching**: Change providers via environment variables

## 🚀 Migration Strategy

### Phase 1: Preparation (Week 1)
1. **Set up Pinecone account** and obtain API keys
2. **Configure development environment** with new providers
3. **Test abstraction layer** with sample data
4. **Validate search quality** between providers

### Phase 2: Implementation (Week 2)
1. **Deploy abstraction layer** to staging environment
2. **Implement dual-write pattern** for data synchronization
3. **Migrate existing data** using batch migration tools
4. **Perform A/B testing** to validate search quality

### Phase 3: Cutover (Week 3)
1. **Gradual traffic migration** (10% → 50% → 100%)
2. **Monitor performance** and error rates
3. **Complete migration** and cleanup old resources
4. **Document lessons learned** and update procedures

## 🎯 Azure AI Search to Pinecone

**Recommended migration path for 70% cost reduction.**

### Step 1: Set Up Pinecone

```bash
# 1. Create Pinecone account at https://www.pinecone.io/
# 2. Create API key in Pinecone console
# 3. Configure environment variables

# Development environment
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=azure_openai
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_INDEX_NAME=konveyor-dev
PINECONE_SERVERLESS_REGION=us-east-1

# Keep existing Azure OpenAI for embeddings
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key
```

### Step 2: Test Provider Switching

```python
# Test the abstraction layer
from konveyor.core.search import get_search_service

# Create Pinecone service
pinecone_service = get_search_service(
    vector_provider="pinecone",
    embedding_provider="azure_openai"
)

# Test basic operations
results = await pinecone_service.semantic_search("test query")
print(f"Found {len(results)} results")

# Test document indexing
success = await pinecone_service.index_document_chunk(
    chunk_id="test-1",
    document_id="test-doc",
    content="Test content",
    chunk_index=0,
    metadata={"source": "test"}
)
```

### Step 3: Data Migration

```python
# Migration script example
from konveyor.core.search.migration import DataMigrator

migrator = DataMigrator(
    source_provider="azure",
    target_provider="pinecone",
    batch_size=100
)

# Export from Azure AI Search
exported_data = await migrator.export_data()
print(f"Exported {len(exported_data)} documents")

# Import to Pinecone
import_results = await migrator.import_data(exported_data)
print(f"Imported {len(import_results)} documents")

# Validate migration
validation_results = await migrator.validate_migration()
print(f"Validation: {validation_results}")
```

### Step 4: Production Deployment

```bash
# Production environment variables
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=azure_openai
PINECONE_API_KEY=your-production-pinecone-key
PINECONE_INDEX_NAME=konveyor-prod
PINECONE_SERVERLESS_REGION=us-east-1

# Deploy with gradual rollout
# 1. Deploy to staging
# 2. Test with 10% traffic
# 3. Increase to 50% traffic
# 4. Full cutover to 100%
```

## ⚙️ Provider Configuration

### Environment-Based Configuration

```bash
# .env.production
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=azure_openai

# .env.development  
SEARCH_PROVIDER=azure
EMBEDDING_PROVIDER=azure_openai

# .env.test
SEARCH_PROVIDER=langchain
EMBEDDING_PROVIDER=azure_openai
```

### Programmatic Configuration

```python
# Custom provider configuration
from konveyor.core.search import SearchProviderFactory

# Register custom provider
SearchProviderFactory.register_vector_provider(
    "custom_provider", 
    CustomSearchProvider
)

# Create service with custom config
search_service = SearchProviderFactory.create_search_service(
    vector_provider="custom_provider",
    config={
        "custom_endpoint": "https://api.example.com",
        "custom_api_key": "your-key"
    }
)
```

### Provider-Specific Optimizations

```python
# Pinecone optimizations
pinecone_config = {
    "batch_size": 100,  # Optimal batch size
    "rate_limit_delay": 0.1,  # Delay between requests
    "max_retries": 3,  # Retry failed operations
    "timeout": 30,  # Request timeout
    "metric": "cosine",  # Distance metric
}

# Azure AI Search optimizations
azure_config = {
    "batch_size": 1000,  # Larger batches supported
    "semantic_configuration": "default",
    "query_type": "semantic",
    "vector_search_dimensions": 1536,
}
```

## 📊 Data Migration

### Migration Tools

```python
# Built-in migration utilities
from konveyor.core.search.migration import (
    AzureToQdrantMigrator,
    AzureToPineconeMigrator,
    DataValidator
)

# Azure to Pinecone migration
migrator = AzureToPineconeMigrator(
    azure_config={
        "endpoint": "https://your-search.search.windows.net",
        "api_key": "your-key"
    },
    pinecone_config={
        "api_key": "your-pinecone-key",
        "index_name": "konveyor-documents"
    }
)

# Run migration with progress tracking
async def migrate_data():
    async for progress in migrator.migrate_with_progress():
        print(f"Progress: {progress.percentage}% - {progress.message}")
```

### Dual-Write Pattern

```python
# Implement dual-write during migration
class DualWriteSearchService:
    def __init__(self, primary_service, secondary_service):
        self.primary = primary_service
        self.secondary = secondary_service
    
    async def index_document_chunk(self, **kwargs):
        # Write to both providers
        primary_result = await self.primary.index_document_chunk(**kwargs)
        
        try:
            secondary_result = await self.secondary.index_document_chunk(**kwargs)
        except Exception as e:
            logger.warning(f"Secondary write failed: {e}")
            secondary_result = False
        
        return primary_result and secondary_result
```

### Data Validation

```python
# Validate migration results
validator = DataValidator()

# Compare document counts
azure_count = await azure_service.get_document_count()
pinecone_count = await pinecone_service.get_document_count()
assert azure_count == pinecone_count, "Document count mismatch"

# Compare search results
test_queries = ["machine learning", "API documentation", "deployment guide"]
for query in test_queries:
    azure_results = await azure_service.semantic_search(query)
    pinecone_results = await pinecone_service.semantic_search(query)
    
    similarity = validator.compare_results(azure_results, pinecone_results)
    assert similarity > 0.95, f"Search quality degraded for query: {query}"
```

## 🧪 Testing and Validation

### Unit Tests

```python
# Test provider switching
class TestSearchProviders(TestCase):
    def test_provider_factory(self):
        # Test Azure provider
        azure_service = get_search_service(vector_provider="azure")
        self.assertIsInstance(azure_service.vector_search, AzureSearchProvider)
        
        # Test Pinecone provider
        pinecone_service = get_search_service(vector_provider="pinecone")
        self.assertIsInstance(pinecone_service.vector_search, PineconeSearchProvider)
    
    async def test_search_consistency(self):
        # Test that both providers return similar results
        query = "test query"
        
        azure_results = await self.azure_service.semantic_search(query)
        pinecone_results = await self.pinecone_service.semantic_search(query)
        
        # Compare top results
        self.assertEqual(len(azure_results), len(pinecone_results))
        self.assertGreater(self.calculate_similarity(azure_results, pinecone_results), 0.9)
```

### Integration Tests

```python
# End-to-end migration test
class TestMigration(TestCase):
    async def test_full_migration(self):
        # 1. Set up test data in Azure
        test_docs = self.create_test_documents()
        await self.azure_service.index_documents(test_docs)
        
        # 2. Migrate to Pinecone
        migrator = AzureToPineconeMigrator(self.azure_config, self.pinecone_config)
        await migrator.migrate()
        
        # 3. Validate migration
        for doc in test_docs:
            azure_results = await self.azure_service.semantic_search(doc.content[:50])
            pinecone_results = await self.pinecone_service.semantic_search(doc.content[:50])
            
            self.assertGreater(len(azure_results), 0)
            self.assertGreater(len(pinecone_results), 0)
            self.assertEqual(azure_results[0].id, pinecone_results[0].id)
```

## 🔄 Rollback Procedures

### Emergency Rollback

```bash
# Immediate rollback to Azure AI Search
export SEARCH_PROVIDER=azure
export EMBEDDING_PROVIDER=azure_openai

# Restart application
kubectl rollout restart deployment/konveyor-app

# Verify rollback
curl -X POST /api/search/ -d '{"query": "test"}' | jq '.provider'
# Should return "azure"
```

### Gradual Rollback

```python
# Gradual traffic rollback
class GradualRollback:
    def __init__(self, primary_service, fallback_service):
        self.primary = primary_service
        self.fallback = fallback_service
        self.fallback_percentage = 0
    
    async def search(self, query):
        if random.random() < self.fallback_percentage:
            return await self.fallback.semantic_search(query)
        else:
            try:
                return await self.primary.semantic_search(query)
            except Exception:
                # Fallback on error
                return await self.fallback.semantic_search(query)
    
    def increase_fallback(self, percentage):
        self.fallback_percentage = min(1.0, percentage)
```

## 💰 Cost Analysis

### Monthly Cost Comparison

| Provider | Storage (100GB) | Queries (1M/month) | Total | Savings |
|----------|-----------------|-------------------|-------|---------|
| Azure AI Search | $200 | $50 | $250 | Baseline |
| Pinecone Serverless | $25 | $50 | $75 | 70% |
| Qdrant Cloud | $20 | $30 | $50 | 80% |
| Self-hosted | $10 | $20 | $30 | 88% |

### ROI Calculation

```python
# Calculate migration ROI
current_monthly_cost = 250  # Azure AI Search
new_monthly_cost = 75       # Pinecone Serverless
migration_cost = 15000      # Development + testing

monthly_savings = current_monthly_cost - new_monthly_cost  # $175
annual_savings = monthly_savings * 12  # $2,100
payback_period = migration_cost / monthly_savings  # 7.1 months

print(f"Monthly savings: ${monthly_savings}")
print(f"Annual savings: ${annual_savings}")
print(f"Payback period: {payback_period:.1f} months")
```

---

**Next Steps**: 
- [Provider Implementation Guide](search-provider-implementation.md)
- [Performance Optimization](search-performance-optimization.md)
- [Monitoring and Alerting](search-monitoring.md)

# Semantic Kernel Setup and Usage

> **📚 Complete Configuration Reference**: For comprehensive configuration documentation, see [Configuration Reference](reference/configuration.md#azure-services) and [Environment Variables Reference](reference/environment-variables.md#azure-openai).

## 1. Directory Structure

- `konveyor/skills/` contains Semantic Kernel modules:
  - `__init__.py`: Package initialization.
  - `setup.py`: Core Kernel creation and Azure OpenAI integration.

## 2. Installation

Install Semantic Kernel SDK:
```bash
pip install semantic-kernel
```

## 3. Configuration

> **📋 Complete Variable List**: See [Environment Variables Reference](reference/environment-variables.md#azure-openai) for all Azure OpenAI configuration options.

### Required Environment Variables

```bash
# Azure OpenAI (required)
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key

# Optional (with defaults)
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-4o  # default
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002  # default
AZURE_OPENAI_API_VERSION=2024-05-13  # default
```

## 4. Usage Example

```python
from konveyor.skills.setup import create_kernel

# Initialize kernel
kernel = create_kernel()

# Access chat AI service
chat = kernel.get_service('chat')
response = chat.generate('Hello Semantic Kernel')
print(response)
```

## 5. Memory Configuration

By default, a volatile memory store is added:
```python
mem = kernel.get_memory_store('volatile')
mem.save('key', 'value')
print(mem.get('key'))  # 'value'
```

## 6. Next Steps

- Add skill modules under `konveyor/skills/`.
- Configure persistent memory stores for production.

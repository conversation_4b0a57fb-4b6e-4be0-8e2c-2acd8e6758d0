# 🧪 Testing Guide

Comprehensive guide to testing in the Konveyor project, covering unit tests, integration tests, and real-world service testing.

## 📋 Table of Contents

- [Testing Philosophy](#testing-philosophy)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Test Categories](#test-categories)
- [CI/CD Integration](#cicd-integration)
- [Debugging Tests](#debugging-tests)
- [Best Practices](#best-practices)

## 🎯 Testing Philosophy

Konveyor follows a **real-world testing approach** that prioritizes integration with actual Azure services over mocked testing:

### Core Principles

1. **Real Service Integration**: Tests validate actual interactions with Azure services
2. **Production-Like Behavior**: Tests run against the same services used in production
3. **Complete Validation**: End-to-end workflows are tested from document upload to search retrieval
4. **Comprehensive Coverage**: Both unit and integration tests ensure code quality

### Why Real Services?

- **Genuine Validation**: Ensures Azure service integrations work correctly
- **API Changes Detection**: Catches breaking changes in Azure APIs
- **Performance Testing**: Validates real-world performance characteristics
- **Configuration Validation**: Tests actual service configurations

## 🏗️ Test Structure

### Directory Organization

```
tests/
├── unit/                   # Unit tests (no external services)
│   ├── test_models.py
│   ├── test_services.py
│   └── test_utilities.py
├── integration/            # Integration tests (mocked services)
│   ├── test_api_endpoints.py
│   ├── test_workflows.py
│   └── test_error_handling.py
├── real/                   # Real service tests
│   ├── test_azure_integration.py
│   ├── test_slack_integration.py
│   ├── test_search_init.py
│   ├── test_documentation_navigator_real.py
│   ├── test_pdf_parsing.py
│   └── test_user_profile_integration.py
├── fixtures/               # Test data and fixtures
│   ├── sample_documents/
│   ├── test_data.json
│   └── mock_responses/
├── conftest.py            # Pytest configuration
├── README.md              # Test documentation
└── run_all_tests.py       # Unified test runner
```

### App-Specific Tests

```
konveyor/apps/*/tests/
├── conftest.py            # App-specific test configuration
├── test_models.py         # Model tests
├── test_views.py          # View tests
├── test_services.py       # Service tests
└── README.md              # App test documentation
```

## 🚀 Running Tests

### Unified Test Runner

The primary way to run tests is using the unified test runner:

```bash
# Run all tests
python tests/run_all_tests.py

# Run specific category
python tests/run_all_tests.py --category unit
python tests/run_all_tests.py --category integration
python tests/run_all_tests.py --category real

# Run with specific environment
python tests/run_all_tests.py --env test --category real

# Run specific test file
python tests/run_all_tests.py --test-file tests/real/test_slack_integration.py
```

### Test Categories

| Category | Description | External Services | Command |
|----------|-------------|-------------------|---------|
| **unit** | Unit tests without external dependencies | None | `--category unit` |
| **integration** | Integration tests with mocked services | Mocked | `--category integration` |
| **real** | Tests with real Azure/Slack services | Real | `--category real` |
| **search** | Search functionality tests | Real Azure Search | `--category search` |
| **document** | Document processing tests | Real Azure services | `--category document` |
| **slack** | Slack integration tests | Real Slack API | `--category slack` |

### Environment Options

```bash
# Development environment (default)
python tests/run_all_tests.py --env dev

# Test environment
python tests/run_all_tests.py --env test

# Production environment (careful!)
python tests/run_all_tests.py --env prod
```

### Service Mode Options

```bash
# Use mocked services (faster, no Azure costs)
python tests/run_all_tests.py --mock

# Use real services (slower, requires Azure setup)
python tests/run_all_tests.py --real

# Run both mock and real tests
python tests/run_all_tests.py --real --mock
```

### Django Test Runner

For traditional Django testing:

```bash
# Run all Django tests
python manage.py test

# Run specific app tests
python manage.py test konveyor.apps.documents

# Run specific test class
python manage.py test konveyor.apps.documents.tests.test_document_service.TestDocumentServiceIntegration

# Run with verbosity
python manage.py test --verbosity=2
```

### Pytest Direct Usage

```bash
# Run with pytest directly
pytest tests/unit/ -v

# Run with coverage
pytest tests/unit/ --cov=konveyor --cov-report=html

# Run specific test
pytest tests/real/test_slack_integration.py::TestSlackIntegration::test_message_handling -v
```

## ✍️ Writing Tests

### Unit Test Example

```python
# tests/unit/test_document_service.py
import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase

from konveyor.core.services.document_service import DocumentService
from konveyor.core.exceptions import DocumentProcessingError

class TestDocumentService(TestCase):
    """Unit tests for DocumentService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_storage = Mock()
        self.mock_search = Mock()
        self.mock_ai_client = Mock()
        
        self.service = DocumentService(
            storage_client=self.mock_storage,
            search_client=self.mock_search,
            ai_client=self.mock_ai_client
        )
    
    def test_process_document_success(self):
        """Test successful document processing."""
        # Arrange
        mock_file = Mock()
        mock_file.name = "test.pdf"
        mock_file.size = 1024
        mock_file.read.return_value = b"test content"
        
        self.mock_storage.upload_blob.return_value = "blob_url"
        self.mock_ai_client.extract_text.return_value = "extracted text"
        
        # Act
        result = self.service.process_document(mock_file, "test.pdf")
        
        # Assert
        self.assertIsNotNone(result)
        self.assertEqual(result.filename, "test.pdf")
        self.mock_storage.upload_blob.assert_called_once()
        self.mock_ai_client.extract_text.assert_called_once()
    
    def test_process_document_invalid_file(self):
        """Test processing with invalid file."""
        # Act & Assert
        with self.assertRaises(DocumentProcessingError) as context:
            self.service.process_document(None, "test.pdf")
        
        self.assertIn("File object is required", str(context.exception))
    
    @patch('konveyor.core.services.document_service.logger')
    def test_process_document_logs_error(self, mock_logger):
        """Test that errors are properly logged."""
        # Arrange
        mock_file = Mock()
        mock_file.name = "test.pdf"
        self.mock_storage.upload_blob.side_effect = Exception("Storage error")
        
        # Act
        with self.assertRaises(DocumentProcessingError):
            self.service.process_document(mock_file, "test.pdf")
        
        # Assert
        mock_logger.error.assert_called()
        self.assertIn("Storage error", str(mock_logger.error.call_args))
```

### Integration Test Example

```python
# tests/integration/test_document_workflow.py
import pytest
from unittest.mock import patch, Mock
from django.test import TransactionTestCase
from rest_framework.test import APIClient

from konveyor.apps.documents.models import Document

class TestDocumentWorkflow(TransactionTestCase):
    """Integration tests for document processing workflow."""
    
    def setUp(self):
        """Set up test client and mocks."""
        self.client = APIClient()
        self.upload_url = '/documents/upload/'
    
    @patch('konveyor.core.services.azure_client_manager.AzureClientManager')
    def test_document_upload_workflow(self, mock_azure_manager):
        """Test complete document upload workflow with mocked Azure services."""
        # Arrange
        mock_storage = Mock()
        mock_search = Mock()
        mock_ai = Mock()
        
        mock_azure_manager.return_value.get_storage_client.return_value = mock_storage
        mock_azure_manager.return_value.get_search_client.return_value = mock_search
        mock_azure_manager.return_value.get_ai_client.return_value = mock_ai
        
        mock_storage.upload_blob.return_value = "https://storage.blob.core.windows.net/test.pdf"
        mock_ai.extract_text.return_value = "Extracted document content"
        mock_search.index_document.return_value = {"status": "success"}
        
        # Create test file
        test_content = b"This is a test PDF content"
        
        # Act
        with patch('builtins.open', create=True) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = test_content
            
            response = self.client.post(
                self.upload_url,
                {
                    'file': Mock(name='test.pdf', size=len(test_content)),
                    'title': 'Test Document'
                },
                format='multipart'
            )
        
        # Assert
        self.assertEqual(response.status_code, 200)
        self.assertIn('id', response.json())
        
        # Verify document was created
        doc_id = response.json()['id']
        self.assertTrue(Document.objects.filter(id=doc_id).exists())
        
        # Verify Azure services were called
        mock_storage.upload_blob.assert_called_once()
        mock_ai.extract_text.assert_called_once()
        mock_search.index_document.assert_called_once()
```

### Real Service Test Example

```python
# tests/real/test_azure_integration.py
import pytest
from django.test import TransactionTestCase
from django.conf import settings

from konveyor.core.services.azure_client_manager import AzureClientManager
from konveyor.core.services.document_service import DocumentService

@pytest.mark.integration
class TestAzureIntegration(TransactionTestCase):
    """Real Azure service integration tests."""
    
    @classmethod
    def setUpClass(cls):
        """Set up Azure clients for real testing."""
        super().setUpClass()
        
        # Skip if Azure credentials not available
        if not all([
            settings.AZURE_OPENAI_ENDPOINT,
            settings.AZURE_OPENAI_API_KEY,
            settings.AZURE_SEARCH_ENDPOINT,
            settings.AZURE_SEARCH_API_KEY
        ]):
            pytest.skip("Azure credentials not configured")
        
        cls.azure_manager = AzureClientManager()
        cls.document_service = DocumentService()
    
    def test_azure_openai_connection(self):
        """Test connection to Azure OpenAI service."""
        # Arrange
        client = self.azure_manager.get_openai_client()
        
        # Act
        response = client.chat.completions.create(
            model="gpt-35-turbo",
            messages=[{"role": "user", "content": "Hello, Azure!"}],
            max_tokens=10
        )
        
        # Assert
        self.assertIsNotNone(response)
        self.assertIsNotNone(response.choices)
        self.assertTrue(len(response.choices) > 0)
        self.assertIsNotNone(response.choices[0].message.content)
    
    def test_azure_search_connection(self):
        """Test connection to Azure Cognitive Search."""
        # Arrange
        client = self.azure_manager.get_search_client()
        
        # Act
        try:
            # Try to get index statistics
            result = client.get_index_statistics()
            connection_successful = True
        except Exception as e:
            connection_successful = False
            error_message = str(e)
        
        # Assert
        self.assertTrue(
            connection_successful, 
            f"Azure Search connection failed: {error_message if not connection_successful else ''}"
        )
    
    def test_document_processing_end_to_end(self):
        """Test complete document processing with real Azure services."""
        # Arrange
        test_content = "This is a test document for Azure integration testing."
        test_filename = "azure_test_document.txt"
        
        # Create a temporary file-like object
        from io import BytesIO
        file_obj = BytesIO(test_content.encode('utf-8'))
        file_obj.name = test_filename
        
        # Act
        try:
            document = self.document_service.process_document(file_obj, test_filename)
            
            # Assert
            self.assertIsNotNone(document)
            self.assertEqual(document.filename, test_filename)
            self.assertTrue(document.processed)
            
            # Verify document can be found in search
            search_results = self.document_service.search_documents(
                query="test document Azure integration",
                top=5
            )
            
            # Should find our test document
            document_found = any(
                result.document_id == document.id 
                for result in search_results
            )
            self.assertTrue(document_found, "Document not found in search results")
            
        finally:
            # Cleanup: Remove test document
            if 'document' in locals():
                try:
                    self.document_service.delete_document(document.id)
                except Exception:
                    pass  # Ignore cleanup errors
```

### Test Fixtures and Utilities

```python
# tests/conftest.py
import pytest
from django.conf import settings
from unittest.mock import Mock

@pytest.fixture
def api_client():
    """Provide API client for tests."""
    from rest_framework.test import APIClient
    return APIClient()

@pytest.fixture
def sample_document():
    """Provide sample document data."""
    return {
        'title': 'Test Document',
        'content': 'This is test content for document processing.',
        'category': 'test',
        'filename': 'test_document.txt'
    }

@pytest.fixture
def mock_azure_clients():
    """Provide mocked Azure clients."""
    mock_storage = Mock()
    mock_search = Mock()
    mock_openai = Mock()
    
    # Configure default return values
    mock_storage.upload_blob.return_value = "https://storage.example.com/test.pdf"
    mock_search.index_document.return_value = {"status": "success"}
    mock_openai.chat.completions.create.return_value = Mock(
        choices=[Mock(message=Mock(content="Test response"))]
    )
    
    return {
        'storage': mock_storage,
        'search': mock_search,
        'openai': mock_openai
    }

@pytest.fixture
def real_azure_clients():
    """Provide real Azure clients (skip if not configured)."""
    if not all([
        settings.AZURE_OPENAI_ENDPOINT,
        settings.AZURE_SEARCH_ENDPOINT
    ]):
        pytest.skip("Azure services not configured")
    
    from konveyor.core.services.azure_client_manager import AzureClientManager
    return AzureClientManager()

@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Allow database access for all tests."""
    pass
```

## 📊 Test Categories

### Unit Tests (`tests/unit/`)

**Purpose**: Test individual components in isolation  
**Dependencies**: None (all external services mocked)  
**Speed**: Fast (< 1 second per test)  
**Coverage**: Business logic, utilities, models

```bash
# Run unit tests
python tests/run_all_tests.py --category unit --mock
```

**Example Test Areas**:
- Model validation and methods
- Service class business logic
- Utility functions
- Data transformations
- Error handling

### Integration Tests (`tests/integration/`)

**Purpose**: Test component interactions with mocked external services  
**Dependencies**: Mocked Azure services  
**Speed**: Medium (1-5 seconds per test)  
**Coverage**: API endpoints, workflows, error scenarios

```bash
# Run integration tests
python tests/run_all_tests.py --category integration --mock
```

**Example Test Areas**:
- API endpoint responses
- Service integration workflows
- Error handling across components
- Authentication and authorization
- Request/response validation

### Real Service Tests (`tests/real/`)

**Purpose**: Test with actual Azure and Slack services  
**Dependencies**: Real Azure services, Slack workspace  
**Speed**: Slow (5-30 seconds per test)  
**Coverage**: End-to-end workflows, service configurations

```bash
# Run real service tests
python tests/run_all_tests.py --category real --real
```

**Example Test Areas**:
- Document upload and processing
- Search indexing and retrieval
- Slack message handling
- Azure service connectivity
- Performance validation

### Specialized Test Categories

#### Search Tests (`--category search`)
```bash
python tests/run_all_tests.py --category search --real
```
- Azure Cognitive Search integration
- Document indexing workflows
- Search query processing
- Vector search functionality

#### Document Tests (`--category document`)
```bash
python tests/run_all_tests.py --category document --real
```
- Document parsing and processing
- Azure Document Intelligence integration
- File format support
- Content extraction validation

#### Slack Tests (`--category slack`)
```bash
python tests/run_all_tests.py --category slack --real
```
- Slack webhook handling
- Message processing
- User interaction flows
- Bot response formatting

## 🔄 CI/CD Integration

### GitHub Actions Workflow

Tests are automatically run on:
- **Pull Requests**: All unit and integration tests
- **Push to main/develop**: Full test suite including real services
- **Manual Trigger**: Configurable test categories and environments

### Workflow Configuration

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  pull_request:
    branches: [main, develop]
  push:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Test type to run'
        required: true
        default: 'mock'
        type: choice
        options:
        - mock
        - real
        - both
      environment:
        description: 'Environment to test against'
        required: true
        default: 'test'
        type: choice
        options:
        - dev
        - test
        - prod
      category:
        description: 'Test category'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - unit
        - integration
        - real
        - search
        - document
        - slack
```

### Test Results and Coverage

```bash
# Generate coverage report
python tests/run_all_tests.py --category unit --mock
coverage run --source='.' manage.py test
coverage report --show-missing
coverage html  # Generate HTML report
```

### Docker Test Environment

```bash
# Run tests in Docker
docker build -t konveyor-test .
docker run --env-file .env.test konveyor-test python tests/run_all_tests.py
```

## 🐛 Debugging Tests

### Common Test Failures

#### Azure Service Connection Issues

```bash
# Test Azure connectivity
python -c "
from konveyor.core.services.azure_client_manager import AzureClientManager
manager = AzureClientManager()
try:
    client = manager.get_openai_client()
    print('Azure OpenAI: Connected')
except Exception as e:
    print(f'Azure OpenAI: Failed - {e}')
"
```

#### Environment Variable Issues

```bash
# Check required environment variables
python -c "
import os
required_vars = [
    'AZURE_OPENAI_ENDPOINT',
    'AZURE_OPENAI_API_KEY',
    'AZURE_SEARCH_ENDPOINT',
    'AZURE_SEARCH_API_KEY'
]
missing = [var for var in required_vars if not os.getenv(var)]
if missing:
    print(f'Missing variables: {missing}')
else:
    print('All required variables set')
"
```

#### Database Issues

```bash
# Reset test database
python manage.py migrate --run-syncdb
python manage.py flush --noinput
```

### Debug Mode

```bash
# Run tests with debug output
python tests/run_all_tests.py --category unit --verbose

# Run specific test with pdb
python -m pytest tests/unit/test_document_service.py::TestDocumentService::test_process_document_success -v -s --pdb
```

### Logging Configuration

```python
# Enable debug logging for tests
import logging
logging.basicConfig(level=logging.DEBUG)

# Or in test settings
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG',
        },
    },
    'loggers': {
        'konveyor': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## 🎯 Best Practices

### Test Organization

1. **Follow naming conventions**: `test_*.py` for files, `test_*` for functions
2. **Group related tests**: Use test classes to group related functionality
3. **Use descriptive names**: Test names should describe what they're testing
4. **Organize by functionality**: Group tests by the feature they're testing

### Test Data Management

```python
# Use factories for test data
import factory
from factory.django import DjangoModelFactory

class DocumentFactory(DjangoModelFactory):
    class Meta:
        model = Document
    
    title = factory.Sequence(lambda n: f"Test Document {n}")
    filename = factory.LazyAttribute(lambda obj: f"{obj.title.lower().replace(' ', '_')}.pdf")
    content_type = "application/pdf"
    processed = True

# Use in tests
def test_document_search():
    # Create test documents
    doc1 = DocumentFactory(title="Python Guide")
    doc2 = DocumentFactory(title="Django Tutorial")
    
    # Test search functionality
    results = search_documents("Python")
    assert doc1.id in [r.document_id for r in results]
```

### Mocking Best Practices

```python
# Mock at the right level
@patch('konveyor.core.services.document_service.AzureClientManager')
def test_document_processing(mock_azure_manager):
    # Mock the manager, not individual clients
    mock_manager = mock_azure_manager.return_value
    mock_manager.get_storage_client.return_value = Mock()
    
    # Test the service
    service = DocumentService()
    result = service.process_document(mock_file, "test.pdf")
    
    # Verify interactions
    mock_manager.get_storage_client.assert_called_once()
```

### Test Isolation

```python
# Ensure tests don't affect each other
class TestDocumentService(TestCase):
    def setUp(self):
        """Set up fresh state for each test."""
        self.test_id = uuid.uuid4().hex
        self.service = DocumentService()
    
    def tearDown(self):
        """Clean up after each test."""
        # Remove any test documents created
        Document.objects.filter(title__contains=self.test_id).delete()
```

### Performance Testing

```python
import time
import pytest

@pytest.mark.slow
def test_document_processing_performance():
    """Test that document processing completes within acceptable time."""
    start_time = time.time()
    
    # Process document
    result = process_large_document()
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # Assert performance requirement
    assert processing_time < 30.0, f"Processing took {processing_time:.2f}s, expected < 30s"
```

### Error Testing

```python
def test_error_handling():
    """Test that errors are handled gracefully."""
    with pytest.raises(DocumentProcessingError) as exc_info:
        service.process_document(invalid_file)
    
    # Verify error details
    assert "Invalid file format" in str(exc_info.value)
    assert exc_info.value.error_code == "INVALID_FILE_FORMAT"
```

### Test Documentation

```python
def test_document_search_with_filters():
    """Test document search with category and date filters.
    
    This test verifies that:
    1. Documents can be filtered by category
    2. Date range filters work correctly
    3. Results are properly ranked by relevance
    4. Pagination works as expected
    
    Test data:
    - Creates 5 documents in 'technical' category
    - Creates 3 documents in 'general' category
    - Tests various filter combinations
    """
    # Test implementation
    pass
```

## 📈 Test Metrics and Reporting

### Coverage Requirements

- **Unit Tests**: Minimum 80% code coverage
- **Integration Tests**: Cover all API endpoints
- **Real Tests**: Cover critical user workflows

### Test Reporting

```bash
# Generate comprehensive test report
python tests/run_all_tests.py --category all --verbose > test_report.txt

# Generate coverage report
coverage run --source='.' manage.py test
coverage report --show-missing > coverage_report.txt
coverage html  # HTML report in htmlcov/
```

### Performance Benchmarks

```python
# Track test performance
@pytest.mark.benchmark
def test_search_performance(benchmark):
    """Benchmark search performance."""
    result = benchmark(search_documents, "test query", top=10)
    assert len(result) <= 10
```

---

**Next**: [Development Setup Guide](development-setup.md) | [Contributing Guidelines](contributing.md)

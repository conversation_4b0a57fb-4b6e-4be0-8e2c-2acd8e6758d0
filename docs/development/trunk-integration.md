# Trunk Integration

This project uses [Trunk](https://trunk.io) as a complementary code quality tool alongside our existing pre-commit hooks. This setup provides additional security scanning and infrastructure linting capabilities while maintaining our proven Python development workflow.

## Overview

**Trunk** is configured to provide capabilities that complement (not replace) our existing pre-commit setup:

### What Trunk Adds
- **Security Scanning**: Python security (bandit), vulnerability scanning (osv-scanner), secret detection (trufflehog)
- **Infrastructure Linting**: Terraform (tflint), Docker (hadolint), Infrastructure security (checkov)
- **Shell Scripts**: Linting (shellcheck) and formatting (shfmt)
- **Documentation**: Markdown linting, asset optimization
- **GitHub Actions**: Workflow validation (actionlint)

### What Pre-commit Handles
- **Python**: Linting and formatting (ruff), type checking (mypy)
- **Basic Checks**: Trailing whitespace, end-of-file fixes, YAML validation
- **Git Hooks**: Automatic execution on commit

## Installation

1. Install Trunk CLI:
```bash
curl https://get.trunk.io -fsSL | bash
```

2. Initialize Trunk (already configured):
```bash
trunk init
```

## Usage

### Run All Trunk Checks
```bash
trunk check
```

### Run Specific Categories
```bash
# Security scanning only
trunk check --filter=bandit,osv-scanner,trufflehog

# Infrastructure only
trunk check --filter=tflint,hadolint,checkov

# Documentation only
trunk check --filter=markdownlint
```

### Auto-fix Issues
```bash
trunk fmt
```

## Integration with Existing Workflow

### Local Development
1. **Pre-commit hooks** run automatically on `git commit` (Python, basic checks)
2. **Trunk checks** run manually or in CI for security/infrastructure validation

### CI/CD Integration
Trunk checks can be added to GitHub Actions as an additional quality gate:

```yaml
- name: Trunk Check
  uses: trunk-io/trunk-action@v1
  with:
    check-mode: all
```

**Note**: CI integration requires workflow permissions and should be added separately by repository administrators.

## Configuration

The configuration is in `.trunk/trunk.yaml` and is designed to:
- **Avoid conflicts** with existing pre-commit tools
- **Add new capabilities** without duplicating existing functionality
- **Maintain simplicity** for easy maintenance

### Disabled Tools
These tools are disabled in Trunk to avoid conflicts:
- `black`, `isort`, `ruff` (handled by pre-commit)
- `mypy` (handled by pre-commit)

### File Exclusions
Trunk respects the same exclusion patterns as pre-commit for consistency.

## Troubleshooting

### Common Issues

1. **Tool conflicts**: If you see formatting conflicts, ensure the disabled tools list is up to date
2. **Performance**: Trunk caches results, but initial runs may be slow
3. **False positives**: Use `.trunk/configs/` to customize tool configurations

### Getting Help
```bash
trunk --help
trunk check --help
```

## Migration Path

This setup allows for gradual adoption:

1. **Phase 1** (Current): Use Trunk for security/infrastructure, keep pre-commit for Python
2. **Phase 2** (Optional): Evaluate moving Python tools to Trunk if team prefers unified interface
3. **Phase 3** (Optional): Full migration to Trunk if desired

The current configuration ensures no breaking changes to existing workflows while adding valuable new capabilities.

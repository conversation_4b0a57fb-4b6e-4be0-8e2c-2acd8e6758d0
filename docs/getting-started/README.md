# 🚀 Getting Started with Konveyor

Welcome to Konveyor! This guide will help you get up and running with the AI-powered knowledge transfer agent in just a few minutes.

## 📋 Prerequisites

Before you begin, ensure you have:

- **Python 3.10+** installed on your system
- **Azure account** with appropriate permissions
- **Slack workspace** with admin privileges (for Slack integration)
- **Git** for cloning the repository

## 🎯 Quick Start Options

Choose your preferred way to get started:

### 🏃‍♂️ 5-Minute Quick Start
Perfect for trying out Konveyor quickly.
👉 **[Quick Start Guide](quick-start.md)**

### 📚 Complete Installation
For a full setup with all features.
👉 **[Installation Guide](installation.md)**

### 🎓 First-Time Setup
New to AI agents? Start here.
👉 **[First Steps Guide](first-steps.md)**

## 🗺️ Learning Path

We recommend following this learning path:

1. **[Installation](installation.md)** - Set up Konveyor locally
2. **[Quick Start](quick-start.md)** - Try basic features
3. **[First Steps](first-steps.md)** - Configure for your needs
4. **[User Guide](../user-guide/)** - Learn all features
5. **[Tutorials](../tutorials/)** - Advanced use cases

## 🆘 Need Help?

- **Installation Issues**: Check [Troubleshooting](../user-guide/troubleshooting.md)
- **Configuration Questions**: See [Configuration Reference](../reference/configuration.md)
- **General Questions**: Visit our [User Guide](../user-guide/)

## 🎯 What You'll Learn

By the end of this section, you'll be able to:

- ✅ Install and configure Konveyor
- ✅ Connect to Azure services
- ✅ Set up Slack integration
- ✅ Ask your first questions to the AI agent
- ✅ Navigate the documentation system

## 🔄 Next Steps

After completing the getting started guide:

- Explore **[Features](../user-guide/features/)** to learn about specific capabilities
- Try **[Tutorials](../tutorials/)** for hands-on learning
- Read the **[User Guide](../user-guide/)** for comprehensive usage information

---

**Ready to begin?** 👉 [Start with Installation](installation.md)

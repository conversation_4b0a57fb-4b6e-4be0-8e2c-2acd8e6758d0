# 📦 Installation Guide

This guide walks you through installing Konveyor on your local machine or server.

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.10 or higher
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space
- **OS**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)

### Cloud Requirements
- **Azure Subscription** with the following services:
  - Azure OpenAI Service
  - Azure Cognitive Search
  - Azure Blob Storage
  - Azure Key Vault
  - Azure Database for PostgreSQL (optional)

## 🚀 Installation Steps

### Step 1: Clone the Repository

```bash
git clone https://github.com/sdamache/konveyor.git
cd konveyor
```

### Step 2: Set Up Python Environment

#### Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

#### Using Conda (Alternative)
```bash
# Create conda environment
conda create -n konveyor python=3.10
conda activate konveyor
```

### Step 3: Install Dependencies

```bash
# Install production dependencies
pip install -r requirements.txt

# For development (includes testing and linting tools)
pip install -r requirements/development.txt
```

### Step 4: Configure Environment Variables

```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file with your configuration:

```bash
# Azure Configuration
AZURE_OPENAI_ENDPOINT=https://your-openai-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key-here
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo
AZURE_OPENAI_API_VERSION=2024-12-01-preview

# Azure Cognitive Search
AZURE_SEARCH_ENDPOINT=https://your-search-service.search.windows.net
AZURE_SEARCH_API_KEY=your-search-api-key

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=your-storage-connection-string

# Database (optional - uses SQLite by default)
DATABASE_URL=postgresql://user:password@localhost:5432/konveyor

# Slack Integration (optional)
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
```

### Step 5: Initialize the Database

```bash
# Run database migrations
python manage.py migrate

# Create a superuser (optional)
python manage.py createsuperuser
```

### Step 6: Verify Installation

```bash
# Start the development server
python manage.py runserver

# In another terminal, test the health endpoint
curl http://localhost:8000/healthz/
```

You should see a response indicating the service is healthy.

## 🔧 Azure Services Setup

### Azure OpenAI Service

1. **Create Azure OpenAI Resource**:
   ```bash
   az cognitiveservices account create \
     --name your-openai-resource \
     --resource-group your-resource-group \
     --kind OpenAI \
     --sku S0 \
     --location eastus
   ```

2. **Deploy Models**:
   - Deploy `gpt-35-turbo` or `gpt-4` for chat
   - Deploy `text-embedding-ada-002` for embeddings

### Azure Cognitive Search

1. **Create Search Service**:
   ```bash
   az search service create \
     --name your-search-service \
     --resource-group your-resource-group \
     --sku Standard \
     --location eastus
   ```

2. **Configure Search Index**:
   ```bash
   # Run the search setup script
   python manage.py setup_search_index
   ```

### Azure Blob Storage

1. **Create Storage Account**:
   ```bash
   az storage account create \
     --name yourstorageaccount \
     --resource-group your-resource-group \
     --location eastus \
     --sku Standard_LRS
   ```

2. **Create Container**:
   ```bash
   az storage container create \
     --name documents \
     --account-name yourstorageaccount
   ```

## 🐳 Docker Installation (Alternative)

For a containerized setup:

```bash
# Build the Docker image
docker build -t konveyor .

# Run with environment variables
docker run -p 8000:8000 --env-file .env konveyor
```

Or use Docker Compose:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

## ✅ Verification Checklist

After installation, verify these components work:

- [ ] **Web Server**: `curl http://localhost:8000/healthz/` returns 200
- [ ] **Azure OpenAI**: Test chat completion
- [ ] **Azure Search**: Search index is accessible
- [ ] **Database**: Migrations completed successfully
- [ ] **Environment**: All required variables are set

### Test Azure Connections

```bash
# Test Azure services connectivity
python manage.py test_azure_connections
```

## 🚨 Troubleshooting

### Common Issues

#### Python Version Error
```bash
# Check Python version
python --version
# Should be 3.10 or higher
```

#### Azure Authentication Error
```bash
# Verify Azure CLI is logged in
az account show

# Login if needed
az login
```

#### Database Connection Error
```bash
# For PostgreSQL, ensure service is running
sudo systemctl status postgresql

# For SQLite, check file permissions
ls -la db.sqlite3
```

#### Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](../user-guide/troubleshooting.md)
2. Review the [Configuration Reference](../reference/configuration.md)
3. Look at [Common Issues](../user-guide/troubleshooting.md#common-issues)

## 🎯 Next Steps

After successful installation:

1. **[Quick Start](quick-start.md)** - Try basic features
2. **[First Steps](first-steps.md)** - Configure for your needs
3. **[Slack Integration](../user-guide/slack-integration.md)** - Set up Slack bot

---

**Installation complete?** 👉 [Continue to Quick Start](quick-start.md)

# ⚡ Quick Start Guide

Get Konveyor up and running in 5 minutes! This guide assumes you've completed the [installation](installation.md).

## 🎯 What You'll Accomplish

In this quick start, you'll:
- ✅ Start the Konveyor service
- ✅ Upload your first document
- ✅ Ask your first question
- ✅ See the AI agent in action

## 🚀 Step 1: Start Konveyor

```bash
# Activate your virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the development server
python manage.py runserver
```

You should see:
```
Starting development server at http://127.0.0.1:8000/
```

## 📄 Step 2: Upload a Document

Let's add some documentation for the AI to learn from:

### Option A: Using the Web Interface

1. Open your browser to `http://localhost:8000`
2. Navigate to the Documents section
3. Click "Upload Document"
4. Upload a markdown file or PDF

### Option B: Using the API

```bash
# Upload a document via API
curl -X POST http://localhost:8000/documents/upload/ \
  -F "file=@your-document.md" \
  -F "title=Getting Started Guide"
```

### Option C: Use Sample Documents

```bash
# Create a sample document
cat > sample-doc.md << 'EOF'
# Welcome to Our Project

## Getting Started
This is a sample document to test Konveyor.

## Features
- Document search
- Code understanding
- Knowledge analysis

## FAQ
Q: How do I get started?
A: Follow the installation guide and upload some documents.
EOF

# Upload the sample document
curl -X POST http://localhost:8000/documents/upload/ \
  -F "file=@sample-doc.md" \
  -F "title=Sample Documentation"
```

## 🤖 Step 3: Ask Your First Question

Now let's interact with the AI agent:

### Option A: Using the Web Interface

1. Go to `http://localhost:8000/chat/`
2. Type your question: "How do I get started with this project?"
3. Press Enter and watch the AI respond!

### Option B: Using the API

```bash
# Ask a question via API
curl -X POST http://localhost:8000/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{
    "message": "How do I get started with this project?",
    "conversation_id": "quick-start-test"
  }'
```

### Option C: Using Slack (if configured)

If you've set up Slack integration:

1. Go to your Slack workspace
2. Find the Konveyor bot
3. Send a message: "How do I get started?"

## 🎉 Step 4: Explore the Response

The AI agent should respond with information from your uploaded document. You'll notice:

- **Contextual answers** based on your documentation
- **Source citations** showing where information came from
- **Follow-up suggestions** for related questions

## 🧪 Try These Sample Questions

Test different types of queries:

```bash
# Documentation questions
"What features are available?"
"Where can I find the FAQ?"

# Code-related questions (if you have code in your docs)
"How does the authentication work?"
"What are the main components?"

# General questions
"What is this project about?"
"How do I contribute?"
```

## 📊 Step 5: Check the Results

### View Search Results
```bash
# Check what documents were indexed
curl http://localhost:8000/api/documents/
```

### View Conversation History
```bash
# See your conversation history
curl http://localhost:8000/api/conversations/
```

### Monitor Logs
```bash
# In another terminal, watch the logs
tail -f logs/konveyor.log
```

## 🎯 Understanding the Response

When you ask a question, Konveyor:

1. **Analyzes your question** using natural language processing
2. **Searches relevant documents** using semantic search
3. **Generates a contextual response** using Azure OpenAI
4. **Provides source citations** for transparency
5. **Suggests follow-up questions** to help you learn more

## 🔧 Quick Configuration

### Adjust Response Style

Edit your `.env` file to customize responses:

```bash
# Make responses more detailed
RESPONSE_STYLE=detailed

# Focus on code explanations
FOCUS_AREA=code

# Adjust response length
MAX_RESPONSE_LENGTH=500
```

### Add More Documents

```bash
# Upload multiple documents at once
for file in docs/*.md; do
  curl -X POST http://localhost:8000/documents/upload/ \
    -F "file=@$file" \
    -F "title=$(basename $file .md)"
done
```

## ✅ Success Indicators

You'll know everything is working when:

- ✅ The web interface loads without errors
- ✅ Documents upload successfully
- ✅ The AI responds to questions with relevant information
- ✅ Source citations point to your uploaded documents
- ✅ Response time is under 5 seconds

## 🚨 Quick Troubleshooting

### AI Not Responding?
```bash
# Check Azure OpenAI connection
python manage.py test_azure_connections
```

### No Search Results?
```bash
# Rebuild search index
python manage.py rebuild_search_index
```

### Slow Responses?
```bash
# Check system resources
top
# Look for high CPU/memory usage
```

## 🎯 Next Steps

Now that you have Konveyor running:

1. **[First Steps Guide](first-steps.md)** - Configure for your specific needs
2. **[User Guide](../user-guide/)** - Learn about all features
3. **[Slack Integration](../user-guide/slack-integration.md)** - Set up team access
4. **[Tutorials](../tutorials/)** - Try advanced use cases

## 💡 Pro Tips

- **Start small**: Upload 5-10 documents initially
- **Use clear questions**: Specific questions get better answers
- **Check citations**: Always verify the AI's sources
- **Iterate**: Refine your questions based on responses
- **Monitor usage**: Keep an eye on Azure costs

## 🆘 Need Help?

- **Not working as expected?** Check [Troubleshooting](../user-guide/troubleshooting.md)
- **Want to customize?** See [Configuration](../reference/configuration.md)
- **Ready for production?** Read [Deployment Guide](../developer-guide/deployment.md)

---

**Quick start complete!** 👉 [Learn more in First Steps](first-steps.md)

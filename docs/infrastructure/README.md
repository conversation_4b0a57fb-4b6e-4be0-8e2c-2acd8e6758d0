# 🏗️ Infrastructure Documentation

This section covers deployment, infrastructure management, and operations for Konveyor.

## 📚 Documentation Structure

### ☁️ Cloud Setup
- **[Azure Setup](azure-setup.md)** - Complete Azure services configuration
- **[Terraform Deployment](terraform-deployment.md)** - Infrastructure as Code
- **[Environment Configuration](environment-config.md)** - Environment-specific settings

### 🚀 Deployment
- **[Production Deployment](production-deployment.md)** - Production deployment guide
- **[Staging Environment](staging-environment.md)** - Staging setup and testing
- **[Local Development](local-development.md)** - Local infrastructure setup

### 📊 Operations
- **[Monitoring](monitoring.md)** - Observability and monitoring setup
- **[Logging](logging.md)** - Centralized logging configuration
- **[Backup and Recovery](backup-recovery.md)** - Data protection strategies

### 🔒 Security
- **[Security Configuration](security.md)** - Security best practices
- **[Access Control](access-control.md)** - Identity and access management
- **[Compliance](compliance.md)** - Compliance and audit requirements

## 🎯 Quick Start

### For New Deployments
1. **[Azure Setup](azure-setup.md)** - Set up Azure services
2. **[Terraform Deployment](terraform-deployment.md)** - Deploy infrastructure
3. **[Production Deployment](production-deployment.md)** - Deploy application

### For Existing Deployments
1. **[Monitoring](monitoring.md)** - Set up observability
2. **[Security Configuration](security.md)** - Harden security
3. **[Backup and Recovery](backup-recovery.md)** - Protect data

## 🏗️ Architecture Overview

Konveyor's infrastructure follows cloud-native principles:

```mermaid
graph TB
    subgraph "Azure Cloud"
        subgraph "Compute"
            AppService[App Service]
            Functions[Azure Functions]
        end
        
        subgraph "AI Services"
            OpenAI[Azure OpenAI]
            CogSearch[Cognitive Search]
        end
        
        subgraph "Storage"
            BlobStorage[Blob Storage]
            PostgreSQL[PostgreSQL]
        end
        
        subgraph "Security"
            KeyVault[Key Vault]
            AAD[Azure AD]
        end
        
        subgraph "Monitoring"
            AppInsights[Application Insights]
            Monitor[Azure Monitor]
        end
    end
    
    subgraph "External"
        Slack[Slack API]
        GitHub[GitHub]
    end
    
    AppService --> OpenAI
    AppService --> CogSearch
    AppService --> BlobStorage
    AppService --> PostgreSQL
    AppService --> KeyVault
    AppService --> Slack
    
    AppInsights --> Monitor
    AppService --> AppInsights
```

## 🛠️ Infrastructure Components

### Core Services
- **Azure App Service**: Hosts the Django application
- **Azure OpenAI**: Provides AI capabilities
- **Azure Cognitive Search**: Vector search and indexing
- **Azure Blob Storage**: Document and file storage
- **Azure Database for PostgreSQL**: Primary database

### Supporting Services
- **Azure Key Vault**: Secrets and certificate management
- **Azure Application Insights**: Application monitoring
- **Azure Monitor**: Infrastructure monitoring
- **Azure Active Directory**: Identity and access management

### Development Tools
- **Terraform**: Infrastructure as Code
- **GitHub Actions**: CI/CD pipeline
- **Docker**: Containerization
- **Azure CLI**: Command-line management

## 📋 Prerequisites

### Azure Requirements
- Azure subscription with appropriate permissions
- Resource group for Konveyor resources
- Service principal for automation (optional)

### Tools Required
- Azure CLI 2.50+
- Terraform 1.5+
- Docker 20.10+
- Python 3.10+

### Permissions Needed
- Contributor role on resource group
- User Access Administrator (for role assignments)
- Key Vault Administrator (for secrets management)

## 🚀 Deployment Options

### 1. Automated Deployment (Recommended)
```bash
# Clone repository
git clone https://github.com/sdamache/konveyor.git
cd konveyor

# Run automated setup
./scripts/deploy.sh --environment production
```

### 2. Manual Deployment
```bash
# Set up Azure services
az group create --name konveyor-rg --location eastus

# Deploy with Terraform
cd terraform
terraform init
terraform plan
terraform apply

# Deploy application
az webapp deployment source config-zip \
  --resource-group konveyor-rg \
  --name konveyor-app \
  --src konveyor.zip
```

### 3. Container Deployment
```bash
# Build container
docker build -t konveyor:latest .

# Push to registry
docker tag konveyor:latest your-registry/konveyor:latest
docker push your-registry/konveyor:latest

# Deploy to App Service
az webapp config container set \
  --name konveyor-app \
  --resource-group konveyor-rg \
  --docker-custom-image-name your-registry/konveyor:latest
```

## 📊 Environment Management

### Development Environment
- Local SQLite database
- Azure services in development tier
- Debug logging enabled
- Hot reload for development

### Staging Environment
- Production-like configuration
- Separate Azure resources
- Integration testing
- Performance testing

### Production Environment
- High availability configuration
- Production-grade Azure services
- Comprehensive monitoring
- Automated backups

## 🔧 Configuration Management

### Environment Variables
```bash
# Core application settings
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-secret-key

# Azure configuration
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;...

# Database configuration
DATABASE_URL=********************************/konveyor

# Monitoring configuration
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=...
```

### Secrets Management
```bash
# Store secrets in Key Vault
az keyvault secret set \
  --vault-name konveyor-keyvault \
  --name "database-password" \
  --value "secure-password"

# Reference in application
DATABASE_PASSWORD=$(az keyvault secret show \
  --vault-name konveyor-keyvault \
  --name "database-password" \
  --query "value" -o tsv)
```

## 📈 Scaling Considerations

### Horizontal Scaling
- App Service can scale out to multiple instances
- Load balancer distributes traffic
- Stateless application design

### Vertical Scaling
- Increase App Service plan size
- Scale Azure OpenAI throughput
- Upgrade database tier

### Auto-scaling Rules
```bash
# CPU-based scaling
az monitor autoscale create \
  --resource-group konveyor-rg \
  --resource konveyor-app \
  --resource-type Microsoft.Web/serverfarms \
  --name cpu-autoscale \
  --min-count 1 \
  --max-count 10 \
  --count 2

# Add scaling rule
az monitor autoscale rule create \
  --resource-group konveyor-rg \
  --autoscale-name cpu-autoscale \
  --condition "Percentage CPU > 70 avg 5m" \
  --scale out 1
```

## 🔒 Security Best Practices

### Network Security
- Use private endpoints for Azure services
- Configure Web Application Firewall
- Implement network security groups

### Identity and Access
- Use managed identities where possible
- Implement least privilege access
- Regular access reviews

### Data Protection
- Enable encryption at rest
- Use TLS 1.3 for data in transit
- Implement data classification

## 📊 Monitoring and Alerting

### Key Metrics
- Application response time
- Error rates and exceptions
- Azure service health
- Resource utilization

### Alert Configuration
```bash
# Create action group
az monitor action-group create \
  --resource-group konveyor-rg \
  --name konveyor-alerts \
  --short-name alerts

# Create alert rule
az monitor metrics alert create \
  --name "High Error Rate" \
  --resource-group konveyor-rg \
  --scopes /subscriptions/{subscription}/resourceGroups/konveyor-rg/providers/Microsoft.Web/sites/konveyor-app \
  --condition "count 'Http Server Errors' > 10" \
  --action konveyor-alerts
```

## 🆘 Troubleshooting

### Common Issues
- **Deployment failures**: Check Azure Activity Log
- **Application errors**: Review Application Insights
- **Performance issues**: Monitor resource utilization
- **Access issues**: Verify permissions and network connectivity

### Diagnostic Commands
```bash
# Check application logs
az webapp log tail --name konveyor-app --resource-group konveyor-rg

# Check resource health
az resource list --resource-group konveyor-rg --query "[].{Name:name,Type:type,Status:properties.provisioningState}"

# Test connectivity
az network connectivity-test --source-resource konveyor-app --dest-resource konveyor-db
```

---

**Next Steps**: Choose your deployment path and follow the detailed guides above.

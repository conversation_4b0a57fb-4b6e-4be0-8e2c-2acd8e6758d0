# ☁️ Azure Services Setup

This guide covers setting up and configuring Azure services required for Konveyor.

## 📋 Prerequisites

- Azure subscription with appropriate permissions
- Azure CLI installed and configured
- Terraform installed (for infrastructure deployment)
- Python 3.10+ environment

## 🚀 Quick Start

### Option 1: Automated Setup with Terraform

1. **Deploy Azure resources**:
```bash
cd terraform
terraform init
terraform apply
```

2. **Update environment variables**:
```bash
./scripts/update_env.sh
```

3. **Install Azure dependencies**:
```bash
pip install azure-identity azure-keyvault-secrets openai azure-search-documents
```

4. **Verify setup**:
```python
from konveyor.config.azure import AzureConfig

azure = AzureConfig()
openai_client = azure.get_openai_client()
search_client = azure.get_search_client()
```

### Option 2: Manual Setup

Follow the detailed setup instructions below for manual configuration.

## 🏗️ Required Azure Services

### 1. Azure OpenAI Service

**Purpose**: Provides large language models for chat and embeddings.

**Setup**:
```bash
# Create Azure OpenAI resource
az cognitiveservices account create \
  --name konveyor-openai \
  --resource-group konveyor-rg \
  --kind OpenAI \
  --sku S0 \
  --location eastus

# Get endpoint and key
az cognitiveservices account show \
  --name konveyor-openai \
  --resource-group konveyor-rg \
  --query "properties.endpoint" -o tsv

az cognitiveservices account keys list \
  --name konveyor-openai \
  --resource-group konveyor-rg \
  --query "key1" -o tsv
```

**Required Models**:
- **Chat Model**: `gpt-35-turbo` or `gpt-4`
- **Embedding Model**: `text-embedding-ada-002`

**Deploy Models**:
```bash
# Deploy chat model
az cognitiveservices account deployment create \
  --name konveyor-openai \
  --resource-group konveyor-rg \
  --deployment-name gpt-35-turbo \
  --model-name gpt-35-turbo \
  --model-version "0613" \
  --model-format OpenAI \
  --scale-settings-scale-type "Standard"

# Deploy embedding model
az cognitiveservices account deployment create \
  --name konveyor-openai \
  --resource-group konveyor-rg \
  --deployment-name text-embedding-ada-002 \
  --model-name text-embedding-ada-002 \
  --model-version "2" \
  --model-format OpenAI \
  --scale-settings-scale-type "Standard"
```

### 2. Azure Cognitive Search

**Purpose**: Vector search and document indexing.

**Setup**:
```bash
# Create search service
az search service create \
  --name konveyor-search \
  --resource-group konveyor-rg \
  --sku Standard \
  --location eastus

# Get admin key
az search admin-key show \
  --service-name konveyor-search \
  --resource-group konveyor-rg \
  --query "primaryKey" -o tsv
```

**Configuration**:
- **SKU**: Standard (minimum for vector search)
- **Replicas**: 1 (can scale up)
- **Partitions**: 1 (can scale up)

### 3. Azure Blob Storage

**Purpose**: Document storage and file management.

**Setup**:
```bash
# Create storage account
az storage account create \
  --name konveyorstorage \
  --resource-group konveyor-rg \
  --location eastus \
  --sku Standard_LRS

# Create container for documents
az storage container create \
  --name documents \
  --account-name konveyorstorage

# Get connection string
az storage account show-connection-string \
  --name konveyorstorage \
  --resource-group konveyor-rg \
  --query "connectionString" -o tsv
```

### 4. Azure Key Vault

**Purpose**: Secure storage of secrets and configuration.

**Setup**:
```bash
# Create Key Vault
az keyvault create \
  --name konveyor-keyvault \
  --resource-group konveyor-rg \
  --location eastus

# Set secrets
az keyvault secret set \
  --vault-name konveyor-keyvault \
  --name "openai-api-key" \
  --value "your-openai-key"

az keyvault secret set \
  --vault-name konveyor-keyvault \
  --name "search-api-key" \
  --value "your-search-key"
```

### 5. Azure Database for PostgreSQL (Optional)

**Purpose**: Production database (SQLite used for development).

**Setup**:
```bash
# Create PostgreSQL server
az postgres server create \
  --name konveyor-db \
  --resource-group konveyor-rg \
  --location eastus \
  --admin-user konveyor \
  --admin-password "SecurePassword123!" \
  --sku-name GP_Gen5_2

# Create database
az postgres db create \
  --name konveyor \
  --server-name konveyor-db \
  --resource-group konveyor-rg
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with your Azure configuration:

```bash
# Azure OpenAI
AZURE_OPENAI_ENDPOINT=https://konveyor-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002
AZURE_OPENAI_API_VERSION=2024-12-01-preview

# Azure Cognitive Search
AZURE_SEARCH_ENDPOINT=https://konveyor-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-api-key
AZURE_SEARCH_INDEX_NAME=konveyor-documents

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=konveyorstorage;AccountKey=...

# Azure Key Vault
AZURE_KEY_VAULT_URL=https://konveyor-keyvault.vault.azure.net/

# Database (optional)
DATABASE_URL=*************************************************************************************/konveyor
```

### Azure Configuration Class

The `AzureConfig` class manages all Azure service connections:

```python
from konveyor.core.azure_utils.config import AzureConfig

# Initialize configuration
config = AzureConfig()

# Get service clients
openai_client = config.get_openai_client()
search_client = config.get_search_client()
storage_client = config.get_storage_client()
keyvault_client = config.get_keyvault_client()
```

## 🔧 Service Configuration

### Azure OpenAI Configuration

```python
# Configure chat completion
AZURE_OPENAI_CONFIG = {
    "deployment_name": "gpt-35-turbo",
    "max_tokens": 1000,
    "temperature": 0.7,
    "top_p": 0.9,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
}

# Configure embeddings
AZURE_EMBEDDING_CONFIG = {
    "deployment_name": "text-embedding-ada-002",
    "chunk_size": 1000,
    "chunk_overlap": 200
}
```

### Azure Search Configuration

```python
# Search index configuration
SEARCH_INDEX_CONFIG = {
    "name": "konveyor-documents",
    "fields": [
        {
            "name": "id",
            "type": "Edm.String",
            "key": True,
            "searchable": False
        },
        {
            "name": "content",
            "type": "Edm.String",
            "searchable": True,
            "analyzer": "standard.lucene"
        },
        {
            "name": "content_vector",
            "type": "Collection(Edm.Single)",
            "searchable": True,
            "vector_search_dimensions": 1536,
            "vector_search_profile_name": "default"
        }
    ]
}
```

## ✅ Verification

### Test Azure Connections

```bash
# Run connection tests
python manage.py test_azure_connections
```

### Manual Testing

```python
# Test OpenAI connection
from konveyor.core.azure_utils.clients import AzureClientManager

client_manager = AzureClientManager()
openai_client = client_manager.get_openai_client()

response = openai_client.chat.completions.create(
    model="gpt-35-turbo",
    messages=[{"role": "user", "content": "Hello, Azure!"}]
)
print(response.choices[0].message.content)

# Test Search connection
search_client = client_manager.get_search_client()
results = search_client.search("test query")
print(f"Search service is working: {len(list(results))} results")
```

## 🚨 Troubleshooting

### Common Issues

#### Authentication Errors
```bash
# Ensure you're logged into Azure CLI
az login

# Check current subscription
az account show

# Set correct subscription if needed
az account set --subscription "your-subscription-id"
```

#### OpenAI Deployment Issues
```bash
# List available models
az cognitiveservices account list-models \
  --name konveyor-openai \
  --resource-group konveyor-rg

# Check deployment status
az cognitiveservices account deployment list \
  --name konveyor-openai \
  --resource-group konveyor-rg
```

#### Search Service Issues
```bash
# Check search service status
az search service show \
  --name konveyor-search \
  --resource-group konveyor-rg \
  --query "status"

# Test search endpoint
curl -X GET "https://konveyor-search.search.windows.net/indexes" \
  -H "api-key: your-search-api-key" \
  -H "Content-Type: application/json"
```

#### Storage Access Issues
```bash
# Test storage connection
az storage container list \
  --connection-string "your-connection-string"

# Check container permissions
az storage container show-permission \
  --name documents \
  --connection-string "your-connection-string"
```

## 💰 Cost Optimization

### Azure OpenAI
- Use `gpt-35-turbo` instead of `gpt-4` for cost savings
- Implement response caching for frequently asked questions
- Monitor token usage and set budgets

### Azure Search
- Start with Basic SKU for development
- Scale to Standard only when needed for production
- Monitor search unit consumption

### Storage
- Use Standard_LRS for cost-effective storage
- Implement lifecycle policies for old documents
- Consider archive storage for infrequently accessed files

## 🔒 Security Best Practices

### Access Control
- Use Azure Active Directory for authentication
- Implement least privilege access
- Regularly rotate API keys

### Network Security
- Configure private endpoints for production
- Use virtual networks to isolate resources
- Enable Azure Firewall for additional protection

### Data Protection
- Enable encryption at rest for all services
- Use TLS 1.3 for data in transit
- Implement data classification and retention policies

## 📈 Monitoring and Alerts

### Azure Monitor
```bash
# Enable diagnostic settings
az monitor diagnostic-settings create \
  --name konveyor-diagnostics \
  --resource /subscriptions/{subscription}/resourceGroups/konveyor-rg/providers/Microsoft.CognitiveServices/accounts/konveyor-openai \
  --logs '[{"category":"Audit","enabled":true}]' \
  --metrics '[{"category":"AllMetrics","enabled":true}]' \
  --workspace /subscriptions/{subscription}/resourceGroups/konveyor-rg/providers/Microsoft.OperationalInsights/workspaces/konveyor-workspace
```

### Key Metrics to Monitor
- **OpenAI**: Token usage, request latency, error rates
- **Search**: Query volume, index size, search latency
- **Storage**: Blob operations, storage usage, access patterns
- **Application**: Response times, error rates, user activity

---

**Next Steps**: [Monitoring Setup](monitoring.md) | [Security Configuration](security.md)

# 📖 Reference Documentation

Comprehensive reference materials for Konveyor configuration, APIs, and concepts.

## 📚 Reference Sections

### ⚙️ Configuration
- **[Configuration Reference](configuration.md)** - Complete configuration options
- **[Environment Variables](environment-variables.md)** - All environment variables
- **[Settings Guide](settings.md)** - Django settings and customization

### 📝 Concepts and Terminology
- **[Glossary](glossary.md)** - Terms and definitions
- **[Architecture Concepts](architecture-concepts.md)** - Core architectural concepts
- **[AI and ML Concepts](ai-concepts.md)** - AI/ML terminology specific to Konveyor

### 📊 Data and Models
- **[Data Models](data-models.md)** - Database schema and models
- **[API Schemas](api-schemas.md)** - Request/response schemas
- **[Search Index Schema](search-schema.md)** - Search index structure

### 🔧 Technical Reference
- **[Command Line Interface](cli-reference.md)** - Management commands
- **[Error Codes](error-codes.md)** - Error codes and meanings
- **[Performance Metrics](performance-metrics.md)** - Key performance indicators

### 📋 Compliance and Standards
- **[Security Standards](security-standards.md)** - Security compliance information
- **[Data Privacy](data-privacy.md)** - Privacy and data handling
- **[Changelog](changelog.md)** - Version history and changes

## 🎯 Quick Reference

### Essential Configuration

```bash
# Minimum required environment variables
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-key
SECRET_KEY=your-django-secret-key
```

### Common Commands

```bash
# Health check
curl https://your-instance.azurewebsites.net/healthz/

# Upload document
curl -X POST https://your-instance.azurewebsites.net/documents/upload/ \
  -F "file=@document.pdf"

# Ask question
curl -X POST https://your-instance.azurewebsites.net/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{"message": "How do I get started?"}'
```

### Key Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/healthz/` | GET | Health check |
| `/api/chat/` | POST | Send message to AI |
| `/documents/upload/` | POST | Upload document |
| `/api/search/` | POST | Search documents |

## 📖 How to Use This Reference

### For Developers
- **Configuration**: Start with [Configuration Reference](configuration.md)
- **APIs**: Check [API Schemas](api-schemas.md) for request/response formats
- **Commands**: Use [CLI Reference](cli-reference.md) for management tasks

### For System Administrators
- **Environment**: Review [Environment Variables](environment-variables.md)
- **Security**: Check [Security Standards](security-standards.md)
- **Performance**: Monitor [Performance Metrics](performance-metrics.md)

### For End Users
- **Concepts**: Start with [Glossary](glossary.md) for terminology
- **Errors**: Look up issues in [Error Codes](error-codes.md)
- **Changes**: Track updates in [Changelog](changelog.md)

## 🔍 Search This Reference

Use your browser's search function (Ctrl+F / Cmd+F) to quickly find:
- **Configuration options**: Search for setting names
- **Error codes**: Search for error numbers or messages
- **API endpoints**: Search for endpoint paths
- **Concepts**: Search for technical terms

## 📱 Reference Format

Each reference document follows a consistent format:

### Configuration References
- **Purpose**: What the setting controls
- **Type**: Data type and format
- **Default**: Default value if any
- **Example**: Usage example
- **Notes**: Important considerations

### API References
- **Endpoint**: URL path and method
- **Parameters**: Request parameters
- **Response**: Response format
- **Examples**: Code examples
- **Errors**: Possible error responses

### Concept References
- **Definition**: Clear, concise definition
- **Context**: Where/how it's used
- **Examples**: Practical examples
- **Related**: Links to related concepts

## 🔄 Keeping References Updated

This reference documentation is:
- **Version-controlled**: Changes tracked in Git
- **Automatically generated**: Some sections generated from code
- **Community-maintained**: Contributions welcome
- **Regularly reviewed**: Updated with each release

## 📞 Reference Feedback

Help us improve the reference documentation:
- **Report errors**: Create GitHub issues for corrections
- **Suggest additions**: Request new reference materials
- **Contribute examples**: Share your configuration examples
- **Improve clarity**: Suggest better explanations

---

**Looking for something specific?** Use the navigation above or search within individual reference documents.

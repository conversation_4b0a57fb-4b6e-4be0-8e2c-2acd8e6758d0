# ⚙️ Configuration Reference

Complete configuration guide for Konveyor with all available options, organized by category.

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Core Configuration](#core-configuration)
- [Search Provider Configuration](#search-provider-configuration)
- [Azure Services](#azure-services)
- [Database Configuration](#database-configuration)
- [Security Settings](#security-settings)
- [Bot Framework & Slack](#bot-framework--slack)
- [Logging Configuration](#logging-configuration)
- [Environment-Specific Settings](#environment-specific-settings)

## 🚀 Quick Start

### Minimum Required Configuration

```bash
# Essential Azure services
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-key

# Django basics
DJANGO_SECRET_KEY=your-secret-key-here
DJANGO_SETTINGS_MODULE=konveyor.settings.development
```

### Complete `.env` Template

```bash
# Copy from .env.example and fill in your values
cp .env.example .env
```

## 🔧 Core Configuration

### Django Framework Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `DJANGO_SECRET_KEY` | string | auto-generated | Django secret key for cryptographic signing |
| `DJANGO_SETTINGS_MODULE` | string | `konveyor.settings.development` | Django settings module to load |
| `DEBUG` | boolean | `False` | Enable Django debug mode (auto-set by environment) |
| `ALLOWED_HOSTS` | string | `localhost,127.0.0.1` | Comma-separated list of allowed hosts |

### Application Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `DJANGO_ENVIRONMENT` | string | `development` | Environment identifier (development/production/test) |
| `BASE_DIR` | path | auto-detected | Project root directory |
| `MEDIA_URL` | string | `/media/` | URL prefix for media files |
| `MEDIA_ROOT` | path | `{BASE_DIR}/media` | Filesystem path for media files |

## 🔍 Search Provider Configuration

**New in v2.0**: Konveyor now supports multiple vector database providers through a unified abstraction layer.

### Provider Selection

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `SEARCH_PROVIDER` | string | `pinecone` | Vector search provider (`pinecone`, `azure`, `langchain`) |
| `EMBEDDING_PROVIDER` | string | `azure_openai` | Embedding service provider (`azure_openai`, `openai`) |

### Pinecone Configuration (Primary Target)

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `PINECONE_API_KEY` | string | **required** | Pinecone API key |
| `PINECONE_ENVIRONMENT` | string | optional | Pinecone environment (legacy) |
| `PINECONE_INDEX_NAME` | string | `konveyor-documents` | Pinecone index name |
| `PINECONE_SERVERLESS_REGION` | string | `us-east-1` | Serverless region for new indexes |
| `PINECONE_METRIC` | string | `cosine` | Distance metric (cosine, euclidean, dotproduct) |
| `EMBEDDING_DIMENSION` | integer | `1536` | Vector dimension (OpenAI: 1536) |

### OpenAI Configuration (Alternative Embedding Provider)

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `OPENAI_API_KEY` | string | optional | OpenAI API key |
| `OPENAI_EMBEDDING_MODEL` | string | `text-embedding-ada-002` | OpenAI embedding model |

### Provider Examples

**Pinecone + Azure OpenAI (Recommended)**:
```bash
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=azure_openai
PINECONE_API_KEY=your-pinecone-key
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key
```

**Azure AI Search + Azure OpenAI (Current)**:
```bash
SEARCH_PROVIDER=azure
EMBEDDING_PROVIDER=azure_openai
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-key
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key
```

**Pinecone + OpenAI**:
```bash
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=openai
PINECONE_API_KEY=your-pinecone-key
OPENAI_API_KEY=your-openai-key
```

## ☁️ Azure Services

### Azure OpenAI Configuration

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `AZURE_OPENAI_ENDPOINT` | url | **required** | Azure OpenAI service endpoint |
| `AZURE_OPENAI_API_KEY` | string | **required** | Azure OpenAI API key |
| `AZURE_OPENAI_CHAT_DEPLOYMENT` | string | `gpt-4o` | Chat completion model deployment name |
| `AZURE_OPENAI_EMBEDDING_DEPLOYMENT` | string | `text-embedding-ada-002` | Embedding model deployment name |
| `AZURE_OPENAI_API_VERSION` | string | `2024-05-13` | Azure OpenAI API version |

### Azure Cognitive Search

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `AZURE_SEARCH_ENDPOINT` | url | **required** | Azure Cognitive Search endpoint |
| `AZURE_SEARCH_API_KEY` | string | **required** | Azure Cognitive Search API key |
| `AZURE_SEARCH_INDEX_NAME` | string | `konveyor-documents` | Search index name for documents |

### Azure Storage

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `AZURE_STORAGE_CONNECTION_STRING` | string | **required** | Azure Storage connection string |
| `AZURE_STORAGE_CONTAINER_NAME` | string | `documents` | Blob container name for documents |
| `AZURE_STORAGE_ACCOUNT_NAME` | string | auto-extracted | Storage account name (from connection string) |
| `AZURE_STORAGE_ACCOUNT_KEY` | string | auto-extracted | Storage account key (from connection string) |
| `AZURE_STORAGE_ACCOUNT_URL` | url | auto-generated | Storage account URL |

### Azure Document Intelligence

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT` | url | **required** | Document Intelligence service endpoint |
| `AZURE_DOCUMENT_INTELLIGENCE_API_KEY` | string | **required** | Document Intelligence API key |

### Azure Core Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `AZURE_TENANT_ID` | string | **required** | Azure Active Directory tenant ID |
| `AZURE_SUBSCRIPTION_ID` | string | **required** | Azure subscription ID |
| `AZURE_LOCATION` | string | `eastus` | Azure region for resources |
| `AZURE_KEY_VAULT_URL` | url | optional | Azure Key Vault URL for secrets |
| `AZURE_CLIENT_ID` | string | optional | Service principal client ID |
| `AZURE_CLIENT_SECRET` | string | optional | Service principal client secret |

## 🗄️ Database Configuration

### PostgreSQL (Production/Staging)

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `DB_NAME` | string | `konveyor` | Database name |
| `DB_USER` | string | `postgres` | Database username |
| `DB_PASSWORD` | string | `postgres` | Database password |
| `DB_HOST` | string | `localhost` | Database host |
| `DB_PORT` | integer | `5432` | Database port |

### SQLite (Development/Testing)

SQLite is used automatically in development and test environments. No additional configuration required.

**Development**: `{BASE_DIR}/db.sqlite3`
**Testing**: `{BASE_DIR}/db.sqlite3.test`

## 🔒 Security Settings

### Production Security

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `SECURE_SSL_REDIRECT` | boolean | `True` | Force HTTPS redirects (disabled in Azure App Service) |
| `SESSION_COOKIE_SECURE` | boolean | `True` | Require HTTPS for session cookies |
| `CSRF_COOKIE_SECURE` | boolean | `True` | Require HTTPS for CSRF cookies |
| `SECURE_BROWSER_XSS_FILTER` | boolean | `True` | Enable XSS filtering |
| `SECURE_CONTENT_TYPE_NOSNIFF` | boolean | `True` | Prevent MIME type sniffing |
| `X_FRAME_OPTIONS` | string | `DENY` | X-Frame-Options header value |

### Azure App Service Detection

The application automatically detects Azure App Service deployment via the `WEBSITE_HOSTNAME` environment variable and adjusts security settings accordingly.

## 🤖 Bot Framework & Slack

### Microsoft Bot Framework

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `MICROSOFT_APP_ID` | string | **required** | Bot Framework application ID |
| `MICROSOFT_APP_PASSWORD` | string | **required** | Bot Framework application password |
| `AZURE_BOT_ENDPOINT` | url | optional | Bot service endpoint |
| `AZURE_BOT_SERVICE_NAME` | string | `konveyor-bot` | Bot service name |
| `AZURE_RESOURCE_GROUP` | string | `konveyor-rg` | Azure resource group name |

### Slack Integration

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `SLACK_BOT_TOKEN` | string | **required** | Slack bot token (xoxb-...) |
| `SLACK_SIGNING_SECRET` | string | **required** | Slack signing secret for verification |
| `SLACK_APP_TOKEN` | string | optional | Slack app token for Socket Mode (xapp-...) |
| `SLACK_CLIENT_ID` | string | optional | Slack OAuth client ID |
| `SLACK_CLIENT_SECRET` | string | optional | Slack OAuth client secret |
| `SLACK_DEFAULT_CHANNEL` | string | optional | Default channel for notifications |
| `SLACK_ADMIN_CHANNEL` | string | optional | Admin channel for system messages |

### Bot Behavior Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `BOT_NAME` | string | `Konveyor Bot` | Display name for the bot |
| `BOT_DESCRIPTION` | string | `AI assistant for documentation...` | Bot description |
| `MAX_RESPONSE_LENGTH` | integer | `4000` | Maximum response length in characters |
| `DEFAULT_WELCOME_MESSAGE` | string | `Hello! I'm Konveyor Bot...` | Welcome message for new users |
| `ERROR_MESSAGE` | string | `I'm sorry, I encountered...` | Default error message |

## 📝 Logging Configuration

### Log Levels by Environment

| Environment | Console Level | File Level | Django Level | Konveyor Level |
|-------------|---------------|------------|--------------|----------------|
| Development | `DEBUG` | `DEBUG` | `DEBUG` | `DEBUG` |
| Production | `INFO` | `INFO` | `INFO` | `INFO` |
| Test | `DEBUG` | `DEBUG` | `DEBUG` | `DEBUG` |

### Log File Locations

| Environment | Log File |
|-------------|----------|
| Development | `{BASE_DIR}/logs/konveyor-dev.log` |
| Production | Console only (Azure App Service) |
| Test | `{BASE_DIR}/logs/konveyor-test.log` |

### Custom Logging Configuration

```python
# Override in environment-specific settings
LOGGING["loggers"]["your_module"] = {
    "handlers": ["console"],
    "level": "INFO",
    "propagate": True,
}
```

## 🌍 Environment-Specific Settings

### Development Environment

- **Database**: SQLite
- **Debug**: Enabled
- **Logging**: Verbose (DEBUG level)
- **Security**: Relaxed settings
- **Allowed Hosts**: Includes ngrok domains

### Production Environment

- **Database**: PostgreSQL with fallback to SQLite
- **Debug**: Disabled
- **Logging**: Standard (INFO level)
- **Security**: Full security headers enabled
- **SSL**: Handled by Azure App Service

### Test Environment

- **Database**: In-memory SQLite
- **Debug**: Disabled
- **Logging**: Verbose for debugging
- **Security**: Minimal for testing
- **Mocking**: Azure services mocked by default

## 🔗 Configuration Loading Order

1. **Environment Variables**: Loaded from system environment
2. **`.env` File**: Loaded if present in project root
3. **Django Settings**: Environment-specific settings files
4. **Defaults**: Fallback values from `settings_loader.py`

## ⚠️ Important Notes

- **Secrets**: Never commit secrets to version control
- **Azure App Service**: Some settings are automatically configured
- **Key Vault**: Future integration planned for secret management
- **Environment Detection**: Automatic based on `DJANGO_SETTINGS_MODULE`

## 🔍 Troubleshooting

### Common Configuration Issues

1. **Missing Required Variables**: Check error messages for specific variables
2. **Azure Connection Failures**: Verify endpoints and API keys
3. **Database Connection**: Ensure PostgreSQL is running and accessible
4. **Slack Integration**: Verify bot tokens and permissions

### Configuration Validation

```bash
# Check configuration status
python manage.py check

# Test Azure connections
python manage.py test konveyor.core.azure_utils

# Validate environment variables
python -c "from konveyor.core.azure_utils.config import AzureConfig; AzureConfig().validate_required_config()"
```

---

**Next**: See [Environment Variables Reference](environment-variables.md) for detailed variable descriptions.

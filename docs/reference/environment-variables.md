# 🌍 Environment Variables Reference

Complete reference of all environment variables used in Konveyor, with descriptions, types, defaults, and examples.

## 📋 Table of Contents

- [Required Variables](#required-variables)
- [Search Provider Configuration](#search-provider-configuration)
- [Azure Services](#azure-services)
- [Django Framework](#django-framework)
- [Database Configuration](#database-configuration)
- [Bot Framework & Slack](#bot-framework--slack)
- [Optional Variables](#optional-variables)
- [Environment-Specific](#environment-specific)

## ⚠️ Required Variables

These variables must be set for Konveyor to function properly:

### Core Azure Services

```bash
# Azure OpenAI (Required)
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-openai-api-key

# Azure Cognitive Search (Required)
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-api-key

# Azure Storage (Required)
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=...;AccountKey=...;EndpointSuffix=core.windows.net"

# Azure Document Intelligence (Required)
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=https://your-doc-intel.cognitiveservices.azure.com/
AZURE_DOCUMENT_INTELLIGENCE_API_KEY=your-doc-intel-api-key

# Azure Core (Required)
AZURE_TENANT_ID=your-tenant-id
AZURE_SUBSCRIPTION_ID=your-subscription-id
```

### Django Essentials

```bash
# Django Secret Key (Required)
DJANGO_SECRET_KEY=your-secret-key-here
```

## 🔍 Search Provider Configuration

**New in v2.0**: Multi-provider search abstraction layer with runtime switching.

### Provider Selection

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `SEARCH_PROVIDER` | string | `pinecone` | Vector search provider | `pinecone` |
| `EMBEDDING_PROVIDER` | string | `azure_openai` | Embedding service provider | `azure_openai` |

**Available Search Providers:**
- `pinecone` - Pinecone Serverless (recommended for cost optimization)
- `azure` - Azure AI Search (current production provider)
- `langchain` - LangChain adapter (supports Qdrant, FAISS, Chroma)

**Available Embedding Providers:**
- `azure_openai` - Azure OpenAI (current production provider)
- `openai` - OpenAI (alternative for Pinecone integration)

### Pinecone Configuration

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `PINECONE_API_KEY` | string | **required** | Pinecone API key | `abc123-def456-ghi789` |
| `PINECONE_ENVIRONMENT` | string | optional | Pinecone environment (legacy) | `us-east1-gcp` |
| `PINECONE_INDEX_NAME` | string | `konveyor-documents` | Pinecone index name | `konveyor-prod` |
| `PINECONE_SERVERLESS_REGION` | string | `us-east-1` | Serverless region | `us-east-1` |
| `PINECONE_METRIC` | string | `cosine` | Distance metric | `cosine` |
| `EMBEDDING_DIMENSION` | integer | `1536` | Vector dimension | `1536` |

### OpenAI Configuration

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `OPENAI_API_KEY` | string | optional | OpenAI API key | `sk-abc123...` |
| `OPENAI_EMBEDDING_MODEL` | string | `text-embedding-ada-002` | Embedding model | `text-embedding-ada-002` |

### Migration Examples

**Pinecone Migration (Recommended)**:
```bash
# Switch to Pinecone for 70% cost reduction
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=azure_openai
PINECONE_API_KEY=your-pinecone-key
PINECONE_INDEX_NAME=konveyor-documents
PINECONE_SERVERLESS_REGION=us-east-1

# Keep existing Azure OpenAI for embeddings
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key
```

**Full OpenAI Migration**:
```bash
# Use OpenAI for both search and embeddings
SEARCH_PROVIDER=pinecone
EMBEDDING_PROVIDER=openai
PINECONE_API_KEY=your-pinecone-key
OPENAI_API_KEY=your-openai-key
```

**Rollback to Azure**:
```bash
# Rollback to Azure AI Search if needed
SEARCH_PROVIDER=azure
EMBEDDING_PROVIDER=azure_openai
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-key
```

## ☁️ Azure Services

### Azure OpenAI

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_OPENAI_ENDPOINT` | URL | **required** | Azure OpenAI service endpoint | `https://myopenai.openai.azure.com/` |
| `AZURE_OPENAI_API_KEY` | string | **required** | Azure OpenAI API key | `abc123...` |
| `AZURE_OPENAI_CHAT_DEPLOYMENT` | string | `gpt-4o` | Chat completion model deployment | `gpt-4o` |
| `AZURE_OPENAI_EMBEDDING_DEPLOYMENT` | string | `text-embedding-ada-002` | Embedding model deployment | `text-embedding-ada-002` |
| `AZURE_OPENAI_API_VERSION` | string | `2024-05-13` | Azure OpenAI API version | `2024-05-13` |

### Azure Cognitive Search

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_SEARCH_ENDPOINT` | URL | **required** | Cognitive Search endpoint | `https://mysearch.search.windows.net` |
| `AZURE_SEARCH_API_KEY` | string | **required** | Cognitive Search API key | `abc123...` |
| `AZURE_SEARCH_INDEX_NAME` | string | `konveyor-documents` | Search index name | `konveyor-documents` |

### Azure Storage

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_STORAGE_CONNECTION_STRING` | string | **required** | Storage account connection string | `DefaultEndpointsProtocol=https;...` |
| `AZURE_STORAGE_CONTAINER_NAME` | string | `documents` | Blob container name | `documents` |
| `AZURE_STORAGE_ACCOUNT_NAME` | string | auto-extracted | Storage account name | `mystorageaccount` |
| `AZURE_STORAGE_ACCOUNT_KEY` | string | auto-extracted | Storage account key | `abc123...` |
| `AZURE_STORAGE_ACCOUNT_URL` | URL | auto-generated | Storage account URL | `https://mystorageaccount.blob.core.windows.net` |

### Azure Document Intelligence

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT` | URL | **required** | Document Intelligence endpoint | `https://mydocai.cognitiveservices.azure.com/` |
| `AZURE_DOCUMENT_INTELLIGENCE_API_KEY` | string | **required** | Document Intelligence API key | `abc123...` |

### Azure Core

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_TENANT_ID` | UUID | **required** | Azure AD tenant ID | `********-1234-1234-1234-************` |
| `AZURE_SUBSCRIPTION_ID` | UUID | **required** | Azure subscription ID | `********-1234-1234-1234-************` |
| `AZURE_LOCATION` | string | `eastus` | Azure region | `eastus` |
| `AZURE_KEY_VAULT_URL` | URL | optional | Key Vault URL | `https://myvault.vault.azure.net/` |
| `AZURE_CLIENT_ID` | UUID | optional | Service principal client ID | `********-1234-1234-1234-************` |
| `AZURE_CLIENT_SECRET` | string | optional | Service principal secret | `abc123...` |

### Azure Additional Services

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `AZURE_COSMOS_CONNECTION_STRING` | string | optional | Cosmos DB connection string | `AccountEndpoint=https://...` |
| `AZURE_REDIS_CONNECTION_STRING` | string | optional | Redis connection string | `myredis.redis.cache.windows.net:6380,...` |
| `AZURE_BOT_ENDPOINT` | URL | optional | Bot service endpoint | `https://mybot.azurewebsites.net` |

## 🔧 Django Framework

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `DJANGO_SECRET_KEY` | string | auto-generated | Django secret key | `django-insecure-abc123...` |
| `DJANGO_SETTINGS_MODULE` | string | `konveyor.settings.development` | Settings module | `konveyor.settings.production` |
| `DJANGO_ENVIRONMENT` | string | `development` | Environment identifier | `production` |
| `ALLOWED_HOSTS` | string | `localhost,127.0.0.1` | Comma-separated allowed hosts | `localhost,mydomain.com` |
| `DEBUG` | boolean | auto-set | Debug mode (set by environment) | `True` |

## 🗄️ Database Configuration

### PostgreSQL (Production)

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `DB_NAME` | string | `konveyor` | Database name | `konveyor_prod` |
| `DB_USER` | string | `postgres` | Database username | `konveyor_user` |
| `DB_PASSWORD` | string | `postgres` | Database password | `secure_password` |
| `DB_HOST` | string | `localhost` | Database host | `mydb.postgres.database.azure.com` |
| `DB_PORT` | integer | `5432` | Database port | `5432` |

### Alternative Database URL

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `DATABASE_URL` | URL | optional | Complete database URL | `********************************/dbname` |

## 🤖 Bot Framework & Slack

### Microsoft Bot Framework

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `MICROSOFT_APP_ID` | UUID | **required** | Bot Framework app ID | `********-1234-1234-1234-************` |
| `MICROSOFT_APP_PASSWORD` | string | **required** | Bot Framework app password | `abc123...` |
| `AZURE_BOT_SERVICE_NAME` | string | `konveyor-bot` | Bot service name | `konveyor-bot` |
| `AZURE_RESOURCE_GROUP` | string | `konveyor-rg` | Resource group name | `konveyor-prod-rg` |

### Slack Integration

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `SLACK_BOT_TOKEN` | string | **required** | Slack bot token | `*************************************...` |
| `SLACK_SIGNING_SECRET` | string | **required** | Slack signing secret | `abc123...` |
| `SLACK_APP_TOKEN` | string | optional | Slack app token (Socket Mode) | `xapp-1-A********9-************-abc123...` |
| `SLACK_CLIENT_ID` | string | optional | Slack OAuth client ID | `************.************` |
| `SLACK_CLIENT_SECRET` | string | optional | Slack OAuth client secret | `abc123...` |
| `SLACK_DEFAULT_CHANNEL` | string | optional | Default notification channel | `#general` |
| `SLACK_ADMIN_CHANNEL` | string | optional | Admin notification channel | `#konveyor-admin` |
| `SLACK_TEST_CHANNEL_ID` | string | optional | Test channel ID | `C********9` |

### Slack Webhook Configuration

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `SLACK_WEBHOOK_URL` | URL | optional | Slack webhook URL | `https://mydomain.com/api/bot/slack/events/` |

## 🔒 Security Settings

### Production Security

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `SECURE_SSL_REDIRECT` | boolean | `True` | Force HTTPS redirects | `True` |
| `SESSION_COOKIE_SECURE` | boolean | `True` | Secure session cookies | `True` |
| `CSRF_COOKIE_SECURE` | boolean | `True` | Secure CSRF cookies | `True` |

### Azure App Service Detection

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `WEBSITE_HOSTNAME` | string | auto-set | Azure App Service hostname | `myapp.azurewebsites.net` |

## 📦 Optional Variables

### Bot Behavior

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `BOT_NAME` | string | `Konveyor Bot` | Bot display name | `My Company Bot` |
| `MAX_RESPONSE_LENGTH` | integer | `4000` | Max response length | `2000` |
| `RESPONSE_STYLE` | string | `balanced` | Response style | `detailed` |
| `FOCUS_AREA` | string | `general` | Focus area | `code` |

### Development Tools

| Variable | Type | Default | Description | Example |
|----------|------|---------|-------------|---------|
| `INTERNAL_IPS` | string | `127.0.0.1` | Internal IPs for debug toolbar | `127.0.0.1,*************` |

## 🌍 Environment-Specific

### Development Environment

```bash
# Automatically set when using development settings
DJANGO_SETTINGS_MODULE=konveyor.settings.development
DEBUG=True

# Development-specific overrides
ALLOWED_HOSTS=localhost,127.0.0.1,*.ngrok-free.app
```

### Production Environment

```bash
# Automatically set when using production settings
DJANGO_SETTINGS_MODULE=konveyor.settings.production
DEBUG=False

# Production-specific settings
WEBSITE_HOSTNAME=myapp.azurewebsites.net
```

### Test Environment

```bash
# Automatically set when using test settings
DJANGO_SETTINGS_MODULE=konveyor.settings.test
DEBUG=False

# Test-specific mocks (automatically set)
AZURE_OPENAI_ENDPOINT=https://mock-openai.example.com
AZURE_SEARCH_ENDPOINT=https://mock-search.example.com
```

## 📝 Variable Types

### Data Types

- **string**: Text value
- **integer**: Numeric value
- **boolean**: `True`/`False` (case-insensitive)
- **URL**: Valid HTTP/HTTPS URL
- **UUID**: Standard UUID format
- **path**: File system path

### Boolean Values

Accepted boolean values (case-insensitive):
- **True**: `true`, `True`, `1`, `yes`, `on`
- **False**: `false`, `False`, `0`, `no`, `off`, empty string

## 🔍 Variable Validation

### Required Variable Check

```bash
# Check for missing required variables
python -c "
from konveyor.core.azure_utils.config import AzureConfig
config = AzureConfig()
config.validate_required_config()
"
```

### Environment Variable Sources

Variables are loaded in this order (later sources override earlier ones):

1. **System Environment**: `export VAR=value`
2. **`.env` File**: `VAR=value` in project root
3. **Django Settings**: Hardcoded in settings files
4. **Defaults**: From `settings_loader.py`

## ⚠️ Security Notes

### Sensitive Variables

Never commit these to version control:
- All API keys (`*_API_KEY`)
- Passwords (`*_PASSWORD`, `*_SECRET`)
- Connection strings (`*_CONNECTION_STRING`)
- Tokens (`*_TOKEN`)

### Secret Management

- **Development**: Use `.env` file (add to `.gitignore`)
- **Production**: Use Azure Key Vault or App Service settings
- **CI/CD**: Use GitHub Secrets

## 🔗 Related Documentation

- **[Configuration Reference](configuration.md)**: Detailed configuration guide
- **[Settings Reference](settings.md)**: Django settings documentation
- **[Azure Setup Guide](../azure_setup.md)**: Azure service configuration
- **[Slack Integration](../user-guide/slack-integration.md)**: Slack setup guide

---

**Need help?** Check the [Troubleshooting Guide](../user-guide/troubleshooting.md) for common configuration issues.

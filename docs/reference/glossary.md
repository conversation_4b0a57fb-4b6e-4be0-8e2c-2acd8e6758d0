# 📖 Glossary

Comprehensive glossary of terms used in Konveyor documentation and codebase.

## A

### Agent Orchestrator
The central coordination system that routes user queries to appropriate AI skills and manages conversation flow. Acts as the "brain" that decides which skill should handle each request.

### API Key
A secret token used to authenticate requests to the Konveyor API. Generated per user or application and should be kept secure.

### Azure Cognitive Search
Microsoft's cloud search service used by Konveyor for semantic search, vector storage, and document indexing.

### Azure OpenAI
Microsoft's cloud service providing access to OpenAI's language models (GPT-3.5, GPT-4) and embedding models.

## B

### Bot Framework
Microsoft's framework for building conversational AI applications. Konveyor uses this for Slack and Teams integration.

### Blob Storage
Azure's object storage service used by Konveyor to store uploaded documents and files.

## C

### Chat Completion
The process of generating a response to a user message using a large language model like GPT-3.5 or GPT-4.

### Chunk
A segment of a document that has been split for processing. Documents are divided into chunks to fit within AI model context limits and improve search relevance.

### Cognitive Search
See [Azure Cognitive Search](#azure-cognitive-search).

### Context Window
The maximum amount of text (measured in tokens) that an AI model can process in a single request. Konveyor manages this by chunking documents and selecting relevant pieces.

### Conversation ID
A unique identifier that groups related messages in a conversation, allowing Konveyor to maintain context across multiple exchanges.

## D

### Document Chunk
See [Chunk](#chunk).

### Document Processing
The workflow of taking an uploaded document, extracting text, splitting into chunks, generating embeddings, and indexing for search.

### Django
The Python web framework that Konveyor is built on, providing the web server, database ORM, and API framework.

## E

### Embedding
A numerical vector representation of text that captures semantic meaning. Used for similarity search and retrieval.

### Embedding Model
An AI model that converts text into embeddings. Konveyor typically uses `text-embedding-ada-002` from Azure OpenAI.

### Environment Variables
Configuration values stored outside the code, typically in a `.env` file, used to configure Konveyor's behavior and connections.

## F

### Function Calling
A feature of modern language models that allows them to call specific functions or tools. Konveyor uses this for skill routing.

## G

### GPT (Generative Pre-trained Transformer)
The family of language models from OpenAI that Konveyor uses for generating responses (e.g., GPT-3.5-turbo, GPT-4).

## H

### Health Check
An endpoint (`/healthz/`) that reports the status of Konveyor and its dependencies, used for monitoring and troubleshooting.

### Hybrid Search
A search approach that combines semantic search (using embeddings) with traditional keyword search for better results.

## I

### Index
A data structure in Azure Cognitive Search that stores processed documents and their embeddings for fast retrieval.

### Inference
The process of using a trained AI model to generate predictions or responses based on input data.

## J

### JWT (JSON Web Token)
A secure way to transmit information between parties. Used by Konveyor for API authentication.

## K

### Knowledge Base
The collection of documents and information that Konveyor can search and reference when answering questions.

### Knowledge Gap Analysis
A feature that identifies areas where users might need additional information or training based on their questions and interactions.

## L

### Large Language Model (LLM)
AI models like GPT-3.5 and GPT-4 that can understand and generate human-like text. The core technology behind Konveyor's conversational abilities.

### Logging
The process of recording events, errors, and activities in Konveyor for debugging and monitoring purposes.

## M

### Metadata
Additional information about documents, such as title, author, creation date, and category, stored alongside the content.

### Migration
Database schema changes managed by Django to update the database structure as the application evolves.

## N

### Natural Language Processing (NLP)
The field of AI focused on helping computers understand and generate human language.

### ngrok
A tool used in development to expose local servers to the internet, commonly used for testing webhooks.

## O

### Onboarding
The process of helping new team members learn about systems, processes, and codebase. Konveyor's primary use case.

### OpenAI
The company that created GPT models and the API that Konveyor uses through Azure OpenAI service.

## P

### Prompt
The input text sent to a language model to generate a response. Konveyor constructs prompts that include user questions and relevant context.

### Prompt Engineering
The practice of crafting effective prompts to get better responses from language models.

## Q

### Query
A search request or question submitted to Konveyor for processing.

### Query Preprocessing
The step where user questions are analyzed and potentially modified before being used for search or generation.

## R

### RAG (Retrieval-Augmented Generation)
The core technique Konveyor uses: retrieving relevant information from documents and using it to augment the generation of responses.

### Rate Limiting
Controlling the number of requests a user or application can make to prevent abuse and manage costs.

### Relevance Score
A numerical measure (0-1) indicating how well a search result matches the user's query.

## S

### Search Index
See [Index](#index).

### Semantic Kernel
Microsoft's framework for building AI applications that Konveyor uses to orchestrate AI skills and manage conversations.

### Semantic Search
Search that understands the meaning of queries and documents, not just keyword matching. Powered by embeddings.

### Skill
A specialized AI capability in Konveyor, such as documentation navigation, code understanding, or knowledge analysis.

### Slack Integration
The feature that allows users to interact with Konveyor through Slack messages and commands.

## T

### Token
In AI contexts, a unit of text (roughly a word or part of a word) that models process. Also refers to authentication tokens for API access.

### Tokenization
The process of breaking text into tokens for AI model processing.

## U

### User Interface (UI)
The web-based interface that allows users to interact with Konveyor through a browser.

## V

### Vector
A numerical representation of data. In Konveyor, text is converted to vectors (embeddings) for similarity search.

### Vector Database
A database optimized for storing and searching vectors. Azure Cognitive Search serves this role for Konveyor.

### Vector Search
Searching by comparing the similarity of vectors rather than exact text matches.

## W

### Webhook
An HTTP callback that allows external services (like Slack) to notify Konveyor of events.

### Web Framework
The underlying technology stack. Konveyor uses Django as its web framework.

## Common Acronyms

| Acronym | Full Form | Description |
|---------|-----------|-------------|
| AI | Artificial Intelligence | Computer systems that can perform tasks typically requiring human intelligence |
| API | Application Programming Interface | Set of protocols for building software applications |
| CLI | Command Line Interface | Text-based interface for interacting with software |
| CRUD | Create, Read, Update, Delete | Basic operations for data management |
| HTTP | HyperText Transfer Protocol | Protocol for transferring data over the web |
| JSON | JavaScript Object Notation | Lightweight data interchange format |
| LLM | Large Language Model | AI models trained on vast amounts of text |
| ML | Machine Learning | Type of AI that learns from data |
| NLP | Natural Language Processing | AI field focused on language understanding |
| ORM | Object-Relational Mapping | Technique for converting data between systems |
| RAG | Retrieval-Augmented Generation | AI technique combining search and generation |
| REST | Representational State Transfer | Architectural style for web services |
| SDK | Software Development Kit | Tools for developing applications |
| SQL | Structured Query Language | Language for managing databases |
| UI | User Interface | How users interact with software |
| URL | Uniform Resource Locator | Web address |
| UUID | Universally Unique Identifier | Unique identifier format |

## Technical Terms by Category

### AI and Machine Learning
- **Embedding**: Vector representation of text
- **Fine-tuning**: Customizing a model for specific tasks
- **Inference**: Using a model to make predictions
- **Model**: Trained AI system
- **Training**: Process of teaching AI from data

### Web Development
- **Endpoint**: Specific URL for API access
- **Middleware**: Software that sits between applications
- **Route**: URL pattern that maps to code
- **Session**: Temporary data storage for user interactions
- **Template**: Reusable code structure

### Database
- **Migration**: Database schema change
- **Model**: Data structure definition
- **Query**: Request for data
- **Schema**: Database structure
- **Transaction**: Group of database operations

### Cloud and Infrastructure
- **Container**: Packaged application with dependencies
- **Deployment**: Process of making software available
- **Load Balancer**: Distributes traffic across servers
- **Scaling**: Adjusting resources based on demand
- **Service**: Independent software component

## Context-Specific Usage

### In Documentation
When we say "upload a document," we mean adding a file to Konveyor's knowledge base where it will be processed, chunked, and indexed for search.

### In API Responses
"Sources" refer to the documents and specific chunks that were used to generate a response, providing transparency and allowing verification.

### In Configuration
"Deployment" often refers to a specific model deployment in Azure OpenAI (like "gpt-35-turbo") rather than software deployment.

### In Conversations
"Context" refers to both the conversation history and the relevant document chunks retrieved to answer a question.

---

**Can't find a term?** [Suggest an addition](https://github.com/sdamache/konveyor/issues/new?title=Glossary%20addition:) or check the [API Reference](../api-reference/) for technical terms.

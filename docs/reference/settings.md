# ⚙️ Django Settings Reference

Complete reference for Django settings in Konveyor, organized by category and environment.

## 📋 Table of Contents

- [Settings Architecture](#settings-architecture)
- [Core Django Settings](#core-django-settings)
- [Database Configuration](#database-configuration)
- [Security Settings](#security-settings)
- [Azure Integration](#azure-integration)
- [Logging Configuration](#logging-configuration)
- [Environment-Specific Settings](#environment-specific-settings)
- [Custom Settings](#custom-settings)

## 🏗️ Settings Architecture

### Settings Module Structure

```
konveyor/settings/
├── __init__.py          # Settings loader and environment detection
├── base.py              # Base settings shared across all environments
├── development.py       # Development-specific settings
├── production.py        # Production-specific settings
├── test.py             # Test-specific settings
├── settings_loader.py   # Environment variable loader with defaults
└── utils.py            # Utility functions for settings
```

### Settings Loading Order

1. **Environment Variables**: Loaded from `.env` file and system environment
2. **Settings Loader**: Populates `os.environ` with defaults from `settings_loader.py`
3. **Base Settings**: Common settings from `base.py`
4. **Environment Settings**: Environment-specific overrides

### Environment Detection

```python
# Automatic environment detection
DJANGO_SETTINGS_MODULE = os.getenv("DJANGO_SETTINGS_MODULE", "konveyor.settings.development")

# Supported environments:
# - konveyor.settings.development
# - konveyor.settings.production  
# - konveyor.settings.test
```

## 🔧 Core Django Settings

### Application Configuration

```python
# Installed Applications
INSTALLED_APPS = [
    # Django core apps
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    
    # Konveyor apps
    "konveyor.apps.core.apps.CoreConfig",
    "konveyor.apps.users.apps.UsersConfig",
    "konveyor.apps.api.apps.ApiConfig",
    "konveyor.apps.documents.apps.DocumentsConfig",
    "konveyor.apps.search.apps.SearchConfig",
    "konveyor.apps.bot.apps.BotConfig",
]
```

### Middleware Configuration

```python
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]
```

### URL and WSGI Configuration

| Setting | Value | Description |
|---------|-------|-------------|
| `ROOT_URLCONF` | `konveyor.urls` | Root URL configuration |
| `WSGI_APPLICATION` | `konveyor.wsgi.application` | WSGI application |

### Template Configuration

```python
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]
```

### Static and Media Files

| Setting | Default | Description |
|---------|---------|-------------|
| `STATIC_URL` | `/static/` | URL prefix for static files |
| `STATIC_ROOT` | `{BASE_DIR}/staticfiles` | Static files collection directory |
| `MEDIA_URL` | `/media/` | URL prefix for media files |
| `MEDIA_ROOT` | `{BASE_DIR}/media` | Media files directory |

### Internationalization

| Setting | Default | Description |
|---------|---------|-------------|
| `LANGUAGE_CODE` | `en-us` | Default language |
| `TIME_ZONE` | `UTC` | Default timezone |
| `USE_I18N` | `True` | Enable internationalization |
| `USE_TZ` | `True` | Enable timezone support |

## 🗄️ Database Configuration

### Development Environment

```python
# SQLite for development
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}
```

### Production Environment

```python
# PostgreSQL with SQLite fallback
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DB_NAME", "konveyor"),
        "USER": os.environ.get("DB_USER", "postgres"),
        "PASSWORD": os.environ.get("DB_PASSWORD", "postgres"),
        "HOST": os.environ.get("DB_HOST", "localhost"),
        "PORT": os.environ.get("DB_PORT", "5432"),
    }
}

# Automatic fallback to SQLite if PostgreSQL connection fails
```

### Test Environment

```python
# In-memory SQLite for testing
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3.test",
    }
}

# Faster password hashing for tests
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]
```

## 🔒 Security Settings

### Base Security Settings

| Setting | Default | Description |
|---------|---------|-------------|
| `SECRET_KEY` | from `DJANGO_SECRET_KEY` | Django secret key |
| `DEBUG` | `False` | Debug mode (overridden by environment) |
| `ALLOWED_HOSTS` | `["*", "localhost", "127.0.0.1"]` | Allowed hosts |
| `DEFAULT_AUTO_FIELD` | `django.db.models.BigAutoField` | Default primary key type |

### Production Security Settings

```python
# HTTPS and Security Headers
SECURE_SSL_REDIRECT = True  # Disabled in Azure App Service
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = "DENY"
```

### Azure App Service Detection

```python
# Automatic detection and configuration
if os.environ.get("WEBSITE_HOSTNAME"):
    # Running in Azure App Service
    SECURE_SSL_REDIRECT = False  # Azure handles HTTPS
    ALLOWED_HOSTS = [os.environ.get("WEBSITE_HOSTNAME", "*")]
```

### Development Security Settings

```python
# Relaxed security for development
DEBUG = True
ALLOWED_HOSTS.extend([
    "*.ngrok-free.app",  # Support for ngrok tunneling
    "ngrok-free.app",
])
```

## ☁️ Azure Integration

### Azure Core Settings

```python
# Loaded from environment variables
AZURE_CORE_SETTINGS = {
    "AZURE_LOCATION": os.getenv("AZURE_LOCATION", "eastus"),
    "AZURE_TENANT_ID": os.getenv("AZURE_TENANT_ID"),
    "AZURE_SUBSCRIPTION_ID": os.getenv("AZURE_SUBSCRIPTION_ID"),
}
```

### Azure OpenAI Settings

```python
# OpenAI configuration
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_CHAT_DEPLOYMENT = os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT", "gpt-4o")
AZURE_OPENAI_EMBEDDING_DEPLOYMENT = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-ada-002")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-05-13")
```

### Azure Storage Settings

```python
# Storage configuration
AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
AZURE_STORAGE_CONTAINER_NAME = os.getenv("AZURE_STORAGE_CONTAINER_NAME", "documents")
```

### Azure Search Settings

```python
# Cognitive Search configuration
AZURE_SEARCH_ENDPOINT = os.getenv("AZURE_SEARCH_ENDPOINT")
AZURE_SEARCH_API_KEY = os.getenv("AZURE_SEARCH_API_KEY")
AZURE_SEARCH_INDEX_NAME = os.getenv("AZURE_SEARCH_INDEX_NAME", "konveyor-documents")
```

### Slack Integration Settings

```python
# Slack configuration
SLACK_BOT_TOKEN = os.environ.get("SLACK_BOT_TOKEN", "")
SLACK_SIGNING_SECRET = os.environ.get("SLACK_SIGNING_SECRET", "")
SLACK_APP_TOKEN = os.environ.get("SLACK_APP_TOKEN", "")
SLACK_CLIENT_ID = os.environ.get("SLACK_CLIENT_ID", "")
SLACK_CLIENT_SECRET = os.environ.get("SLACK_CLIENT_SECRET", "")
```

## 📝 Logging Configuration

### Base Logging Structure

```python
# Logging formatters
LOGGING_FORMATTERS = {
    "verbose": {
        "format": "{levelname} {asctime} {module} {message}",
        "style": "{",
    },
    "structured": {
        "format": "{levelname} {asctime} {name} {module} {message}",
        "style": "{",
    },
}

# Base handlers
LOGGING_HANDLERS = {
    "console_base": {
        "class": "logging.StreamHandler",
        "formatter": "structured",
    },
}
```

### Environment-Specific Logging

#### Development Logging

```python
# Development: Verbose logging with file output
LOGGING["handlers"]["console"]["level"] = "DEBUG"
LOGGING["loggers"]["django"]["level"] = "DEBUG"
LOGGING["loggers"]["konveyor"]["level"] = "DEBUG"
LOGGING["root"]["level"] = "DEBUG"

# File handler for development
LOGGING["handlers"]["dev_file"] = {
    "level": "DEBUG",
    "class": "logging.FileHandler",
    "filename": os.path.join(BASE_DIR, "logs", "konveyor-dev.log"),
    "formatter": "structured",
}
```

#### Production Logging

```python
# Production: Standard logging to console (Azure App Service)
LOGGING["handlers"]["console"]["level"] = "INFO"
LOGGING["loggers"]["django"]["level"] = "INFO"
LOGGING["loggers"]["konveyor"]["level"] = "INFO"
LOGGING["root"]["level"] = "INFO"
```

#### Test Logging

```python
# Test: Debug logging for troubleshooting
LOGGING["handlers"]["console"]["level"] = "DEBUG"
LOGGING["loggers"]["django"]["level"] = "DEBUG"
LOGGING["loggers"]["konveyor"]["level"] = "DEBUG"
LOGGING["root"]["level"] = "DEBUG"

# Test file handler
LOGGING["handlers"]["test_file"] = {
    "level": "DEBUG",
    "class": "logging.FileHandler",
    "filename": os.path.join(BASE_DIR, "logs", "konveyor-test.log"),
    "formatter": "structured",
}
```

### Logger Configuration

```python
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": LOGGING_FORMATTERS,
    "handlers": {
        "console": {
            **LOGGING_HANDLERS["console_base"],
            "level": "INFO",  # Overridden by environment
        }
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": True,
        },
        "konveyor": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": True,
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
}
```

## 🌍 Environment-Specific Settings

### Development Settings (`development.py`)

```python
# Development overrides
DEBUG = True
ALLOWED_HOSTS.extend(["*.ngrok-free.app", "ngrok-free.app"])

# SQLite database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}

# Debug logging
LOGGING["handlers"]["console"]["level"] = "DEBUG"

# Optional: Django Extensions
try:
    import django_extensions
    INSTALLED_APPS += ["django_extensions"]
except ImportError:
    pass
```

### Production Settings (`production.py`)

```python
# Production overrides
DEBUG = False
ALLOWED_HOSTS = [os.environ.get("WEBSITE_HOSTNAME", "*")]

# PostgreSQL with fallback
# (Database configuration with connection testing)

# Security settings
SECURE_SSL_REDIRECT = True  # Disabled if WEBSITE_HOSTNAME detected
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
```

### Test Settings (`test.py`)

```python
# Test overrides
DEBUG = False
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")

# Fast SQLite database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3.test",
    }
}

# Fast password hashing
PASSWORD_HASHERS = ["django.contrib.auth.hashers.MD5PasswordHasher"]

# Mock Azure settings
MOCK_VALUES = {
    "AZURE_OPENAI_ENDPOINT": "https://mock-openai.example.com",
    "AZURE_SEARCH_ENDPOINT": "https://mock-search.example.com",
    # ... other mock values
}
```

## 🔧 Custom Settings

### Settings Loader (`settings_loader.py`)

```python
def load_settings():
    """Load and populate environment variables with defaults."""
    defined_settings = {
        # Azure Settings
        "AZURE_OPENAI_ENDPOINT": None,
        "AZURE_OPENAI_CHAT_DEPLOYMENT": "gpt-4o",
        # ... all other settings with defaults
    }
    
    # Populate os.environ with defaults
    for key, default_value in defined_settings.items():
        value = os.environ.get(key, default_value)
        if value is not None:
            os.environ[key] = str(value)
```

### Settings Utilities (`utils.py`)

```python
def get_secret(secret_name, default=None):
    """Get secret from environment with future Key Vault support."""
    return os.environ.get(secret_name, default)

# Future: Azure Key Vault integration
# def get_secret_from_key_vault(secret_name, default=None):
#     # Key Vault implementation
```

## 🔍 Settings Validation

### Required Settings Check

```python
# Check for required settings
from konveyor.core.azure_utils.config import AzureConfig
config = AzureConfig()
config.validate_required_config()
```

### Django Settings Check

```bash
# Validate Django configuration
python manage.py check

# Check specific settings
python manage.py check --settings=konveyor.settings.production
```

## 📚 Best Practices

### Environment Variables

1. **Use environment variables** for all configuration
2. **Provide sensible defaults** in `settings_loader.py`
3. **Never hardcode secrets** in settings files
4. **Use type conversion** when reading from environment

### Settings Organization

1. **Keep base settings minimal** and environment-agnostic
2. **Override in environment files** only when necessary
3. **Document all custom settings** with comments
4. **Group related settings** together

### Security

1. **Never commit secrets** to version control
2. **Use different secrets** for each environment
3. **Validate required settings** at startup
4. **Log configuration sources** for debugging

## 🔗 Related Documentation

- **[Configuration Reference](configuration.md)**: Complete configuration guide
- **[Environment Variables](environment-variables.md)**: All environment variables
- **[Azure Setup](../azure_setup.md)**: Azure service configuration
- **[Development Setup](../developer-guide/development-setup.md)**: Local development setup

---

**Need help?** Check the [Troubleshooting Guide](../user-guide/troubleshooting.md) for common settings issues.

# 🚀 Basic Setup Tutorial

Learn how to set up Konveyor from scratch in this comprehensive, hands-on tutorial.

## 📋 Prerequisites

- **Computer**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Python**: Version 3.10 or higher
- **Azure Account**: With permissions to create resources
- **Time**: 45-60 minutes
- **Skill Level**: 🟢 Beginner

## 🎯 What You'll Learn

By the end of this tutorial, you'll have:
- ✅ A fully functional Konveyor installation
- ✅ Azure services configured and connected
- ✅ Your first document uploaded and indexed
- ✅ A working chat interface
- ✅ Basic understanding of how Konveyor works

## ⏱️ Estimated Time: 45-60 minutes

## 🚀 Step 1: Environment Setup (10 minutes)

### 1.1 Check Python Version

```bash
python --version
# Should show Python 3.10.x or higher
```

If you need to install or upgrade Python:
- **Windows**: Download from [python.org](https://python.org)
- **macOS**: Use Homebrew: `brew install python@3.10`
- **Linux**: `sudo apt update && sudo apt install python3.10`

### 1.2 Clone the Repository

```bash
git clone https://github.com/sdamache/konveyor.git
cd konveyor
```

### 1.3 Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate it
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Verify activation (should show venv in prompt)
which python
```

### 1.4 Install Dependencies

```bash
# Install core dependencies
pip install -r requirements.txt

# For development (optional but recommended)
pip install -r requirements/development.txt
```

**✅ Verification**: Run `pip list` and confirm you see packages like `django`, `azure-openai`, etc.

## ☁️ Step 2: Azure Services Setup (20 minutes)

### 2.1 Install Azure CLI

If you don't have Azure CLI installed:

```bash
# Windows (using winget)
winget install Microsoft.AzureCLI

# macOS
brew install azure-cli

# Linux
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

### 2.2 Login to Azure

```bash
az login
# This will open a browser window for authentication

# Verify login
az account show
```

### 2.3 Create Resource Group

```bash
# Set variables (customize these)
RESOURCE_GROUP="konveyor-tutorial-rg"
LOCATION="eastus"

# Create resource group
az group create --name $RESOURCE_GROUP --location $LOCATION
```

### 2.4 Create Azure OpenAI Service

```bash
# Create OpenAI resource
az cognitiveservices account create \
  --name "konveyor-tutorial-openai" \
  --resource-group $RESOURCE_GROUP \
  --kind OpenAI \
  --sku S0 \
  --location $LOCATION

# Get the endpoint
OPENAI_ENDPOINT=$(az cognitiveservices account show \
  --name "konveyor-tutorial-openai" \
  --resource-group $RESOURCE_GROUP \
  --query "properties.endpoint" -o tsv)

# Get the API key
OPENAI_KEY=$(az cognitiveservices account keys list \
  --name "konveyor-tutorial-openai" \
  --resource-group $RESOURCE_GROUP \
  --query "key1" -o tsv)

echo "OpenAI Endpoint: $OPENAI_ENDPOINT"
echo "OpenAI Key: $OPENAI_KEY"
```

### 2.5 Deploy Required Models

```bash
# Deploy chat model
az cognitiveservices account deployment create \
  --name "konveyor-tutorial-openai" \
  --resource-group $RESOURCE_GROUP \
  --deployment-name "gpt-35-turbo" \
  --model-name "gpt-35-turbo" \
  --model-version "0613" \
  --model-format "OpenAI" \
  --scale-settings-scale-type "Standard"

# Deploy embedding model
az cognitiveservices account deployment create \
  --name "konveyor-tutorial-openai" \
  --resource-group $RESOURCE_GROUP \
  --deployment-name "text-embedding-ada-002" \
  --model-name "text-embedding-ada-002" \
  --model-version "2" \
  --model-format "OpenAI" \
  --scale-settings-scale-type "Standard"
```

### 2.6 Create Cognitive Search Service

```bash
# Create search service
az search service create \
  --name "konveyor-tutorial-search" \
  --resource-group $RESOURCE_GROUP \
  --sku Standard \
  --location $LOCATION

# Get search endpoint and key
SEARCH_ENDPOINT="https://konveyor-tutorial-search.search.windows.net"
SEARCH_KEY=$(az search admin-key show \
  --service-name "konveyor-tutorial-search" \
  --resource-group $RESOURCE_GROUP \
  --query "primaryKey" -o tsv)

echo "Search Endpoint: $SEARCH_ENDPOINT"
echo "Search Key: $SEARCH_KEY"
```

### 2.7 Create Storage Account

```bash
# Create storage account (name must be globally unique)
STORAGE_NAME="konveyortutorial$(date +%s)"
az storage account create \
  --name $STORAGE_NAME \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku Standard_LRS

# Create container
az storage container create \
  --name documents \
  --account-name $STORAGE_NAME

# Get connection string
STORAGE_CONNECTION=$(az storage account show-connection-string \
  --name $STORAGE_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "connectionString" -o tsv)

echo "Storage Connection: $STORAGE_CONNECTION"
```

**✅ Verification**: All commands should complete successfully and you should have endpoint URLs and keys.

## ⚙️ Step 3: Configure Konveyor (10 minutes)

### 3.1 Create Environment File

```bash
# Copy the example environment file
cp .env.example .env
```

### 3.2 Edit Configuration

Open `.env` in your favorite text editor and update these values:

```bash
# Basic Configuration
DEBUG=True
ENVIRONMENT=development

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://konveyor-tutorial-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your-openai-key-here
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-ada-002
AZURE_OPENAI_API_VERSION=2024-12-01-preview

# Azure Cognitive Search Configuration
AZURE_SEARCH_ENDPOINT=https://konveyor-tutorial-search.search.windows.net
AZURE_SEARCH_API_KEY=your-search-key-here
AZURE_SEARCH_INDEX_NAME=konveyor-documents

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING=your-storage-connection-string-here

# Database (SQLite for tutorial)
DATABASE_URL=sqlite:///db.sqlite3

# Security
SECRET_KEY=your-secret-key-here-make-it-long-and-random
```

**💡 Pro Tip**: Use the values you collected in Step 2. Replace `your-*-here` with actual values.

### 3.3 Generate Secret Key

```bash
# Generate a secure secret key
python -c "
import secrets
print('SECRET_KEY=' + secrets.token_urlsafe(50))
"
```

Copy the output and update the `SECRET_KEY` in your `.env` file.

**✅ Verification**: Your `.env` file should have all required values filled in.

## 🗄️ Step 4: Initialize Database (5 minutes)

### 4.1 Run Migrations

```bash
# Create database tables
python manage.py migrate

# Create a superuser (optional but recommended)
python manage.py createsuperuser
# Follow the prompts to create an admin user
```

### 4.2 Initialize Search Index

```bash
# Create the search index in Azure Cognitive Search
python manage.py create_search_index
```

**✅ Verification**: Commands should complete without errors.

## 🚀 Step 5: Start Konveyor (5 minutes)

### 5.1 Start the Development Server

```bash
python manage.py runserver
```

You should see output like:
```
Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).
January 01, 2024 - 12:00:00
Django version 4.2.x, using settings 'konveyor.settings.development'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.
```

### 5.2 Test the Health Endpoint

Open a new terminal (keep the server running) and test:

```bash
curl http://localhost:8000/healthz/
```

You should see:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "database": "ok",
    "azure_openai": "ok",
    "azure_search": "ok"
  }
}
```

**✅ Verification**: Health check returns "healthy" status for all services.

## 📄 Step 6: Upload Your First Document (10 minutes)

### 6.1 Create a Sample Document

Create a file called `sample-guide.md`:

```markdown
# Welcome to Our Project

## Getting Started

This is a sample document to test Konveyor's capabilities.

### Prerequisites

- Python 3.10+
- Basic understanding of web development
- Access to our development environment

### Installation Steps

1. Clone the repository
2. Install dependencies with `pip install -r requirements.txt`
3. Set up environment variables
4. Run migrations with `python manage.py migrate`
5. Start the server with `python manage.py runserver`

### Common Issues

**Q: The server won't start**
A: Check that all environment variables are set correctly and that the database is accessible.

**Q: I get authentication errors**
A: Verify your API keys are correct and have the necessary permissions.

### Next Steps

After installation, you can:
- Upload documents to build your knowledge base
- Ask questions through the chat interface
- Integrate with Slack for team access

## Support

For help, contact the development team or check our troubleshooting guide.
```

### 6.2 Upload via Web Interface

1. **Open your browser** to `http://localhost:8000`
2. **Navigate to Documents** (you might need to login with your superuser account)
3. **Click "Upload Document"**
4. **Select your `sample-guide.md` file**
5. **Add title**: "Sample Getting Started Guide"
6. **Click "Upload"**

### 6.3 Upload via API (Alternative)

```bash
curl -X POST http://localhost:8000/documents/upload/ \
  -F "file=@sample-guide.md" \
  -F "title=Sample Getting Started Guide"
```

### 6.4 Verify Document Processing

```bash
# Check if document was processed
curl http://localhost:8000/api/documents/
```

You should see your uploaded document in the response.

**✅ Verification**: Document appears in the list and shows `"processed": true`.

## 💬 Step 7: Your First Conversation (10 minutes)

### 7.1 Test via Web Interface

1. **Go to** `http://localhost:8000/chat/`
2. **Type a question**: "How do I get started with this project?"
3. **Press Enter** and wait for the response

### 7.2 Test via API

```bash
curl -X POST http://localhost:8000/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{
    "message": "How do I get started with this project?",
    "conversation_id": "tutorial-test"
  }'
```

### 7.3 Expected Response

You should get a response that references your uploaded document:

```json
{
  "success": true,
  "data": {
    "response": "To get started with this project, follow these steps:\n\n1. Clone the repository\n2. Install dependencies with `pip install -r requirements.txt`\n3. Set up environment variables\n4. Run migrations with `python manage.py migrate`\n5. Start the server with `python manage.py runserver`\n\nMake sure you have Python 3.10+ and basic understanding of web development.",
    "sources": [
      {
        "document_id": "1",
        "title": "Sample Getting Started Guide",
        "relevance_score": 0.95
      }
    ]
  }
}
```

### 7.4 Try More Questions

Test these questions to see how Konveyor responds:

```bash
# About troubleshooting
curl -X POST http://localhost:8000/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{
    "message": "The server won'\''t start, what should I check?",
    "conversation_id": "tutorial-test"
  }'

# About next steps
curl -X POST http://localhost:8000/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What can I do after installation?",
    "conversation_id": "tutorial-test"
  }'
```

**✅ Verification**: Konveyor provides relevant answers based on your uploaded document.

## 🎉 Congratulations!

You've successfully set up Konveyor! Here's what you accomplished:

- ✅ **Installed Konveyor** with all dependencies
- ✅ **Configured Azure services** (OpenAI, Search, Storage)
- ✅ **Uploaded your first document** and saw it get processed
- ✅ **Had your first conversation** with the AI agent
- ✅ **Verified the system works** end-to-end

## 🔍 Understanding What Happened

### Document Processing Flow
1. **Upload**: You uploaded a markdown file
2. **Parsing**: Konveyor extracted the text content
3. **Chunking**: The content was split into searchable chunks
4. **Embedding**: Each chunk was converted to a vector using Azure OpenAI
5. **Indexing**: Vectors were stored in Azure Cognitive Search

### Query Processing Flow
1. **Question**: You asked "How do I get started?"
2. **Search**: Konveyor found relevant chunks in your document
3. **Context**: The relevant content was gathered
4. **Generation**: Azure OpenAI generated a response using the context
5. **Response**: You received an answer with source citations

## 🎯 Next Steps

Now that you have Konveyor running, here are some next steps:

### Immediate Next Steps
1. **[Upload more documents](first-document-upload.md)** - Add your actual project documentation
2. **[Try the Slack integration](../user-guide/slack-integration.md)** - Set up team access
3. **[Explore the web interface](../user-guide/)** - Learn about all features

### For Your Team
1. **[Set up team access](team-access-setup.md)** - Configure for multiple users
2. **[Create onboarding paths](onboarding-engineers.md)** - Use for new engineer onboarding
3. **[Optimize performance](performance-optimization.md)** - Scale for larger teams

### For Production
1. **[Production deployment](production-deployment.md)** - Deploy to production
2. **[Monitoring setup](monitoring-setup.md)** - Set up observability
3. **[Security hardening](../infrastructure/security.md)** - Secure your deployment

## 🚨 Troubleshooting

### Common Issues

#### Azure Services Not Working
```bash
# Test Azure connections
python manage.py test_azure_connections
```

#### Document Not Processing
```bash
# Check document status
python manage.py shell
>>> from konveyor.apps.documents.models import Document
>>> docs = Document.objects.all()
>>> for doc in docs:
...     print(f"{doc.title}: processed={doc.processed}")
```

#### Search Not Working
```bash
# Rebuild search index
python manage.py rebuild_search_index
```

#### Chat Not Responding
```bash
# Check logs
tail -f logs/konveyor.log

# Test OpenAI directly
curl -X POST "$AZURE_OPENAI_ENDPOINT/openai/deployments/gpt-35-turbo/chat/completions?api-version=2024-12-01-preview" \
  -H "Content-Type: application/json" \
  -H "api-key: $AZURE_OPENAI_API_KEY" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'
```

### Getting Help

If you're still having issues:
1. Check the [Troubleshooting Guide](../user-guide/troubleshooting.md)
2. Review the [Installation Guide](../getting-started/installation.md)
3. Ask in your team's Slack channel
4. Create an issue on GitHub

## 🧹 Cleanup (Optional)

If you want to clean up the Azure resources created in this tutorial:

```bash
# Delete the entire resource group (this deletes all resources)
az group delete --name $RESOURCE_GROUP --yes --no-wait
```

**⚠️ Warning**: This will delete all resources in the resource group. Only do this if you're sure you want to remove everything.

---

**Tutorial complete!** 🎉 You now have a working Konveyor installation. Ready for the next tutorial? Try [First Document Upload](first-document-upload.md) to learn more about document management.

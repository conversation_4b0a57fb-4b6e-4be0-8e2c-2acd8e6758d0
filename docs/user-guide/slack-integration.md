# 💬 Slack Integration

Learn how to set up and use Konveyor in Slack for seamless team collaboration and knowledge sharing.

## 🎯 Overview

The Slack integration allows your team to interact with Konveyor directly in Slack channels and direct messages. This provides a familiar interface for asking questions, getting documentation help, and onboarding new team members.

### Key Features

- **Direct Messages**: Private conversations with the Konveyor bot
- **Channel Integration**: Team-wide access in specific channels
- **Rich Formatting**: Code blocks, links, and formatted responses
- **Thread Support**: Organized conversations with follow-up questions
- **Slash Commands**: Quick access to specific features

## 🚀 Quick Start

### For End Users

If your team already has Konveyor set up in Slack:

1. **Find the Konveyor bot** in your Slack workspace
2. **Send a direct message**: Click on the bot and start typing
3. **Ask a question**: "How do I set up my development environment?"
4. **Get your answer**: Konveyor will respond with relevant information and sources

### For Administrators

If you need to set up Konveyor for your team, follow the [Setup Guide](#setup-guide) below.

## 💬 How to Use Konveyor in Slack

### Direct Messages

The most common way to use Konveyor:

```
You: How do I deploy changes to staging?

Konveyor: Based on our deployment documentation, here's how to deploy to staging:

1. Create a pull request with your changes
2. Wait for CI/CD pipeline to pass
3. Get approval from a team member
4. Merge to the `develop` branch
5. The staging deployment will trigger automatically

📚 Source: Deployment Guide (docs/deployment.md)
🔗 Related: CI/CD Pipeline, Code Review Process
```

### Channel Mentions

Use Konveyor in team channels by mentioning the bot:

```
You: @konveyor What's our coding standard for Python?

Konveyor: Our Python coding standards include:

• Use Black for code formatting
• Follow PEP 8 style guidelines
• Maximum line length: 88 characters
• Use type hints for function parameters
• Write docstrings for all public functions

📚 Source: Python Style Guide (docs/coding-standards.md)
```

### Thread Conversations

Ask follow-up questions in threads:

```
You: How do I set up the database locally?

Konveyor: To set up the database locally:
1. Install PostgreSQL
2. Create a database named 'konveyor_dev'
3. Run migrations: `python manage.py migrate`

You: (in thread) What if I get a connection error?

Konveyor: For database connection errors, check:
• PostgreSQL service is running
• Database credentials in .env file
• Port 5432 is not blocked by firewall

📚 Source: Troubleshooting Guide (docs/troubleshooting.md)
```

## 🎨 Response Types

### Documentation Responses

When you ask about processes or setup:

```
📚 **Getting Started with API Development**

To create a new API endpoint:

1. **Define the view** in `views.py`
2. **Add URL pattern** in `urls.py`
3. **Write tests** in `test_views.py`
4. **Update documentation** in `api-docs.md`

💡 **Pro tip**: Use Django REST Framework serializers for data validation

📖 **Sources**:
• API Development Guide (docs/api-guide.md, lines 15-30)
• Django Best Practices (docs/django-practices.md)

🔗 **Related topics**: Testing, Serializers, Authentication
```

### Code Explanations

When you ask about code or technical details:

```python
🔧 **Authentication Middleware Explanation**

This middleware handles JWT token validation:

```python
class JWTAuthenticationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        token = self.get_token_from_header(request)
        if token:
            request.user = self.validate_token(token)
        return self.get_response(request)
```

**Key concepts**:
• Middleware runs on every request
• JWT tokens are extracted from Authorization header
• User object is attached to request if token is valid

📖 **Source**: Authentication Module (auth/middleware.py)
```

### Troubleshooting Help

When you ask about problems or errors:

```
🚨 **Database Connection Error Solutions**

**Common causes**:
1. PostgreSQL service not running
2. Incorrect database credentials
3. Network connectivity issues
4. Database doesn't exist

**Quick fixes**:
✅ Check service: `sudo systemctl status postgresql`
✅ Verify credentials in `.env` file
✅ Test connection: `psql -h localhost -U username -d database`
✅ Create database if missing: `createdb konveyor_dev`

📖 **Source**: Troubleshooting Guide (docs/troubleshooting.md)
🆘 **Still stuck?** Contact @devops-team for help
```

## 🛠️ Setup Guide

### Prerequisites

- Slack workspace with admin privileges
- Konveyor application deployed and running
- Public URL for webhook endpoints (use ngrok for development)

### Step 1: Create Slack App

1. **Go to Slack API**: Visit [api.slack.com/apps](https://api.slack.com/apps)
2. **Create New App**: Click "Create New App" → "From scratch"
3. **App Details**: 
   - Name: "Konveyor"
   - Workspace: Select your team workspace

### Step 2: Configure Bot Permissions

In your Slack app settings:

1. **Go to "OAuth & Permissions"**
2. **Add Bot Token Scopes**:
   ```
   chat:write          # Send messages
   im:history          # Read DM history
   im:read             # Read DM info
   im:write            # Send DMs
   channels:history    # Read channel messages
   channels:read       # Read channel info
   groups:history      # Read private channel messages
   groups:read         # Read private channel info
   users:read          # Read user profiles
   users:read.email    # Read user emails
   app_mentions:read   # Respond to @mentions
   ```

3. **Install App**: Click "Install to Workspace"
4. **Copy Bot Token**: Save the "Bot User OAuth Token" (starts with `xoxb-`)

### Step 3: Configure Event Subscriptions

1. **Go to "Event Subscriptions"**
2. **Enable Events**: Toggle on
3. **Request URL**: `https://your-domain.com/api/bot/slack/events/`
4. **Subscribe to Bot Events**:
   ```
   app_mention         # When bot is mentioned
   message.im          # Direct messages to bot
   message.channels    # Messages in channels (if bot is added)
   ```

### Step 4: Configure Slash Commands (Optional)

1. **Go to "Slash Commands"**
2. **Create New Command**:
   - Command: `/konveyor`
   - Request URL: `https://your-domain.com/api/bot/slack/commands/`
   - Short Description: "Ask Konveyor a question"
   - Usage Hint: "How do I deploy to staging?"

### Step 5: Environment Configuration

Add these variables to your `.env` file:

```bash
# Slack Configuration
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here
SLACK_APP_TOKEN=xapp-your-app-token-here  # For Socket Mode (optional)

# Webhook Configuration
SLACK_WEBHOOK_URL=https://your-domain.com/api/bot/slack/events/

# Optional: Default channels
SLACK_DEFAULT_CHANNEL=#general
SLACK_ADMIN_CHANNEL=#konveyor-admin
```

### Step 6: Test the Integration

1. **Invite Bot to Channel**:
   ```
   /invite @konveyor
   ```

2. **Test Direct Message**:
   ```
   Hello! Can you help me with onboarding?
   ```

3. **Test Channel Mention**:
   ```
   @konveyor What's our deployment process?
   ```

4. **Test Slash Command**:
   ```
   /konveyor How do I set up my development environment?
   ```

## 🎯 Best Practices

### For Users

**Ask Specific Questions**:
```
✅ Good: "How do I configure the database for local development?"
❌ Vague: "How does the database work?"
```

**Provide Context**:
```
✅ Good: "I'm new to the team. What should I know about our Git workflow?"
❌ Missing context: "Tell me about Git"
```

**Use Threads for Follow-ups**:
- Keep related questions in the same thread
- Helps maintain conversation context
- Reduces channel noise

### For Administrators

**Channel Strategy**:
- Add Konveyor to `#onboarding` for new team members
- Use in `#engineering` for technical questions
- Keep in `#general` for company-wide access

**Permission Management**:
- Regularly review bot permissions
- Monitor usage in admin channels
- Update documentation based on common questions

**Content Curation**:
- Keep documentation up-to-date
- Remove outdated information
- Add new content based on team needs

## 🔧 Advanced Features

### Custom Slash Commands

Create specialized commands for common tasks:

```bash
# Quick commands
/konveyor-deploy    # Deployment information
/konveyor-setup     # Setup guides
/konveyor-debug     # Troubleshooting help
```

### Channel-Specific Behavior

Configure different responses for different channels:

```python
# Example configuration
CHANNEL_CONFIGS = {
    "#onboarding": {
        "focus": "getting_started",
        "style": "beginner_friendly"
    },
    "#engineering": {
        "focus": "technical_details",
        "style": "detailed"
    }
}
```

### Integration with Other Tools

Connect Konveyor with your existing tools:

- **Jira**: Link to relevant tickets
- **GitHub**: Reference pull requests and issues
- **Confluence**: Connect to additional documentation
- **Monitoring**: Include system status information

## 🚨 Troubleshooting

### Common Issues

#### Bot Not Responding
```bash
# Check bot token
curl -X POST https://slack.com/api/auth.test \
  -H "Authorization: Bearer $SLACK_BOT_TOKEN"

# Verify webhook URL
curl -X POST https://your-domain.com/api/bot/slack/events/ \
  -H "Content-Type: application/json" \
  -d '{"type": "url_verification", "challenge": "test"}'
```

#### Permission Errors
- Verify bot has required scopes
- Check if bot is added to the channel
- Ensure webhook URL is accessible

#### Slow Responses
- Check Azure OpenAI service status
- Monitor application performance
- Verify network connectivity

### Debug Mode

Enable debug logging for troubleshooting:

```bash
# In .env file
SLACK_DEBUG=true
LOG_LEVEL=DEBUG

# View logs
tail -f logs/slack-integration.log
```

### Testing Webhooks Locally

Use ngrok for local development:

```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 8000

# Use the HTTPS URL in Slack app settings
https://abc123.ngrok-free.app/api/bot/slack/events/
```

## 📊 Analytics and Monitoring

### Usage Metrics

Track how your team uses Konveyor:

- Most common questions
- Response satisfaction ratings
- Channel usage patterns
- User engagement levels

### Performance Monitoring

Monitor integration health:

- Response times
- Error rates
- Webhook delivery success
- Azure service status

## 🔒 Security Considerations

### Token Security
- Store tokens in environment variables
- Use Azure Key Vault for production
- Rotate tokens regularly
- Monitor for unauthorized access

### Message Privacy
- Bot can read messages in channels it's added to
- Direct messages are private to the user and bot
- Consider data retention policies
- Implement audit logging

### Network Security
- Use HTTPS for all webhook URLs
- Validate Slack request signatures
- Implement rate limiting
- Monitor for suspicious activity

---

**Next Steps**: [Features Overview](features/) | [Best Practices](best-practices.md)

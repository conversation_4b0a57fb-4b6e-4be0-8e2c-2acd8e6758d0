# 🚨 Troubleshooting Guide

This guide helps you diagnose and resolve common issues with Konveyor.

## 🎯 Quick Diagnosis

### Is Konveyor Working?

**Check these first**:

1. **Service Status**: `curl https://your-instance.azurewebsites.net/healthz/`
2. **Azure Services**: Check Azure portal for service health
3. **Network**: Verify connectivity to your Konveyor instance
4. **Authentication**: Ensure your API keys or tokens are valid

### Common Symptoms

| Symptom | Likely Cause | Quick Fix |
|---------|--------------|-----------|
| No response from bot | Service down or network issue | Check health endpoint |
| Slow responses | Azure OpenAI throttling | Check usage limits |
| "I don't know" responses | No relevant documents | Upload more documentation |
| Authentication errors | Invalid or expired tokens | Refresh authentication |
| Search returns no results | Index not built or corrupted | Rebuild search index |

## 🔧 Installation and Setup Issues

### Python Environment Problems

#### Issue: `ModuleNotFoundError`
```bash
# Error
ModuleNotFoundError: No module named 'konveyor'

# Solution
pip install -r requirements.txt
# Or for development
pip install -r requirements/development.txt
```

#### Issue: Python Version Compatibility
```bash
# Check Python version
python --version
# Should be 3.10 or higher

# If wrong version, use pyenv or conda
pyenv install 3.10.12
pyenv local 3.10.12
```

#### Issue: Virtual Environment Problems
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Database Issues

#### Issue: Database Connection Error
```bash
# Check database status
python manage.py dbshell

# If using PostgreSQL
sudo systemctl status postgresql
sudo systemctl start postgresql

# Reset database
python manage.py migrate --run-syncdb
```

#### Issue: Migration Errors
```bash
# Reset migrations (development only)
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete
python manage.py makemigrations
python manage.py migrate
```

### Environment Configuration

#### Issue: Missing Environment Variables
```bash
# Check required variables
python -c "
import os
required = ['AZURE_OPENAI_ENDPOINT', 'AZURE_OPENAI_API_KEY', 'AZURE_SEARCH_ENDPOINT']
missing = [var for var in required if not os.getenv(var)]
if missing:
    print(f'Missing: {missing}')
else:
    print('All required variables set')
"
```

#### Issue: Invalid Azure Configuration
```bash
# Test Azure connections
python manage.py test_azure_connections

# Check specific services
az cognitiveservices account show --name your-openai-resource --resource-group your-rg
az search service show --name your-search-service --resource-group your-rg
```

## ☁️ Azure Services Issues

### Azure OpenAI Problems

#### Issue: Authentication Failed
```bash
# Verify endpoint and key
curl -X POST "https://your-openai.openai.azure.com/openai/deployments/gpt-35-turbo/chat/completions?api-version=2024-12-01-preview" \
  -H "Content-Type: application/json" \
  -H "api-key: YOUR_API_KEY" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'
```

#### Issue: Rate Limiting
```bash
# Check quota and usage
az cognitiveservices account list-usage \
  --name your-openai-resource \
  --resource-group your-rg

# Monitor rate limits in logs
grep "rate limit" logs/konveyor.log
```

#### Issue: Model Not Deployed
```bash
# List deployments
az cognitiveservices account deployment list \
  --name your-openai-resource \
  --resource-group your-rg

# Deploy missing model
az cognitiveservices account deployment create \
  --name your-openai-resource \
  --resource-group your-rg \
  --deployment-name gpt-35-turbo \
  --model-name gpt-35-turbo \
  --model-version "0613"
```

### Azure Cognitive Search Problems

#### Issue: Search Index Not Found
```bash
# List indexes
curl -X GET "https://your-search.search.windows.net/indexes?api-version=2023-11-01" \
  -H "api-key: YOUR_SEARCH_KEY"

# Rebuild index
python manage.py rebuild_search_index
```

#### Issue: Search Returns No Results
```bash
# Check index statistics
curl -X GET "https://your-search.search.windows.net/indexes/konveyor-documents/stats?api-version=2023-11-01" \
  -H "api-key: YOUR_SEARCH_KEY"

# Test search directly
curl -X POST "https://your-search.search.windows.net/indexes/konveyor-documents/docs/search?api-version=2023-11-01" \
  -H "Content-Type: application/json" \
  -H "api-key: YOUR_SEARCH_KEY" \
  -d '{"search": "*", "top": 5}'
```

#### Issue: Vector Search Not Working
```bash
# Check if embeddings are generated
python manage.py shell
>>> from konveyor.apps.documents.models import DocumentChunk
>>> chunks_with_embeddings = DocumentChunk.objects.exclude(embedding_vector__isnull=True).count()
>>> print(f"Chunks with embeddings: {chunks_with_embeddings}")

# Regenerate embeddings
python manage.py generate_embeddings --force
```

### Azure Storage Issues

#### Issue: Blob Storage Access Denied
```bash
# Test storage connection
az storage container list --connection-string "YOUR_CONNECTION_STRING"

# Check container permissions
az storage container show-permission \
  --name documents \
  --connection-string "YOUR_CONNECTION_STRING"
```

#### Issue: File Upload Failures
```bash
# Check storage account status
az storage account show \
  --name your-storage-account \
  --resource-group your-rg \
  --query "statusOfPrimary"

# Test upload manually
az storage blob upload \
  --file test.txt \
  --container-name documents \
  --name test.txt \
  --connection-string "YOUR_CONNECTION_STRING"
```

## 🤖 AI and Chat Issues

### Bot Not Responding

#### Issue: Slack Bot Silent
```bash
# Check Slack token
curl -X POST https://slack.com/api/auth.test \
  -H "Authorization: Bearer $SLACK_BOT_TOKEN"

# Verify webhook URL
curl -X POST https://your-instance.azurewebsites.net/api/bot/slack/events/ \
  -H "Content-Type: application/json" \
  -d '{"type": "url_verification", "challenge": "test"}'

# Check bot permissions
# Go to Slack App settings > OAuth & Permissions
# Ensure bot has required scopes
```

#### Issue: Web Chat Not Working
```bash
# Check API endpoint
curl -X POST https://your-instance.azurewebsites.net/api/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "message": "test",
    "conversation_id": "test-123"
  }'
```

### Poor Response Quality

#### Issue: Irrelevant Answers
**Causes**:
- Insufficient or poor-quality documentation
- Search index not optimized
- Query preprocessing issues

**Solutions**:
```bash
# Upload more relevant documents
curl -X POST https://your-instance.azurewebsites.net/documents/upload/ \
  -F "file=@relevant-doc.md" \
  -F "category=onboarding"

# Rebuild search index with better settings
python manage.py rebuild_search_index --optimize

# Check search relevance
python manage.py test_search_relevance --query "your test query"
```

#### Issue: "I Don't Know" Responses
**Causes**:
- No relevant documents in the knowledge base
- Search threshold too high
- Documents not properly indexed

**Solutions**:
```bash
# Check document count
python manage.py shell
>>> from konveyor.apps.documents.models import Document
>>> print(f"Total documents: {Document.objects.count()}")
>>> print(f"Processed documents: {Document.objects.filter(processed=True).count()}")

# Lower search threshold (temporarily)
# In .env file
SEARCH_RELEVANCE_THRESHOLD=0.5  # Default is usually 0.7

# Add more comprehensive documentation
# Focus on FAQ-style content that directly answers common questions
```

### Slow Response Times

#### Issue: Responses Take Too Long
**Diagnosis**:
```bash
# Check response times in logs
grep "processing_time" logs/konveyor.log | tail -10

# Monitor Azure service latency
az monitor metrics list \
  --resource /subscriptions/{subscription}/resourceGroups/{rg}/providers/Microsoft.CognitiveServices/accounts/{openai-resource} \
  --metric "ProcessingTime"
```

**Solutions**:
```bash
# Enable response caching
# In .env file
ENABLE_RESPONSE_CACHE=true
CACHE_TTL=3600  # 1 hour

# Optimize search queries
python manage.py optimize_search_index

# Scale up Azure services if needed
az cognitiveservices account update \
  --name your-openai-resource \
  --resource-group your-rg \
  --sku S0  # or higher tier
```

## 📱 Slack Integration Issues

### Setup Problems

#### Issue: Slack App Not Responding
```bash
# Verify Slack app configuration
curl -X POST https://slack.com/api/apps.permissions.info \
  -H "Authorization: Bearer $SLACK_BOT_TOKEN"

# Check event subscriptions
# Go to Slack App settings > Event Subscriptions
# Verify Request URL is correct and verified
```

#### Issue: Permission Denied in Slack
**Check Bot Scopes**:
- `chat:write` - Send messages
- `im:history` - Read DM history
- `channels:history` - Read channel messages
- `app_mentions:read` - Respond to mentions

**Reinstall App**:
```bash
# Go to Slack App settings > Install App
# Click "Reinstall to Workspace"
```

### Runtime Issues

#### Issue: Bot Mentions Not Working
```bash
# Check if bot is added to channel
# In Slack: /invite @konveyor

# Verify mention events are subscribed
# Slack App settings > Event Subscriptions > app_mention
```

#### Issue: Slash Commands Failing
```bash
# Test slash command endpoint
curl -X POST https://your-instance.azurewebsites.net/api/bot/slack/commands/ \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "token=YOUR_VERIFICATION_TOKEN&command=/konveyor&text=test"
```

## 🔍 Search and Indexing Issues

### Document Processing Problems

#### Issue: Documents Not Processing
```bash
# Check document processing status
python manage.py shell
>>> from konveyor.apps.documents.models import Document
>>> failed_docs = Document.objects.filter(processed=False)
>>> for doc in failed_docs:
...     print(f"Failed: {doc.title} - {doc.file_path}")

# Reprocess failed documents
python manage.py reprocess_documents --failed-only
```

#### Issue: Unsupported File Types
**Supported formats**: PDF, DOCX, TXT, MD, HTML

**Solution**:
```bash
# Convert unsupported files
# For example, convert DOC to DOCX
libreoffice --headless --convert-to docx document.doc

# Or extract text manually
python -c "
import textract
text = textract.process('document.ppt')
with open('document.txt', 'wb') as f:
    f.write(text)
"
```

### Search Index Issues

#### Issue: Search Index Corrupted
```bash
# Delete and rebuild index
python manage.py delete_search_index
python manage.py create_search_index
python manage.py rebuild_search_index
```

#### Issue: Embeddings Not Generated
```bash
# Check embedding status
python manage.py shell
>>> from konveyor.apps.documents.models import DocumentChunk
>>> total_chunks = DocumentChunk.objects.count()
>>> chunks_with_embeddings = DocumentChunk.objects.exclude(embedding_vector__isnull=True).count()
>>> print(f"Embeddings: {chunks_with_embeddings}/{total_chunks}")

# Generate missing embeddings
python manage.py generate_embeddings --missing-only
```

## 🔒 Security and Authentication Issues

### API Authentication Problems

#### Issue: Invalid API Key
```bash
# Test API key
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://your-instance.azurewebsites.net/api/auth/verify/

# Generate new API key
python manage.py shell
>>> from django.contrib.auth.models import User
>>> from rest_framework.authtoken.models import Token
>>> user = User.objects.get(username='your-username')
>>> token, created = Token.objects.get_or_create(user=user)
>>> print(f"API Key: {token.key}")
```

#### Issue: Azure AD Authentication Failing
```bash
# Check Azure AD configuration
az ad app show --id YOUR_APP_ID

# Test token validation
curl -X POST https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET&grant_type=client_credentials&scope=https://graph.microsoft.com/.default"
```

### Permission Issues

#### Issue: Access Denied Errors
```bash
# Check user permissions
python manage.py shell
>>> from django.contrib.auth.models import User
>>> user = User.objects.get(username='your-username')
>>> print(f"User groups: {[g.name for g in user.groups.all()]}")
>>> print(f"User permissions: {[p.codename for p in user.user_permissions.all()]}")

# Add required permissions
python manage.py shell
>>> from django.contrib.auth.models import User, Permission
>>> user = User.objects.get(username='your-username')
>>> permission = Permission.objects.get(codename='can_upload_documents')
>>> user.user_permissions.add(permission)
```

## 📊 Performance Issues

### High Memory Usage

#### Issue: Memory Leaks
```bash
# Monitor memory usage
top -p $(pgrep -f "python manage.py runserver")

# Check for memory leaks in logs
grep -i "memory\|oom" logs/konveyor.log

# Restart application
sudo systemctl restart konveyor
```

#### Issue: Large Document Processing
```bash
# Increase memory limits
# In .env file
MAX_DOCUMENT_SIZE=50MB  # Default is usually 10MB
CHUNK_SIZE=500  # Smaller chunks use less memory

# Process large documents in batches
python manage.py process_documents --batch-size=5
```

### High CPU Usage

#### Issue: CPU Spikes During Processing
```bash
# Monitor CPU usage
htop

# Check for CPU-intensive operations
grep "processing_time" logs/konveyor.log | sort -k3 -nr | head -10

# Optimize processing
# In .env file
ASYNC_PROCESSING=true
MAX_WORKERS=4  # Adjust based on CPU cores
```

## 🔧 Development and Testing Issues

### Local Development Problems

#### Issue: Hot Reload Not Working
```bash
# Ensure DEBUG=True in settings
echo "DEBUG=True" >> .env.dev

# Use development server
python manage.py runserver --settings=konveyor.settings.development
```

#### Issue: Tests Failing
```bash
# Run tests with verbose output
python manage.py test --verbosity=2

# Run specific test
python manage.py test konveyor.apps.documents.tests.test_upload

# Check test database
python manage.py test --keepdb --debug-mode
```

### Docker Issues

#### Issue: Container Won't Start
```bash
# Check container logs
docker logs konveyor-app

# Debug container
docker run -it --entrypoint /bin/bash konveyor:latest

# Check Dockerfile
docker build --no-cache -t konveyor:debug .
```

## 📞 Getting Help

### Self-Service Debugging

1. **Check Logs**: Always start with application logs
2. **Health Endpoints**: Use `/healthz/` and `/api/health/detailed/`
3. **Azure Portal**: Check service health and metrics
4. **Test Endpoints**: Use curl to test API endpoints directly

### Escalation Path

1. **Documentation**: Check this troubleshooting guide
2. **GitHub Issues**: Search existing issues
3. **Team Chat**: Ask in your team's Slack channel
4. **Support**: Contact your system administrator

### Useful Commands for Support

When asking for help, include this information:

```bash
# System information
python --version
pip list | grep -E "(django|azure|openai)"

# Service status
curl -s https://your-instance.azurewebsites.net/healthz/ | jq

# Recent errors
tail -50 logs/konveyor.log | grep -i error

# Configuration (sanitized)
env | grep -E "(AZURE|SLACK|DATABASE)" | sed 's/=.*/=***/'
```

### Log Collection Script

```bash
#!/bin/bash
# collect_logs.sh - Gather diagnostic information

echo "=== Konveyor Diagnostic Information ==="
echo "Date: $(date)"
echo "Python Version: $(python --version)"
echo "Django Version: $(python -c 'import django; print(django.get_version())')"
echo

echo "=== Service Health ==="
curl -s https://your-instance.azurewebsites.net/healthz/ || echo "Health check failed"
echo

echo "=== Recent Errors ==="
tail -20 logs/konveyor.log | grep -i error || echo "No recent errors"
echo

echo "=== Configuration ==="
env | grep -E "(AZURE|SLACK|DATABASE)" | sed 's/=.*/=***/' || echo "No config found"
```

---

**Still having issues?** Contact your team's Konveyor administrator or check the [GitHub Issues](https://github.com/sdamache/konveyor/issues) page.

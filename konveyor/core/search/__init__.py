"""
Search abstraction layer for Konveyor.

This package provides vendor-agnostic search interfaces and implementations
for different vector database providers, enabling seamless migration between
Azure AI Search, Pinecone, and other vector databases.
"""

from .factory import get_search_service, SearchProviderFactory
from .interfaces import (
    VectorSearchInterface,
    EmbeddingInterface, 
    SearchServiceInterface,
    SearchResult,
    DocumentChunk,
    SearchQuery,
    SearchType
)

__all__ = [
    "get_search_service",
    "SearchProviderFactory", 
    "VectorSearchInterface",
    "EmbeddingInterface",
    "SearchServiceInterface",
    "SearchResult",
    "DocumentChunk", 
    "SearchQuery",
    "SearchType"
]

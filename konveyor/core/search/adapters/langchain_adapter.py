"""
LangChain VectorStore adapter for unified search interface.

This module provides an adapter that wraps LangChain's VectorStore
implementations to work with our unified search interface.
"""

import logging
from typing import Any, Dict, List, Optional

try:
    from langchain_core.documents import Document as LangChainDocument
    from langchain_core.vectorstores import VectorStore
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    VectorStore = object
    LangChainDocument = object

from ..interfaces import (
    VectorSearchInterface,
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)

logger = logging.getLogger(__name__)


class LangChainSearchAdapter(VectorSearchInterface):
    """
    Adapter that wraps LangChain VectorStore implementations.
    
    This adapter allows us to use any LangChain VectorStore (Azure AI Search,
    Pinecone, Qdrant, FAISS, etc.) through our unified interface.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize LangChain adapter.
        
        Args:
            config: Configuration dictionary containing:
                - langchain_provider: Provider name (azuresearch, pinecone, qdrant, etc.)
                - embedding_service: LangChain Embeddings instance
                - provider_config: Provider-specific configuration
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("LangChain not available. Install langchain-core to use LangChain adapter.")
        
        self.config = config
        self.provider_name = config.get("langchain_provider", "azuresearch")
        self.embedding_service = config.get("embedding_service")
        self.provider_config = config.get("provider_config", {})
        
        # Initialize the LangChain VectorStore
        self.vector_store = self._create_vector_store()

    def _create_vector_store(self) -> VectorStore:
        """Create LangChain VectorStore based on provider configuration."""
        provider = self.provider_name.lower()
        
        if provider == "azuresearch":
            return self._create_azure_search_store()
        elif provider == "pinecone":
            return self._create_pinecone_store()
        elif provider == "qdrant":
            return self._create_qdrant_store()
        elif provider == "faiss":
            return self._create_faiss_store()
        elif provider == "chroma":
            return self._create_chroma_store()
        else:
            raise ValueError(f"Unsupported LangChain provider: {provider}")

    def _create_azure_search_store(self) -> VectorStore:
        """Create Azure AI Search VectorStore."""
        try:
            from langchain_community.vectorstores import AzureSearch
            
            return AzureSearch(
                azure_search_endpoint=self.provider_config.get("azure_search_endpoint"),
                azure_search_key=self.provider_config.get("azure_search_api_key"),
                index_name=self.provider_config.get("azure_search_index_name", "konveyor-documents"),
                embedding_function=self.embedding_service,
                **self.provider_config.get("azure_search_kwargs", {})
            )
        except ImportError:
            raise ImportError("langchain-community is required for Azure Search support")

    def _create_pinecone_store(self) -> VectorStore:
        """Create Pinecone VectorStore."""
        try:
            from langchain_pinecone import PineconeVectorStore
            
            return PineconeVectorStore(
                index_name=self.provider_config.get("pinecone_index_name", "konveyor-documents"),
                embedding=self.embedding_service,
                **self.provider_config.get("pinecone_kwargs", {})
            )
        except ImportError:
            raise ImportError("langchain-pinecone is required for Pinecone support")

    def _create_qdrant_store(self) -> VectorStore:
        """Create Qdrant VectorStore."""
        try:
            from langchain_qdrant import QdrantVectorStore
            
            return QdrantVectorStore(
                url=self.provider_config.get("qdrant_url", "http://localhost:6333"),
                api_key=self.provider_config.get("qdrant_api_key"),
                collection_name=self.provider_config.get("qdrant_collection_name", "konveyor-documents"),
                embedding=self.embedding_service,
                **self.provider_config.get("qdrant_kwargs", {})
            )
        except ImportError:
            raise ImportError("langchain-qdrant is required for Qdrant support")

    def _create_faiss_store(self) -> VectorStore:
        """Create FAISS VectorStore."""
        try:
            from langchain_community.vectorstores import FAISS
            import faiss
            
            # Create empty FAISS index
            dimension = self.provider_config.get("embedding_dimension", 1536)
            index = faiss.IndexFlatL2(dimension)
            
            return FAISS(
                embedding_function=self.embedding_service,
                index=index,
                docstore={},
                index_to_docstore_id={},
                **self.provider_config.get("faiss_kwargs", {})
            )
        except ImportError:
            raise ImportError("langchain-community and faiss-cpu are required for FAISS support")

    def _create_chroma_store(self) -> VectorStore:
        """Create Chroma VectorStore."""
        try:
            from langchain_chroma import Chroma
            
            return Chroma(
                collection_name=self.provider_config.get("chroma_collection_name", "konveyor-documents"),
                embedding_function=self.embedding_service,
                **self.provider_config.get("chroma_kwargs", {})
            )
        except ImportError:
            raise ImportError("langchain-chroma is required for Chroma support")

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform search using LangChain VectorStore."""
        try:
            # Convert search type to LangChain method
            if query.search_type == SearchType.VECTOR:
                # Use similarity search by vector if embedding provided
                if query.embedding:
                    results = self.vector_store.similarity_search_by_vector(
                        embedding=query.embedding,
                        k=query.top_k,
                        filter=query.filters
                    )
                else:
                    # Generate embedding and search
                    results = self.vector_store.similarity_search(
                        query=query.text,
                        k=query.top_k,
                        filter=query.filters
                    )
            elif query.search_type == SearchType.HYBRID:
                # For hybrid search, use similarity search with score
                results_with_scores = self.vector_store.similarity_search_with_score(
                    query=query.text,
                    k=query.top_k,
                    filter=query.filters
                )
                results = [doc for doc, score in results_with_scores]
            else:
                # Default to similarity search
                results = self.vector_store.similarity_search(
                    query=query.text,
                    k=query.top_k,
                    filter=query.filters
                )
            
            # Convert LangChain Documents to SearchResults
            search_results = []
            for doc in results:
                search_result = SearchResult(
                    id=doc.metadata.get("id", ""),
                    content=doc.page_content,
                    score=doc.metadata.get("score", 0.0),
                    metadata=doc.metadata,
                    document_id=doc.metadata.get("document_id"),
                    chunk_id=doc.metadata.get("chunk_id"),
                    chunk_index=doc.metadata.get("chunk_index"),
                )
                
                # Apply minimum score filter if specified
                if query.min_score is None or search_result.score >= query.min_score:
                    search_results.append(search_result)
            
            return search_results[:query.top_k]
            
        except Exception as e:
            logger.error(f"LangChain search error: {str(e)}")
            raise

    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Add documents to LangChain VectorStore."""
        try:
            # Convert DocumentChunk objects to LangChain Documents
            langchain_docs = []
            for doc in documents:
                metadata = doc.metadata or {}
                metadata.update({
                    "id": doc.id,
                    "document_id": doc.document_id,
                    "chunk_id": doc.id,
                    "chunk_index": doc.chunk_index,
                })
                
                langchain_doc = LangChainDocument(
                    page_content=doc.content,
                    metadata=metadata
                )
                langchain_docs.append(langchain_doc)
            
            # Add documents to vector store
            ids = [doc.id for doc in documents]
            self.vector_store.add_documents(documents=langchain_docs, ids=ids)
            
            logger.info(f"Successfully added {len(documents)} documents to LangChain vector store")
            return ids
            
        except Exception as e:
            logger.error(f"LangChain document addition error: {str(e)}")
            raise

    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in LangChain VectorStore."""
        try:
            # Most LangChain stores don't have explicit update methods
            # So we delete and re-add
            await self.delete_document(document.id)
            result_ids = await self.add_documents([document])
            return len(result_ids) > 0
            
        except Exception as e:
            logger.error(f"LangChain document update error: {str(e)}")
            return False

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from LangChain VectorStore."""
        try:
            # Check if the vector store supports deletion
            if hasattr(self.vector_store, 'delete'):
                result = self.vector_store.delete([document_id])
                return result is not False
            else:
                logger.warning(f"Vector store {self.provider_name} does not support deletion")
                return False
                
        except Exception as e:
            logger.error(f"LangChain document deletion error: {str(e)}")
            return False

    async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
        """Create index in LangChain VectorStore."""
        try:
            # Most LangChain stores handle index creation automatically
            # This is mainly for compatibility
            logger.info(f"Index creation handled automatically by LangChain {self.provider_name}")
            return True
            
        except Exception as e:
            logger.error(f"LangChain index creation error: {str(e)}")
            return False

    async def delete_index(self, index_name: str) -> bool:
        """Delete index from LangChain VectorStore."""
        try:
            # Check if the vector store supports index deletion
            if hasattr(self.vector_store, 'delete_collection'):
                self.vector_store.delete_collection()
                return True
            else:
                logger.warning(f"Vector store {self.provider_name} does not support index deletion")
                return False
                
        except Exception as e:
            logger.error(f"LangChain index deletion error: {str(e)}")
            return False

    async def list_indexes(self) -> List[str]:
        """List available indexes in LangChain VectorStore."""
        try:
            # Most LangChain stores don't expose index listing
            # Return the configured index/collection name
            if self.provider_name == "azuresearch":
                return [self.provider_config.get("azure_search_index_name", "konveyor-documents")]
            elif self.provider_name == "pinecone":
                return [self.provider_config.get("pinecone_index_name", "konveyor-documents")]
            elif self.provider_name == "qdrant":
                return [self.provider_config.get("qdrant_collection_name", "konveyor-documents")]
            else:
                return ["default"]
                
        except Exception as e:
            logger.error(f"LangChain list indexes error: {str(e)}")
            return []

    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get index statistics from LangChain VectorStore."""
        try:
            # Most LangChain stores don't expose detailed stats
            return {
                "provider": self.provider_name,
                "index_name": index_name,
                "status": "active"
            }
            
        except Exception as e:
            logger.error(f"LangChain index stats error: {str(e)}")
            return {}

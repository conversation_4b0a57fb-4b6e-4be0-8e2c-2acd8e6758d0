"""
Embedding service implementations.

This package contains concrete implementations of the EmbeddingInterface
for different embedding providers.
"""

__all__ = []

try:
    from .azure_openai import AzureOpenAIEmbedding
    __all__.append("AzureOpenAIEmbedding")
except ImportError:
    pass

try:
    from .openai import OpenAIEmbedding
    __all__.append("OpenAIEmbedding")
except ImportError:
    pass

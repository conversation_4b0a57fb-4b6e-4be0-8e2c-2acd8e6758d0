"""
Azure OpenAI embedding provider implementation.

This module provides the Azure OpenAI implementation of the EmbeddingInterface,
maintaining compatibility with existing Azure OpenAI functionality.
"""

import logging
from typing import List, Dict, Any

try:
    from openai import AzureOpenAI
    AZURE_OPENAI_AVAILABLE = True
except ImportError:
    AZURE_OPENAI_AVAILABLE = False

from ..interfaces import EmbeddingInterface

logger = logging.getLogger(__name__)


class AzureOpenAIEmbedding(EmbeddingInterface):
    """Azure OpenAI implementation of EmbeddingInterface."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Azure OpenAI embedding provider.
        
        Args:
            config: Configuration dictionary containing:
                - azure_openai_endpoint: Azure OpenAI endpoint
                - azure_openai_api_key: Azure OpenAI API key
                - azure_openai_api_version: API version
                - azure_openai_embedding_deployment: Deployment name
        """
        if not AZURE_OPENAI_AVAILABLE:
            raise ImportError("OpenAI SDK not available. Install openai to use Azure OpenAI provider.")
        
        self.config = config
        self.endpoint = config.get("azure_openai_endpoint")
        self.api_key = config.get("azure_openai_api_key")
        self.api_version = config.get("azure_openai_api_version", "2024-05-13")
        self.deployment_name = config.get("azure_openai_embedding_deployment", "embeddings")
        
        if not self.endpoint or not self.api_key:
            raise ValueError("Azure OpenAI endpoint and API key are required")
        
        # Initialize Azure OpenAI client
        self.client = AzureOpenAI(
            azure_endpoint=self.endpoint,
            api_key=self.api_key,
            api_version=self.api_version
        )

    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using Azure OpenAI."""
        try:
            response = self.client.embeddings.create(
                input=text,
                model=self.deployment_name
            )
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated embedding for text: {text[:50]}...")
            return embedding
            
        except Exception as e:
            logger.error(f"Azure OpenAI embedding generation failed: {str(e)}")
            raise

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts using Azure OpenAI."""
        try:
            response = self.client.embeddings.create(
                input=texts,
                model=self.deployment_name
            )
            
            embeddings = [data.embedding for data in response.data]
            logger.debug(f"Generated embeddings for {len(texts)} texts")
            return embeddings
            
        except Exception as e:
            logger.error(f"Azure OpenAI batch embedding generation failed: {str(e)}")
            raise

    @property
    def embedding_dimension(self) -> int:
        """Get embedding dimension for Azure OpenAI embeddings."""
        # Azure OpenAI text-embedding-ada-002 returns 1536 dimensions
        return 1536

"""
OpenAI embedding provider implementation.

This module provides the OpenAI implementation of the EmbeddingInterface,
as an alternative to Azure OpenAI for Pinecone integration.
"""

import logging
from typing import List, Dict, Any

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from ..interfaces import EmbeddingInterface

logger = logging.getLogger(__name__)


class OpenAIEmbedding(EmbeddingInterface):
    """OpenAI implementation of EmbeddingInterface."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize OpenAI embedding provider.
        
        Args:
            config: Configuration dictionary containing:
                - openai_api_key: OpenAI API key
                - openai_model: Embedding model name
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI SDK not available. Install openai to use OpenAI provider.")
        
        self.config = config
        self.api_key = config.get("openai_api_key")
        self.model = config.get("openai_model", "text-embedding-ada-002")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=self.api_key)

    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI."""
        try:
            response = self.client.embeddings.create(
                input=text,
                model=self.model
            )
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated embedding for text: {text[:50]}...")
            return embedding
            
        except Exception as e:
            logger.error(f"OpenAI embedding generation failed: {str(e)}")
            raise

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts using OpenAI."""
        try:
            response = self.client.embeddings.create(
                input=texts,
                model=self.model
            )
            
            embeddings = [data.embedding for data in response.data]
            logger.debug(f"Generated embeddings for {len(texts)} texts")
            return embeddings
            
        except Exception as e:
            logger.error(f"OpenAI batch embedding generation failed: {str(e)}")
            raise

    @property
    def embedding_dimension(self) -> int:
        """Get embedding dimension for OpenAI embeddings."""
        # OpenAI text-embedding-ada-002 returns 1536 dimensions
        return 1536

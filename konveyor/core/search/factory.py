"""
Search service factory for creating provider-specific implementations.

This module provides a factory pattern for instantiating different
vector database providers based on configuration, with <PERSON><PERSON><PERSON> as the primary target.
"""

import os
from typing import Dict, Type, Optional, Any

from .interfaces import VectorSearchInterface, EmbeddingInterface, SearchServiceInterface
from .unified_search_service import UnifiedSearchService


class SearchProviderFactory:
    """Factory for creating search provider instances with Pinecone as primary target."""

    _vector_providers: Dict[str, Type[VectorSearchInterface]] = {}
    _embedding_providers: Dict[str, Type[EmbeddingInterface]] = {}

    @classmethod
    def register_vector_provider(
        cls, 
        name: str, 
        provider_class: Type[VectorSearchInterface]
    ) -> None:
        """Register a vector search provider."""
        cls._vector_providers[name] = provider_class

    @classmethod
    def register_embedding_provider(
        cls, 
        name: str, 
        provider_class: Type[EmbeddingInterface]
    ) -> None:
        """Register an embedding provider."""
        cls._embedding_providers[name] = provider_class

    @classmethod
    def create_vector_search(
        cls, 
        provider: str, 
        config: Optional[Dict[str, Any]] = None
    ) -> VectorSearchInterface:
        """Create a vector search provider instance."""
        if provider not in cls._vector_providers:
            raise ValueError(f"Unknown vector search provider: {provider}. Available: {list(cls._vector_providers.keys())}")
        
        provider_class = cls._vector_providers[provider]
        return provider_class(config or {})

    @classmethod
    def create_embedding_service(
        cls, 
        provider: str, 
        config: Optional[Dict[str, Any]] = None
    ) -> EmbeddingInterface:
        """Create an embedding service instance."""
        if provider not in cls._embedding_providers:
            raise ValueError(f"Unknown embedding provider: {provider}. Available: {list(cls._embedding_providers.keys())}")
        
        provider_class = cls._embedding_providers[provider]
        return provider_class(config or {})

    @classmethod
    def create_search_service(
        cls,
        vector_provider: Optional[str] = None,
        embedding_provider: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> SearchServiceInterface:
        """Create a unified search service with specified providers."""
        # Default to Pinecone for new deployments, Azure for existing
        vector_provider = vector_provider or os.getenv("SEARCH_PROVIDER", "pinecone")
        embedding_provider = embedding_provider or os.getenv("EMBEDDING_PROVIDER", "azure_openai")

        # Use default config if none provided
        if config is None:
            config = get_default_config()

        # Create provider instances
        vector_search = cls.create_vector_search(vector_provider, config)
        embedding_service = cls.create_embedding_service(embedding_provider, config)
        
        return UnifiedSearchService(
            vector_search=vector_search,
            embedding_service=embedding_service,
            config=config or {}
        )

    @classmethod
    def get_available_providers(cls) -> Dict[str, list]:
        """Get list of available providers."""
        return {
            "vector_search": list(cls._vector_providers.keys()),
            "embedding": list(cls._embedding_providers.keys())
        }


def get_search_service(
    vector_provider: Optional[str] = None,
    embedding_provider: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> SearchServiceInterface:
    """
    Convenience function to get a configured search service.
    
    Args:
        vector_provider: Vector search provider name (default: "pinecone")
        embedding_provider: Embedding provider name (default: "azure_openai")
        config: Configuration dictionary
        
    Returns:
        Configured search service instance
        
    Example:
        # Use default providers (Pinecone + Azure OpenAI)
        search_service = get_search_service()
        
        # Use specific providers
        search_service = get_search_service(
            vector_provider="pinecone",
            embedding_provider="openai"
        )
        
        # Use with custom config
        search_service = get_search_service(
            vector_provider="pinecone",
            config={
                "pinecone_api_key": "your-key",
                "pinecone_index_name": "konveyor-documents"
            }
        )
    """
    # Use default config if none provided
    if config is None:
        config = get_default_config()

    return SearchProviderFactory.create_search_service(
        vector_provider=vector_provider,
        embedding_provider=embedding_provider,
        config=config
    )


def get_default_config() -> Dict[str, Any]:
    """Get default configuration from environment variables with Pinecone focus."""
    return {
        # Pinecone (Primary Target)
        "pinecone_api_key": os.getenv("PINECONE_API_KEY"),
        "pinecone_environment": os.getenv("PINECONE_ENVIRONMENT"),
        "pinecone_index_name": os.getenv("PINECONE_INDEX_NAME", "konveyor-documents"),
        "embedding_dimension": int(os.getenv("EMBEDDING_DIMENSION", "1536")),
        
        # Azure AI Search (Current Implementation)
        "azure_search_endpoint": os.getenv("AZURE_SEARCH_ENDPOINT"),
        "azure_search_api_key": os.getenv("AZURE_SEARCH_API_KEY"),
        "azure_search_index_name": os.getenv("AZURE_SEARCH_INDEX_NAME", "konveyor-documents"),
        
        # Azure OpenAI (Current Embedding Provider)
        "azure_openai_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT"),
        "azure_openai_api_key": os.getenv("AZURE_OPENAI_API_KEY"),
        "azure_openai_api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview"),
        "azure_openai_embedding_deployment": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "embeddings"),
        
        # OpenAI (Alternative Embedding Provider)
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "openai_model": os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-ada-002"),
    }


def get_pinecone_migration_config() -> Dict[str, Any]:
    """Get Pinecone-specific migration configuration."""
    base_config = get_default_config()
    
    # Pinecone-specific optimizations
    pinecone_config = {
        **base_config,
        "batch_size": 100,  # Pinecone recommended batch size
        "rate_limit_delay": 0.1,  # Delay between API calls
        "max_retries": 3,  # Retry failed operations
        "timeout": 30,  # Request timeout in seconds
        "serverless_region": "us-east-1",  # Pinecone serverless region
        "metric": "cosine",  # Best for OpenAI embeddings
    }
    
    return pinecone_config


# Auto-register providers when available
def _register_providers():
    """Register available providers."""
    # Register vector search providers
    try:
        from .providers.azure_search import AzureSearchProvider
        SearchProviderFactory.register_vector_provider("azure", AzureSearchProvider)
    except ImportError:
        pass

    try:
        from .providers.pinecone_provider import PineconeSearchProvider
        SearchProviderFactory.register_vector_provider("pinecone", PineconeSearchProvider)
    except ImportError:
        pass

    try:
        from .adapters.langchain_adapter import LangChainSearchAdapter
        SearchProviderFactory.register_vector_provider("langchain", LangChainSearchAdapter)
    except ImportError:
        pass

    # Register embedding providers
    try:
        from .embeddings.azure_openai import AzureOpenAIEmbedding
        SearchProviderFactory.register_embedding_provider("azure_openai", AzureOpenAIEmbedding)
    except ImportError:
        pass

    try:
        from .embeddings.openai import OpenAIEmbedding
        SearchProviderFactory.register_embedding_provider("openai", OpenAIEmbedding)
    except ImportError:
        pass


# Register providers on module import
_register_providers()

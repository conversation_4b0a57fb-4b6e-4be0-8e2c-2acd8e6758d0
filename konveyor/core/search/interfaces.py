"""
Search service interfaces and abstractions.

This module provides vendor-agnostic interfaces for search operations,
allowing the application to work with different vector database providers
without changing core business logic.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union


class SearchType(Enum):
    """Supported search types."""
    VECTOR = "vector"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    SEMANTIC = "semantic"


@dataclass
class SearchResult:
    """Standardized search result format."""
    id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    document_id: Optional[str] = None
    chunk_id: Optional[str] = None
    chunk_index: Optional[int] = None

    @classmethod
    def from_azure_result(cls, result: Dict[str, Any]) -> "SearchResult":
        """Convert Azure AI Search result to standardized format."""
        return cls(
            id=result.get("id", ""),
            content=result.get("content", ""),
            score=result.get("@search.score", 0.0),
            metadata=result.get("metadata", {}),
            document_id=result.get("document_id"),
            chunk_id=result.get("chunk_id"),
            chunk_index=result.get("chunk_index"),
        )

    @classmethod
    def from_pinecone_result(cls, result: Dict[str, Any]) -> "SearchResult":
        """Convert Pinecone result to standardized format."""
        metadata = result.get("metadata", {})
        return cls(
            id=result.get("id", ""),
            content=metadata.get("content", ""),
            score=result.get("score", 0.0),
            metadata=metadata,
            document_id=metadata.get("document_id"),
            chunk_id=metadata.get("chunk_id"),
            chunk_index=metadata.get("chunk_index"),
        )

    @classmethod
    def from_qdrant_result(cls, result: Dict[str, Any]) -> "SearchResult":
        """Convert Qdrant result to standardized format."""
        payload = result.get("payload", {})
        return cls(
            id=str(result.get("id", "")),
            content=payload.get("content", ""),
            score=result.get("score", 0.0),
            metadata=payload.get("metadata", {}),
            document_id=payload.get("document_id"),
            chunk_id=payload.get("chunk_id"),
            chunk_index=payload.get("chunk_index"),
        )


@dataclass
class DocumentChunk:
    """Standardized document chunk for indexing."""
    id: str
    content: str
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None
    document_id: Optional[str] = None
    chunk_index: Optional[int] = None


@dataclass
class SearchQuery:
    """Standardized search query."""
    text: str
    search_type: SearchType = SearchType.HYBRID
    top_k: int = 5
    filters: Optional[Dict[str, Any]] = None
    min_score: Optional[float] = None
    embedding: Optional[List[float]] = None


class VectorSearchInterface(ABC):
    """Abstract interface for vector search operations."""

    @abstractmethod
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform search operation."""
        pass

    @abstractmethod
    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Add documents to the search index."""
        pass

    @abstractmethod
    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in the search index."""
        pass

    @abstractmethod
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from the search index."""
        pass

    @abstractmethod
    async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
        """Create a search index."""
        pass

    @abstractmethod
    async def delete_index(self, index_name: str) -> bool:
        """Delete a search index."""
        pass

    @abstractmethod
    async def list_indexes(self) -> List[str]:
        """List available indexes."""
        pass

    @abstractmethod
    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get index statistics."""
        pass


class EmbeddingInterface(ABC):
    """Abstract interface for embedding generation."""

    @abstractmethod
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text."""
        pass

    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        pass

    @property
    @abstractmethod
    def embedding_dimension(self) -> int:
        """Get embedding dimension."""
        pass


class SearchServiceInterface(ABC):
    """High-level search service interface."""

    @abstractmethod
    async def semantic_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform semantic search."""
        pass

    @abstractmethod
    async def hybrid_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform hybrid search."""
        pass

    @abstractmethod
    async def vector_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform vector search."""
        pass

    @abstractmethod
    async def index_document_chunk(
        self,
        chunk_id: str,
        document_id: str,
        content: str,
        chunk_index: int,
        metadata: Dict[str, Any],
        embedding: Optional[List[float]] = None,
    ) -> bool:
        """Index a document chunk."""
        pass

    @abstractmethod
    async def create_search_index(self, index_name: Optional[str] = None) -> bool:
        """Create search index."""
        pass

    @abstractmethod
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text."""
        pass

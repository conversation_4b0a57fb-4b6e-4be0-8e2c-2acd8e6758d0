"""
Search provider implementations.

This package contains concrete implementations of the VectorSearchInterface
for different vector database providers.
"""

# Import providers when available
__all__ = []

try:
    from .azure_search import AzureSearchProvider
    __all__.append("AzureSearchProvider")
except ImportError:
    pass

try:
    from .pinecone_provider import PineconeSearchProvider
    __all__.append("PineconeSearchProvider")
except ImportError:
    pass

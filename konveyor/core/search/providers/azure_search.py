"""
Azure AI Search provider implementation.

This module provides the Azure AI Search implementation of the VectorSearchInterface,
maintaining compatibility with existing Azure-specific functionality.
"""

import logging
from typing import Any, Dict, List, Optional

try:
    from azure.core.credentials import AzureKeyCredential
    from azure.core.exceptions import AzureError
    from azure.search.documents import SearchClient
    from azure.search.documents.indexes import SearchIndexClient
    from azure.search.documents.indexes.models import (
        HnswAlgorithmConfiguration,
        SearchableField,
        SearchField,
        SearchFieldDataType,
        SearchIndex,
        SimpleField,
        VectorSearch,
        VectorSearchAlgorithmConfiguration,
        VectorSearchAlgorithmKind,
        VectorSearchAlgorithmMetric,
        VectorSearchProfile,
    )
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False
    AzureError = Exception

from ..interfaces import VectorSearchInterface, SearchQuery, SearchResult, DocumentChunk, SearchType

logger = logging.getLogger(__name__)


class AzureSearchProvider(VectorSearchInterface):
    """Azure AI Search implementation of VectorSearchInterface."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Azure Search provider.
        
        Args:
            config: Configuration dictionary containing:
                - azure_search_endpoint: Azure Search service endpoint
                - azure_search_api_key: Azure Search API key
                - azure_search_index_name: Index name (optional)
        """
        if not AZURE_AVAILABLE:
            raise ImportError("Azure SDK not available. Install azure-search-documents to use Azure provider.")
        
        self.config = config
        self.endpoint = config.get("azure_search_endpoint")
        self.api_key = config.get("azure_search_api_key")
        self.index_name = config.get("azure_search_index_name", "konveyor-documents")
        
        if not self.endpoint or not self.api_key:
            raise ValueError("Azure Search endpoint and API key are required")
        
        # Initialize clients
        self.credential = AzureKeyCredential(self.api_key)
        self.index_client = SearchIndexClient(endpoint=self.endpoint, credential=self.credential)
        self.search_client = SearchClient(
            endpoint=self.endpoint,
            index_name=self.index_name,
            credential=self.credential
        )

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform search operation using Azure AI Search."""
        try:
            search_params = {
                "search_text": query.text if query.search_type != SearchType.VECTOR else None,
                "top": query.top_k,
                "select": ["id", "content", "document_id", "chunk_id", "chunk_index", "metadata"],
            }
            
            # Add vector search if embedding is provided
            if query.embedding and query.search_type in [SearchType.VECTOR, SearchType.HYBRID]:
                search_params["vector_queries"] = [{
                    "vector": query.embedding,
                    "fields": "embedding",
                    "k": query.top_k,
                    "kind": "vector",
                }]
            
            # Add filters if provided
            if query.filters:
                filter_expressions = []
                for key, value in query.filters.items():
                    if isinstance(value, str):
                        filter_expressions.append(f"{key} eq '{value}'")
                    else:
                        filter_expressions.append(f"{key} eq {value}")
                
                if filter_expressions:
                    search_params["filter"] = " and ".join(filter_expressions)
            
            # Perform search
            results = self.search_client.search(**search_params)
            
            # Convert results to standardized format
            search_results = []
            for result in results:
                search_result = SearchResult.from_azure_result(result)
                
                # Apply minimum score filter if specified
                if query.min_score is None or search_result.score >= query.min_score:
                    search_results.append(search_result)
            
            return search_results[:query.top_k]
            
        except AzureError as e:
            logger.error(f"Azure Search error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Search operation failed: {str(e)}")
            raise

    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Add documents to Azure AI Search index."""
        try:
            # Convert DocumentChunk objects to Azure format
            azure_documents = []
            for doc in documents:
                azure_doc = {
                    "id": doc.id,
                    "content": doc.content,
                    "document_id": doc.document_id,
                    "chunk_id": doc.id,  # Use id as chunk_id for backward compatibility
                    "chunk_index": doc.chunk_index,
                    "metadata": doc.metadata or {},
                }
                
                # Add embedding if provided
                if doc.embedding:
                    azure_doc["embedding"] = doc.embedding
                
                azure_documents.append(azure_doc)
            
            # Upload documents
            result = self.search_client.upload_documents(azure_documents)
            
            # Extract successful document IDs
            successful_ids = []
            for item in result:
                if item.succeeded:
                    successful_ids.append(item.key)
                else:
                    logger.error(f"Failed to upload document {item.key}: {item.error_message}")
            
            logger.info(f"Successfully uploaded {len(successful_ids)}/{len(documents)} documents")
            return successful_ids
            
        except AzureError as e:
            logger.error(f"Azure document upload error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Document upload failed: {str(e)}")
            raise

    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in Azure AI Search index."""
        try:
            # Convert to Azure format
            azure_doc = {
                "id": document.id,
                "content": document.content,
                "document_id": document.document_id,
                "chunk_id": document.id,
                "chunk_index": document.chunk_index,
                "metadata": document.metadata or {},
            }
            
            if document.embedding:
                azure_doc["embedding"] = document.embedding
            
            # Update document
            result = self.search_client.merge_or_upload_documents([azure_doc])
            
            success = len(result) > 0 and result[0].succeeded
            if not success and result:
                logger.error(f"Failed to update document {document.id}: {result[0].error_message}")
            
            return success
            
        except AzureError as e:
            logger.error(f"Azure document update error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Document update failed: {str(e)}")
            return False

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from Azure AI Search index."""
        try:
            result = self.search_client.delete_documents([{"id": document_id}])
            
            success = len(result) > 0 and result[0].succeeded
            if not success and result:
                logger.error(f"Failed to delete document {document_id}: {result[0].error_message}")
            
            return success
            
        except AzureError as e:
            logger.error(f"Azure document deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Document deletion failed: {str(e)}")
            return False

    async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
        """Create Azure AI Search index."""
        try:
            # Check if index already exists
            existing_indexes = [index.name for index in self.index_client.list_indexes()]
            if index_name in existing_indexes:
                logger.info(f"Index '{index_name}' already exists")
                return True
            
            # Create index with default schema if none provided
            if schema is None:
                schema = self._get_default_schema()
            
            # Create search index
            index = SearchIndex(
                name=index_name,
                fields=schema["fields"],
                vector_search=schema.get("vector_search"),
            )
            
            self.index_client.create_index(index)
            logger.info(f"Successfully created index: {index_name}")
            return True
            
        except AzureError as e:
            logger.error(f"Azure index creation error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index creation failed: {str(e)}")
            return False

    async def delete_index(self, index_name: str) -> bool:
        """Delete Azure AI Search index."""
        try:
            self.index_client.delete_index(index_name)
            logger.info(f"Successfully deleted index: {index_name}")
            return True
            
        except AzureError as e:
            logger.error(f"Azure index deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index deletion failed: {str(e)}")
            return False

    async def list_indexes(self) -> List[str]:
        """List available Azure AI Search indexes."""
        try:
            indexes = self.index_client.list_indexes()
            return [index.name for index in indexes]
            
        except AzureError as e:
            logger.error(f"Azure list indexes error: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"List indexes failed: {str(e)}")
            return []

    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get Azure AI Search index statistics."""
        try:
            stats = self.index_client.get_index_statistics(index_name)
            return {
                "document_count": stats.document_count,
                "storage_size": stats.storage_size,
            }
            
        except AzureError as e:
            logger.error(f"Azure index stats error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Get index stats failed: {str(e)}")
            return {}

    def _get_default_schema(self) -> Dict[str, Any]:
        """Get default Azure AI Search index schema."""
        if not AZURE_AVAILABLE:
            return {}
        
        fields = [
            SimpleField(name="id", type=SearchFieldDataType.String, key=True),
            SearchableField(name="content", type=SearchFieldDataType.String),
            SimpleField(name="document_id", type=SearchFieldDataType.String, filterable=True),
            SimpleField(name="chunk_id", type=SearchFieldDataType.String, filterable=True),
            SimpleField(name="chunk_index", type=SearchFieldDataType.Int32, filterable=True),
            SearchField(
                name="embedding",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name="embedding-profile",
            ),
            SearchableField(name="metadata", type=SearchFieldDataType.String),
        ]
        
        vector_search = VectorSearch(
            algorithms=[
                HnswAlgorithmConfiguration(
                    name="hnsw-algorithm",
                    kind=VectorSearchAlgorithmKind.HNSW,
                    parameters={
                        "m": 4,
                        "efConstruction": 400,
                        "efSearch": 500,
                        "metric": VectorSearchAlgorithmMetric.COSINE,
                    },
                )
            ],
            profiles=[
                VectorSearchProfile(
                    name="embedding-profile",
                    algorithm_configuration_name="hnsw-algorithm",
                )
            ],
        )
        
        return {
            "fields": fields,
            "vector_search": vector_search,
        }

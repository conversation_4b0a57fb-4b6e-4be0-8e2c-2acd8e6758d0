"""
Pinecone provider implementation for unified search interface.

This module provides the Pinecone implementation of the VectorSearchInterface,
optimized for Pinecone's serverless architecture and API patterns.
"""

import asyncio
import logging
import random
import time
from typing import Any

try:
    from pinecone import Pinecone, ServerlessSpec
    from pinecone.exceptions import PineconeException

    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    PineconeException = Exception

from ..interfaces import (
    DocumentChunk,
    SearchQuery,
    SearchResult,
    SearchType,
    VectorSearchInterface,
)

logger = logging.getLogger(__name__)


def retry_with_exponential_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
):
    """
    Decorator for retrying operations with exponential backoff.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Factor by which delay increases each retry
        jitter: Whether to add random jitter to delay
    """

    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except (PineconeException, ConnectionError, TimeoutError) as e:
                    last_exception = e

                    if attempt == max_retries:
                        logger.error(
                            f"Operation failed after {max_retries} retries: {str(e)}"
                        )
                        raise

                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (backoff_factor**attempt), max_delay)

                    # Add jitter to prevent thundering herd
                    if jitter:
                        delay *= 0.5 + random.random() * 0.5

                    logger.warning(
                        f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {delay:.2f}s..."
                    )
                    await asyncio.sleep(delay)
                except Exception as e:
                    # Don't retry for non-transient errors
                    logger.error(f"Non-retryable error: {str(e)}")
                    raise

            # This should never be reached, but just in case
            raise last_exception

        return wrapper

    return decorator


class PineconeSearchProvider(VectorSearchInterface):
    """Pinecone implementation of VectorSearchInterface."""

    def __init__(self, config: dict[str, Any]):
        """
        Initialize Pinecone provider.

        Args:
            config: Configuration dictionary containing:
                - pinecone_api_key: Pinecone API key
                - pinecone_environment: Pinecone environment (optional for serverless)
                - pinecone_index_name: Index name
                - embedding_dimension: Vector dimension (default: 1536)
        """
        if not PINECONE_AVAILABLE:
            raise ImportError(
                "Pinecone SDK not available. Install pinecone-client to use Pinecone provider."
            )

        self.config = config
        self.api_key = config.get("pinecone_api_key")
        self.environment = config.get("pinecone_environment")
        self.index_name = config.get("pinecone_index_name", "konveyor-documents")
        self.embedding_dimension = config.get("embedding_dimension", 1536)

        if not self.api_key:
            raise ValueError("Pinecone API key is required")

        # Initialize Pinecone client
        self.pc = Pinecone(api_key=self.api_key)
        self.index = None
        self._initialize_index()

    def _initialize_index(self):
        """Initialize or connect to Pinecone index."""
        try:
            # Check if index exists
            existing_indexes = self.pc.list_indexes()
            index_names = [idx.name for idx in existing_indexes]

            if self.index_name not in index_names:
                logger.info(
                    f"Index {self.index_name} not found, will create on first use"
                )
            else:
                logger.info(f"Connecting to existing index: {self.index_name}")

            # Connect to index (will be created if it doesn't exist)
            self.index = self.pc.Index(self.index_name)

        except Exception as e:
            logger.error(f"Failed to initialize Pinecone index: {str(e)}")
            raise

    async def search(self, query: SearchQuery) -> list[SearchResult]:
        """Perform search operation using Pinecone."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")

            # Prepare query parameters
            query_params = {
                "top_k": query.top_k,
                "include_metadata": True,
                "include_values": False,  # Don't return vectors to save bandwidth
            }

            # Add vector for vector/hybrid search
            if query.embedding:
                query_params["vector"] = query.embedding
            else:
                raise ValueError("Pinecone requires embedding for search operations")

            # Add metadata filters if provided
            if query.filters:
                query_params["filter"] = self._convert_filters(query.filters)

            # Handle hybrid search with sparse vectors (if supported)
            if query.search_type == SearchType.HYBRID and query.text:
                # For hybrid search, we can use Pinecone's sparse-dense functionality
                # This requires the index to be configured for hybrid search
                query_params["sparse_vector"] = self._create_sparse_vector(query.text)

            # Perform search with fallback for unsupported features
            try:
                response = self.index.query(**query_params)
            except PineconeException as e:
                if "sparse values" in str(e) and query.search_type == SearchType.HYBRID:
                    # Fall back to dense vector search if sparse is not supported
                    logger.warning(
                        "Sparse vectors not supported, falling back to dense vector search"
                    )
                    query_params.pop("sparse_vector", None)
                    response = self.index.query(**query_params)
                else:
                    raise

            # Convert results to standardized format
            search_results = []
            for match in response.matches:
                search_result = SearchResult.from_pinecone_result(
                    {
                        "id": match.id,
                        "score": match.score,
                        "metadata": match.metadata or {},
                    }
                )

                # Apply minimum score filter if specified
                if query.min_score is None or search_result.score >= query.min_score:
                    search_results.append(search_result)

            logger.info(f"Pinecone search completed: {len(search_results)} results")
            return search_results

        except PineconeException as e:
            logger.error(f"Pinecone search error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Search operation failed: {str(e)}")
            raise

    def _convert_filters(self, filters: dict[str, Any]) -> dict[str, Any]:
        """Convert generic filters to Pinecone filter format."""
        pinecone_filters = {}

        for key, value in filters.items():
            if isinstance(value, str | int | float | bool):
                pinecone_filters[key] = {"$eq": value}
            elif isinstance(value, list):
                pinecone_filters[key] = {"$in": value}
            elif isinstance(value, dict):
                # Handle range queries
                if "gte" in value or "lte" in value:
                    range_filter = {}
                    if "gte" in value:
                        range_filter["$gte"] = value["gte"]
                    if "lte" in value:
                        range_filter["$lte"] = value["lte"]
                    pinecone_filters[key] = range_filter
                else:
                    pinecone_filters[key] = value

        return pinecone_filters

    def _create_sparse_vector(self, text: str) -> dict[str, Any]:
        """Create sparse vector for hybrid search (simplified implementation)."""
        # This is a simplified implementation
        # In production, you'd use proper sparse vector generation
        words = text.lower().split()
        indices = []
        values = []

        # Simple word-based sparse vector (for demonstration)
        for i, word in enumerate(set(words[:50])):  # Limit to 50 unique words
            indices.append(hash(word) % 10000)  # Simple hash to index
            values.append(words.count(word) / len(words))  # TF normalization

        return {"indices": indices, "values": values}

    async def add_documents(self, documents: list[DocumentChunk]) -> list[str]:
        """Add documents to Pinecone index with batch optimization and robust error handling."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")

            if not documents:
                logger.warning("No documents provided for addition")
                return []

            # Convert DocumentChunk objects to Pinecone format
            vectors = []
            for doc in documents:
                if not doc.embedding:
                    logger.warning(f"Document {doc.id} has no embedding, skipping")
                    continue

                # Validate embedding dimension
                if len(doc.embedding) != self.embedding_dimension:
                    logger.error(
                        f"Document {doc.id} embedding dimension {len(doc.embedding)} doesn't match expected {self.embedding_dimension}"
                    )
                    continue

                metadata = doc.metadata or {}
                metadata.update(
                    {
                        "content": doc.content[:1000],  # Limit content size in metadata
                        "chunk_id": doc.id,
                    }
                )

                # Only add document_id if it's not None (Pinecone doesn't accept null values)
                if doc.document_id is not None:
                    metadata["document_id"] = doc.document_id

                # Only add chunk_index if it's not None (Pinecone doesn't accept null values)
                if doc.chunk_index is not None:
                    metadata["chunk_index"] = doc.chunk_index

                vector = {"id": doc.id, "values": doc.embedding, "metadata": metadata}
                vectors.append(vector)

            if not vectors:
                logger.warning("No valid vectors to upsert after processing documents")
                return []

            # Batch upsert with retry logic and comprehensive error handling
            successful_ids = []
            failed_batches = []
            batch_size = 100  # Pinecone recommended batch size

            for i in range(0, len(vectors), batch_size):
                batch = vectors[i : i + batch_size]
                batch_number = i // batch_size + 1

                try:
                    await self._upsert_batch_with_retry(batch, batch_number)
                    successful_ids.extend([v["id"] for v in batch])

                    # Rate limiting - Pinecone has API limits
                    if i + batch_size < len(vectors):
                        await asyncio.sleep(0.1)  # Small delay between batches

                except Exception as e:
                    logger.error(
                        f"Failed to upsert batch {batch_number} after retries: {str(e)}"
                    )
                    failed_batches.append(batch_number)
                    # Continue with next batch rather than failing completely

            if failed_batches:
                logger.warning(
                    f"Failed to upsert {len(failed_batches)} batches: {failed_batches}"
                )

            logger.info(
                f"Successfully upserted {len(successful_ids)}/{len(documents)} documents to Pinecone"
            )
            return successful_ids

        except Exception as e:
            logger.error(f"Pinecone document addition error: {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.exception("Full traceback for document addition failure:")
            raise

    @retry_with_exponential_backoff(max_retries=3, base_delay=1.0, max_delay=30.0)
    async def _upsert_batch_with_retry(
        self, batch: list[dict[str, Any]], batch_number: int
    ) -> None:
        """
        Upsert a batch of vectors with retry logic.

        Args:
            batch: List of vector dictionaries to upsert
            batch_number: Batch number for logging purposes

        Raises:
            PineconeException: For Pinecone-specific errors
            ConnectionError: For network-related issues
            TimeoutError: For timeout issues
            ValueError: For invalid data
        """
        try:
            logger.debug(f"Upserting batch {batch_number} with {len(batch)} vectors")

            # Validate batch before upsert
            for vector in batch:
                if not isinstance(vector.get("values"), list):
                    raise ValueError(
                        f"Invalid vector values for ID {vector.get('id', 'unknown')}"
                    )
                if len(vector["values"]) != self.embedding_dimension:
                    raise ValueError(
                        f"Vector dimension mismatch for ID {vector.get('id', 'unknown')}"
                    )

            # Perform the upsert operation
            response = self.index.upsert(vectors=batch)

            # Check response for any issues
            if hasattr(response, "upserted_count") and response.upserted_count != len(batch):
                logger.warning(
                    f"Batch {batch_number}: Expected {len(batch)} upserts, got {response.upserted_count}"
                )

            logger.debug(f"Successfully upserted batch {batch_number}")

        except PineconeException as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg or "quota" in error_msg:
                logger.warning(
                    f"Rate limit hit for batch {batch_number}, will retry with backoff"
                )
                raise
            if "timeout" in error_msg:
                logger.warning(f"Timeout for batch {batch_number}, will retry")
                raise TimeoutError(f"Pinecone timeout: {str(e)}")
            if "connection" in error_msg or "network" in error_msg:
                logger.warning(f"Connection error for batch {batch_number}, will retry")
                raise ConnectionError(f"Pinecone connection error: {str(e)}")
            logger.error(
                f"Non-retryable Pinecone error for batch {batch_number}: {str(e)}"
            )
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error during batch {batch_number} upsert: {str(e)}"
            )
            raise

    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in Pinecone index."""
        try:
            # Pinecone upsert handles both insert and update
            result_ids = await self.add_documents([document])
            return len(result_ids) > 0

        except Exception as e:
            logger.error(f"Pinecone document update error: {str(e)}")
            return False

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from Pinecone index."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")

            self.index.delete(ids=[document_id])
            logger.info(f"Successfully deleted document {document_id} from Pinecone")
            return True

        except PineconeException as e:
            logger.error(f"Pinecone document deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Document deletion failed: {str(e)}")
            return False

    async def create_index(
        self, index_name: str, schema: dict[str, Any] | None = None
    ) -> bool:
        """Create Pinecone index with serverless configuration."""
        try:
            # Check if index already exists
            existing_indexes = self.pc.list_indexes()
            index_names = [idx.name for idx in existing_indexes]

            if index_name in index_names:
                logger.info(f"Pinecone index '{index_name}' already exists")
                return True

            # Create serverless index
            self.pc.create_index(
                name=index_name,
                dimension=self.embedding_dimension,
                metric="cosine",  # Best for OpenAI embeddings
                spec=ServerlessSpec(
                    cloud="aws",  # or "gcp"
                    region="us-east-1",  # Choose appropriate region
                ),
            )

            # Wait for index to be ready
            while not self.pc.describe_index(index_name).status["ready"]:
                time.sleep(1)

            logger.info(f"Successfully created Pinecone index: {index_name}")
            return True

        except PineconeException as e:
            logger.error(f"Pinecone index creation error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index creation failed: {str(e)}")
            return False

    async def delete_index(self, index_name: str) -> bool:
        """Delete Pinecone index."""
        try:
            self.pc.delete_index(index_name)
            logger.info(f"Successfully deleted Pinecone index: {index_name}")
            return True

        except PineconeException as e:
            logger.error(f"Pinecone index deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index deletion failed: {str(e)}")
            return False

    async def list_indexes(self) -> list[str]:
        """List available Pinecone indexes."""
        try:
            indexes = self.pc.list_indexes()
            return [idx.name for idx in indexes]

        except PineconeException as e:
            logger.error(f"Pinecone list indexes error: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"List indexes failed: {str(e)}")
            return []

    async def get_index_stats(self, index_name: str) -> dict[str, Any]:
        """Get Pinecone index statistics."""
        try:
            stats = self.pc.describe_index(index_name)
            return {
                "dimension": stats.dimension,
                "metric": stats.metric,
                "status": stats.status,
                "spec": stats.spec,
            }

        except PineconeException as e:
            logger.error(f"Pinecone index stats error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Get index stats failed: {str(e)}")
            return {}

"""
Pinecone provider implementation for unified search interface.

This module provides the Pinecone implementation of the VectorSearchInterface,
optimized for Pinecone's serverless architecture and API patterns.
"""

import logging
import time
from typing import Any, Dict, List, Optional

try:
    from pinecone import Pinecone, ServerlessSpec
    from pinecone.exceptions import PineconeException
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    PineconeException = Exception

from ..interfaces import (
    VectorSearchInterface,
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)

logger = logging.getLogger(__name__)


class PineconeSearchProvider(VectorSearchInterface):
    """Pinecone implementation of VectorSearchInterface."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Pinecone provider.
        
        Args:
            config: Configuration dictionary containing:
                - pinecone_api_key: Pinecone API key
                - pinecone_environment: Pinecone environment (optional for serverless)
                - pinecone_index_name: Index name
                - embedding_dimension: Vector dimension (default: 1536)
        """
        if not PINECONE_AVAILABLE:
            raise ImportError("Pinecone SDK not available. Install pinecone-client to use Pinecone provider.")
        
        self.config = config
        self.api_key = config.get("pinecone_api_key")
        self.environment = config.get("pinecone_environment")
        self.index_name = config.get("pinecone_index_name", "konveyor-documents")
        self.embedding_dimension = config.get("embedding_dimension", 1536)
        
        if not self.api_key:
            raise ValueError("Pinecone API key is required")
        
        # Initialize Pinecone client
        self.pc = Pinecone(api_key=self.api_key)
        self.index = None
        self._initialize_index()

    def _initialize_index(self):
        """Initialize or connect to Pinecone index."""
        try:
            # Check if index exists
            existing_indexes = self.pc.list_indexes()
            index_names = [idx.name for idx in existing_indexes]
            
            if self.index_name not in index_names:
                logger.info(f"Index {self.index_name} not found, will create on first use")
            else:
                logger.info(f"Connecting to existing index: {self.index_name}")
            
            # Connect to index (will be created if it doesn't exist)
            self.index = self.pc.Index(self.index_name)
            
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone index: {str(e)}")
            raise

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform search operation using Pinecone."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")
            
            # Prepare query parameters
            query_params = {
                "top_k": query.top_k,
                "include_metadata": True,
                "include_values": False,  # Don't return vectors to save bandwidth
            }
            
            # Add vector for vector/hybrid search
            if query.embedding:
                query_params["vector"] = query.embedding
            else:
                raise ValueError("Pinecone requires embedding for search operations")
            
            # Add metadata filters if provided
            if query.filters:
                query_params["filter"] = self._convert_filters(query.filters)
            
            # Handle hybrid search with sparse vectors (if supported)
            if query.search_type == SearchType.HYBRID and query.text:
                # For hybrid search, we can use Pinecone's sparse-dense functionality
                # This requires the index to be configured for hybrid search
                query_params["sparse_vector"] = self._create_sparse_vector(query.text)

            # Perform search with fallback for unsupported features
            try:
                response = self.index.query(**query_params)
            except PineconeException as e:
                if "sparse values" in str(e) and query.search_type == SearchType.HYBRID:
                    # Fall back to dense vector search if sparse is not supported
                    logger.warning("Sparse vectors not supported, falling back to dense vector search")
                    query_params.pop("sparse_vector", None)
                    response = self.index.query(**query_params)
                else:
                    raise
            
            # Convert results to standardized format
            search_results = []
            for match in response.matches:
                search_result = SearchResult.from_pinecone_result({
                    "id": match.id,
                    "score": match.score,
                    "metadata": match.metadata or {}
                })
                
                # Apply minimum score filter if specified
                if query.min_score is None or search_result.score >= query.min_score:
                    search_results.append(search_result)
            
            logger.info(f"Pinecone search completed: {len(search_results)} results")
            return search_results
            
        except PineconeException as e:
            logger.error(f"Pinecone search error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Search operation failed: {str(e)}")
            raise

    def _convert_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Convert generic filters to Pinecone filter format."""
        pinecone_filters = {}
        
        for key, value in filters.items():
            if isinstance(value, (str, int, float, bool)):
                pinecone_filters[key] = {"$eq": value}
            elif isinstance(value, list):
                pinecone_filters[key] = {"$in": value}
            elif isinstance(value, dict):
                # Handle range queries
                if "gte" in value or "lte" in value:
                    range_filter = {}
                    if "gte" in value:
                        range_filter["$gte"] = value["gte"]
                    if "lte" in value:
                        range_filter["$lte"] = value["lte"]
                    pinecone_filters[key] = range_filter
                else:
                    pinecone_filters[key] = value
        
        return pinecone_filters

    def _create_sparse_vector(self, text: str) -> Dict[str, Any]:
        """Create sparse vector for hybrid search (simplified implementation)."""
        # This is a simplified implementation
        # In production, you'd use proper sparse vector generation
        words = text.lower().split()
        indices = []
        values = []
        
        # Simple word-based sparse vector (for demonstration)
        for i, word in enumerate(set(words[:50])):  # Limit to 50 unique words
            indices.append(hash(word) % 10000)  # Simple hash to index
            values.append(words.count(word) / len(words))  # TF normalization
        
        return {
            "indices": indices,
            "values": values
        }

    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Add documents to Pinecone index with batch optimization."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")
            
            # Convert DocumentChunk objects to Pinecone format
            vectors = []
            for doc in documents:
                if not doc.embedding:
                    logger.warning(f"Document {doc.id} has no embedding, skipping")
                    continue
                
                metadata = doc.metadata or {}
                metadata.update({
                    "content": doc.content[:1000],  # Limit content size in metadata
                    "chunk_id": doc.id,
                })

                # Only add document_id if it's not None (Pinecone doesn't accept null values)
                if doc.document_id is not None:
                    metadata["document_id"] = doc.document_id

                # Only add chunk_index if it's not None (Pinecone doesn't accept null values)
                if doc.chunk_index is not None:
                    metadata["chunk_index"] = doc.chunk_index
                
                vector = {
                    "id": doc.id,
                    "values": doc.embedding,
                    "metadata": metadata
                }
                vectors.append(vector)
            
            # Batch upsert with retry logic
            successful_ids = []
            batch_size = 100  # Pinecone recommended batch size
            
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i + batch_size]
                try:
                    self.index.upsert(vectors=batch)
                    successful_ids.extend([v["id"] for v in batch])
                    
                    # Rate limiting - Pinecone has API limits
                    if i + batch_size < len(vectors):
                        time.sleep(0.1)  # Small delay between batches
                        
                except PineconeException as e:
                    logger.error(f"Failed to upsert batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch rather than failing completely
            
            logger.info(f"Successfully upserted {len(successful_ids)}/{len(documents)} documents to Pinecone")
            return successful_ids
            
        except Exception as e:
            logger.error(f"Pinecone document addition error: {str(e)}")
            raise

    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document in Pinecone index."""
        try:
            # Pinecone upsert handles both insert and update
            result_ids = await self.add_documents([document])
            return len(result_ids) > 0
            
        except Exception as e:
            logger.error(f"Pinecone document update error: {str(e)}")
            return False

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from Pinecone index."""
        try:
            if not self.index:
                raise RuntimeError("Pinecone index not initialized")
            
            self.index.delete(ids=[document_id])
            logger.info(f"Successfully deleted document {document_id} from Pinecone")
            return True
            
        except PineconeException as e:
            logger.error(f"Pinecone document deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Document deletion failed: {str(e)}")
            return False

    async def create_index(self, index_name: str, schema: Optional[Dict[str, Any]] = None) -> bool:
        """Create Pinecone index with serverless configuration."""
        try:
            # Check if index already exists
            existing_indexes = self.pc.list_indexes()
            index_names = [idx.name for idx in existing_indexes]
            
            if index_name in index_names:
                logger.info(f"Pinecone index '{index_name}' already exists")
                return True
            
            # Create serverless index
            self.pc.create_index(
                name=index_name,
                dimension=self.embedding_dimension,
                metric="cosine",  # Best for OpenAI embeddings
                spec=ServerlessSpec(
                    cloud="aws",  # or "gcp"
                    region="us-east-1"  # Choose appropriate region
                )
            )
            
            # Wait for index to be ready
            while not self.pc.describe_index(index_name).status['ready']:
                time.sleep(1)
            
            logger.info(f"Successfully created Pinecone index: {index_name}")
            return True
            
        except PineconeException as e:
            logger.error(f"Pinecone index creation error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index creation failed: {str(e)}")
            return False

    async def delete_index(self, index_name: str) -> bool:
        """Delete Pinecone index."""
        try:
            self.pc.delete_index(index_name)
            logger.info(f"Successfully deleted Pinecone index: {index_name}")
            return True
            
        except PineconeException as e:
            logger.error(f"Pinecone index deletion error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Index deletion failed: {str(e)}")
            return False

    async def list_indexes(self) -> List[str]:
        """List available Pinecone indexes."""
        try:
            indexes = self.pc.list_indexes()
            return [idx.name for idx in indexes]
            
        except PineconeException as e:
            logger.error(f"Pinecone list indexes error: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"List indexes failed: {str(e)}")
            return []

    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """Get Pinecone index statistics."""
        try:
            stats = self.pc.describe_index(index_name)
            return {
                "dimension": stats.dimension,
                "metric": stats.metric,
                "status": stats.status,
                "spec": stats.spec
            }
            
        except PineconeException as e:
            logger.error(f"Pinecone index stats error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Get index stats failed: {str(e)}")
            return {}

"""
Unified search service that provides backward compatibility.

This service maintains the same interface as the original SearchService
while allowing different vector database providers underneath.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from .interfaces import (
    VectorSearchInterface, 
    EmbeddingInterface, 
    SearchServiceInterface,
    SearchQuery,
    SearchType,
    SearchResult,
    DocumentChunk
)

logger = logging.getLogger(__name__)


class UnifiedSearchService(SearchServiceInterface):
    """
    Unified search service that abstracts different vector database providers.
    
    This service maintains backward compatibility with the existing SearchService
    interface while allowing runtime switching between different providers.
    """

    def __init__(
        self,
        vector_search: VectorSearchInterface,
        embedding_service: EmbeddingInterface,
        config: Dict[str, Any]
    ):
        """
        Initialize the unified search service.
        
        Args:
            vector_search: Vector search provider instance
            embedding_service: Embedding service instance
            config: Configuration dictionary
        """
        self.vector_search_provider = vector_search
        self.embedding_service = embedding_service
        self.config = config
        self.index_name = config.get("index_name", "konveyor-documents")

        # Backward compatibility attributes
        self.client = vector_search  # For existing code that accesses .client
        self.openai_client = embedding_service  # For existing code that accesses .openai_client

    async def semantic_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform semantic search using vector embeddings."""
        try:
            # Generate embedding for the query
            embedding = await self.embedding_service.generate_embedding(query)
            
            # Create search query
            search_query = SearchQuery(
                text=query,
                search_type=SearchType.VECTOR,
                top_k=top_k,
                filters=filters,
                embedding=embedding
            )
            
            # Perform search
            results = await self.vector_search_provider.search(search_query)
            
            logger.info(f"Semantic search completed: {len(results)} results for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {str(e)}")
            raise

    async def hybrid_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform hybrid search combining vector and keyword search."""
        try:
            # Generate embedding for the query
            embedding = await self.embedding_service.generate_embedding(query)
            
            # Create search query
            search_query = SearchQuery(
                text=query,
                search_type=SearchType.HYBRID,
                top_k=top_k,
                filters=filters,
                embedding=embedding
            )
            
            # Perform search
            results = await self.vector_search_provider.search(search_query)
            
            logger.info(f"Hybrid search completed: {len(results)} results for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            raise

    async def vector_search(
        self, 
        query: str, 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Perform pure vector search."""
        return await self.semantic_search(query, top_k, filters)

    async def index_document_chunk(
        self,
        chunk_id: str,
        document_id: str,
        content: str,
        chunk_index: int,
        metadata: Dict[str, Any],
        embedding: Optional[List[float]] = None,
    ) -> bool:
        """Index a document chunk."""
        try:
            # Generate embedding if not provided
            if embedding is None:
                embedding = await self.embedding_service.generate_embedding(content)
            
            # Create document chunk
            document_chunk = DocumentChunk(
                id=chunk_id,
                content=content,
                embedding=embedding,
                metadata=metadata,
                document_id=document_id,
                chunk_index=chunk_index
            )
            
            # Add to index
            result_ids = await self.vector_search_provider.add_documents([document_chunk])
            
            success = len(result_ids) > 0
            if success:
                logger.info(f"Successfully indexed chunk: {chunk_id}")
            else:
                logger.error(f"Failed to index chunk: {chunk_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Failed to index document chunk {chunk_id}: {str(e)}")
            return False

    async def create_search_index(self, index_name: Optional[str] = None) -> bool:
        """Create search index with comprehensive error handling."""
        index_name = index_name or self.index_name

        try:
            logger.info(f"Attempting to create search index: {index_name}")

            # Create index with default schema and comprehensive error handling
            success = await self.vector_search_provider.create_index(index_name)

            if success:
                logger.info(f"Successfully created search index: {index_name}")
                return True
            else:
                logger.error(f"Index creation returned failure for: {index_name}")
                return False

        except ImportError as e:
            logger.error(f"Provider SDK not available for index creation: {str(e)}")
            logger.error(f"Ensure the required SDK is installed for provider: {self.vector_search_provider.__class__.__name__}")
            return False
        except ValueError as e:
            logger.error(f"Invalid configuration for index creation '{index_name}': {str(e)}")
            logger.error(f"Check provider configuration and index name format")
            return False
        except ConnectionError as e:
            logger.error(f"Connection failed during index creation '{index_name}': {str(e)}")
            logger.error(f"Verify network connectivity and provider endpoint configuration")
            return False
        except TimeoutError as e:
            logger.error(f"Timeout during index creation '{index_name}': {str(e)}")
            logger.error(f"Index creation may take longer than expected, check provider status")
            return False
        except PermissionError as e:
            logger.error(f"Permission denied for index creation '{index_name}': {str(e)}")
            logger.error(f"Verify API key permissions and account limits")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during index creation '{index_name}': {str(e)}")
            logger.error(f"Provider: {self.vector_search_provider.__class__.__name__}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.exception("Full traceback for index creation failure:")
            return False

    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text."""
        return await self.embedding_service.generate_embedding(text)

    # Backward compatibility methods
    def search(self, *args, **kwargs):
        """Backward compatibility method for synchronous search."""
        # Convert old search parameters to new format
        if args:
            query = args[0]
        else:
            query = kwargs.get('search_text', kwargs.get('query', ''))
            
        top_k = kwargs.get('top', kwargs.get('top_k', 5))
        
        # Run async method synchronously
        return self._run_async(self.hybrid_search(query, top_k))

    def upload_documents(self, documents: List[Dict[str, Any]]):
        """Backward compatibility method for document upload."""
        # Convert documents to DocumentChunk format
        chunks = []
        for doc in documents:
            chunk = DocumentChunk(
                id=doc.get('id', ''),
                content=doc.get('content', ''),
                embedding=doc.get('content_vector', doc.get('embedding')),
                metadata=doc.get('metadata', {}),
                document_id=doc.get('document_id'),
                chunk_index=doc.get('chunk_index')
            )
            chunks.append(chunk)
        
        # Run async method synchronously
        return self._run_async(self.vector_search_provider.add_documents(chunks))

    def _run_async(self, coro):
        """Helper to run async methods synchronously."""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a new task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, coro)
                    return future.result()
            else:
                return loop.run_until_complete(coro)
        except RuntimeError:
            # No event loop, create one
            return asyncio.run(coro)

    # Provider-specific access methods
    def get_provider_info(self) -> Dict[str, str]:
        """Get information about current providers."""
        return {
            "vector_search": self.vector_search_provider.__class__.__name__,
            "embedding_service": self.embedding_service.__class__.__name__,
            "index_name": self.index_name
        }

    async def health_check(self) -> Dict[str, bool]:
        """Check health of all providers."""
        health = {}
        
        try:
            # Check vector search health
            indexes = await self.vector_search_provider.list_indexes()
            health["vector_search"] = True
        except Exception as e:
            logger.error(f"Vector search health check failed: {str(e)}")
            health["vector_search"] = False
        
        try:
            # Check embedding service health
            test_embedding = await self.embedding_service.generate_embedding("test")
            health["embedding_service"] = len(test_embedding) > 0
        except Exception as e:
            logger.error(f"Embedding service health check failed: {str(e)}")
            health["embedding_service"] = False
        
        return health


class BackwardCompatibilityWrapper:
    """
    Wrapper that maintains backward compatibility during migration.
    
    This wrapper can be used to replace existing SearchService instances
    while maintaining the same interface.
    """

    def __init__(self, search_service: SearchServiceInterface):
        """
        Initialize compatibility wrapper.
        
        Args:
            search_service: Unified search service instance
        """
        self.search_service = search_service
        
        # Expose attributes for backward compatibility
        self.client = getattr(search_service, 'client', None)
        self.openai_client = getattr(search_service, 'openai_client', None)
        self.index_name = getattr(search_service, 'index_name', 'konveyor-documents')

    # Synchronous methods for backward compatibility
    def search(self, *args, **kwargs):
        """Backward compatible search method."""
        return self.search_service.search(*args, **kwargs)

    def upload_documents(self, documents: List[Dict[str, Any]]):
        """Backward compatible document upload method."""
        return self.search_service.upload_documents(documents)

    def index_document_chunk(self, *args, **kwargs):
        """Backward compatible document chunk indexing."""
        return self.search_service._run_async(
            self.search_service.index_document_chunk(*args, **kwargs)
        )

    def create_search_index(self, index_name: Optional[str] = None):
        """Backward compatible index creation."""
        return self.search_service._run_async(
            self.search_service.create_search_index(index_name)
        )

    def generate_embedding(self, text: str):
        """Backward compatible embedding generation."""
        return self.search_service._run_async(
            self.search_service.generate_embedding(text)
        )

    # Async methods
    async def async_search(self, *args, **kwargs):
        """Async search method."""
        return await self.search_service.hybrid_search(*args, **kwargs)

    async def async_upload_documents(self, documents: List[Dict[str, Any]]):
        """Async document upload method."""
        # Convert to DocumentChunk format
        chunks = []
        for doc in documents:
            chunk = DocumentChunk(
                id=doc.get('id', ''),
                content=doc.get('content', ''),
                embedding=doc.get('content_vector', doc.get('embedding')),
                metadata=doc.get('metadata', {}),
                document_id=doc.get('document_id'),
                chunk_index=doc.get('chunk_index')
            )
            chunks.append(chunk)
        
        return await self.search_service.vector_search_provider.add_documents(chunks)

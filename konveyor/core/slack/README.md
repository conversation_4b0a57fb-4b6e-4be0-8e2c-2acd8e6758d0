# Slack Integration

This module contains the direct integration with Slack for the Konveyor project.

## Structure

- `client.py`: Slack client for sending and receiving messages
- `webhook.py`: Webhook handler for Slack events
- `slack_manifest.json`: Slack app manifest for configuration

## Usage

This module is used for direct Slack integration. For Bot Framework integration, see the `konveyor.core.botframework` module.

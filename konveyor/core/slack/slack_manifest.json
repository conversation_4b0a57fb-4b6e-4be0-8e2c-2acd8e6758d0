{"display_information": {"name": "Konveyor", "description": "AI assistant for documentation and code understanding", "background_color": "#FFFFFF", "long_description": "Konveyor helps users find information in documentation and understand code through natural language conversations."}, "features": {"bot_user": {"display_name": "konveyor-bot", "always_online": true}}, "oauth_config": {"scopes": {"bot": ["chat:write", "im:history", "channels:history", "groups:history", "app_mentions:read"]}}, "settings": {"interactivity": {"is_enabled": true}, "org_deploy_enabled": false, "socket_mode_enabled": false, "token_rotation_enabled": false}}
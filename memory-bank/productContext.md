# Product Context: Konveyor

## Why This Project Exists

Software engineer onboarding is a costly, inefficient process plagued by scattered documentation, knowledge silos, and lack of structured support. Organizations routinely lose over $30,000 per new engineer in lost productivity, mentor time, and delayed project contributions. Traditional onboarding tools and generic AI assistants fail to address the unique, context-heavy challenges faced by engineering teams.

Konveyor was created to directly solve these problems by providing a domain-specific, AI-powered knowledge transfer agent that accelerates onboarding, reduces costs, and improves the new hire experience for software engineers.

## Problems Solved

- Lack of comprehensive, up-to-date, and centralized engineering documentation
- Knowledge silos and ineffective handover processes
- Overwhelming or unstructured onboarding experiences
- Difficulty understanding complex, undocumented codebases
- Limited access to mentorship and context from senior engineers
- Communication barriers in remote and cross-team environments
- Inadequate search and fragmented tool integration for engineering knowledge

## How It Should Work

- Engineers interact with Konveyor via familiar chat interfaces (e.g., Slack)
- Users can ask context-specific questions about code, architecture, and processes
- The agent provides answers that incorporate organizational context, design decisions, and up-to-date documentation
- Code snippets can be explained with architectural insights and references to relevant documentation
- Knowledge gaps are detected and personalized learning paths are suggested
- All responses are actionable, with proper citations and links to source material

## User Experience Goals

- Minimize time-to-productivity for new engineers
- Reduce cognitive overload by surfacing only the most relevant information
- Enable self-service onboarding, reducing reliance on senior engineers
- Provide clear, contextual, and actionable answers to engineering questions
- Integrate seamlessly with existing engineering workflows and tools
- Support progressive disclosure: offer high-level overviews with the option to drill down into details

## Unique Differentiators

- Purpose-built for software engineering onboarding, not a generic AI assistant
- Deep integration with existing infrastructure (Azure, Semantic Kernel, Slack)
- Contextual understanding of organizational terminology, architecture, and code patterns
- Agentic framework with specialized tools for documentation navigation, code understanding, and knowledge gap analysis
- Focus on both passive (documentation) and active (how-to, context-specific) knowledge delivery

## Vision

Konveyor aims to transform software engineer onboarding from a fragmented, high-cost process into a streamlined, AI-augmented experience that empowers engineers to become productive, confident contributors in record time.

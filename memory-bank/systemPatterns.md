# System Patterns: Konveyor

## System Architecture

Konveyor is architected as a modular, AI-driven onboarding and knowledge transfer platform for software engineers, built on Django and Azure. The system is designed for extensibility, security, and deep integration with cloud services.

### Key Architectural Patterns

- **Modular Django Backend**:
  - Apps for documents, search, RAG (Retrieve–Augment–Generate), and bot integration.
  - Each app encapsulates domain logic and exposes REST API endpoints.
  - Core services and adapters abstract Azure SDKs and business logic.

- **Semantic Kernel Framework**:
  - Comprehensive skills architecture in `konveyor/skills/` directory with agent orchestration.
  - Full integration with Azure OpenAI using credentials from Key Vault with fallback mechanisms.
  - Volatile memory system for session-based data storage with extensibility for persistent storage.
  - Complete agent orchestration layer with SkillRegistry and AgentOrchestratorSkill for intelligent request routing.
  - Unified interfaces for conversation management, message formatting, and response generation.
  - PascalCase naming convention for Semantic Kernel skills with comprehensive documentation.

- **RAG Workflow**:
  - Document ingestion: files are uploaded, parsed, chunked, and stored in Azure Blob Storage.
  - Indexing: document chunks are indexed in Azure Cognitive Search for semantic and hybrid search.
  - Query flow: user queries are routed through SearchService to retrieve top-k relevant chunks.
  - LLM response: retrieved context is combined with prompts and sent to Azure OpenAI for answer generation.
  - Bot integration: chat interfaces (Slack, Bot Framework) connect users to the RAG pipeline.

- **Infrastructure-as-Code**:
  - All Azure resources (App Service, Cognitive Search, Blob Storage, PostgreSQL, Key Vault) are provisioned and operational via Terraform.
  - Environments for dev, test, and prod are managed as code with successful production deployment.
  - GitHub Actions workflows for CI/CD with feature-branch naming conventions, PR review requirements, and automated production deployment.
  - Docker containerization with GitHub Container Registry (GHCR) for reliable deployment.

- **Security Patterns**:
  - HTTPS enforced for all endpoints.
  - OAuth2/JWT for API authentication.
  - Secrets managed in Azure Key Vault.
  - Azure RBAC for resource access control.
  - Fallback mechanism for credentials when Key Vault is unavailable.

- **Testing Strategy**:
  - Unit tests for core logic and services.
  - Integration tests for service and API layers.
  - End-to-end tests for RAG workflows and bot interactions.
  - Production validation with real Slack workspace integration.
  - Health check endpoints for monitoring application status.

### Component Relationships

- **konveyor/apps/documents**: Handles document ingestion, parsing, chunking, and storage. Delegates to core DocumentService.
- **konveyor/apps/search**: Manages semantic and hybrid search, batch indexing, and search endpoints. Wraps core SearchService.
- **konveyor/apps/rag**: Orchestrates RAG workflows, conversation management, and context retrieval. Uses core RAGService.
- **konveyor/apps/bot**: Integrates with Slack and Bot Framework for chat-based access. Implements bot logic and secure credential storage.
- **konveyor/core**: Contains shared utilities, Azure adapters, core business logic, and unified interfaces:
  - **conversation/**: Unified conversation management with multiple storage options.
  - **formatters/**: Message formatting for different platforms (Slack, Markdown).
  - **generation/**: Response generation with RAG and direct generation support.
  - **azure_utils/**: Azure service clients and unified OpenAI integration.
  - **botframework/**: Bot Framework integration with credential management.
- **konveyor/skills**: Houses comprehensive Semantic Kernel skills and framework setup:
  - **setup.py**: Initializes Semantic Kernel with Azure OpenAI integration and memory configuration.
  - **agent_orchestrator/**: Complete orchestration layer with SkillRegistry and AgentOrchestratorSkill.
  - **ChatSkill.py**: Advanced chat functionality with conversation management and Slack formatting.
  - **documentation_navigator/**: DocumentationNavigatorSkill with search, answer generation, and citations.
  - **knowledge_analyzer/**: KnowledgeGapAnalyzerSkill with taxonomy mapping and learning paths.
  - **examples/**: Contains example implementations and usage patterns.
  - **CodeUnderstandingSkill**: Planned skill for code parsing and explanation (not yet implemented).
- **Konveyor-infra**: Infrastructure-as-Code for all Azure resources, supporting multi-environment deployment.

### Critical Implementation Paths

- **Document Processing**: Upload → parsing → chunking → Blob Storage → Cognitive Search indexing
- **RAG Query Flow**: User query → SearchService → retrieve chunks → RAGService → OpenAI → formatted response
- **Production Bot Integration**: Slack message → Azure App Service → Bot Framework → Agent Orchestration → Skill routing → response formatting → Slack response
- **Semantic Kernel Workflow**: User message → Bot Framework → AgentOrchestratorSkill → SkillRegistry → appropriate skill → Azure OpenAI → formatted response
- **Credential Management**: Request → AzureConfig → Key Vault (primary) → Environment variables (fallback) → service initialization
- **Conversation Flow**: Message → ConversationInterface → memory storage → context retrieval → response generation
- **Skill Orchestration**: Intent analysis → skill selection → parameter extraction → skill invocation → response formatting
- **Production Deployment**: GitHub Actions → Docker build → GHCR push → Terraform apply → Azure App Service deployment → health check validation

### Design for Extensibility

- **Modular Skills Architecture**: New AI capabilities can be easily added as Semantic Kernel skills with automatic registration.
- **Unified Interfaces**: ConversationInterface, FormatterInterface, and ResponseGeneratorInterface enable easy extension.
- **Storage Flexibility**: Support for multiple storage backends (in-memory, Azure, Redis, Cosmos DB) through factory pattern.
- **Platform Integration**: Additional chat platforms can be integrated by extending bot services and formatters.
- **Document Processing**: New document types and sources can be added via modular adapters.
- **Memory System**: Can be extended from volatile to persistent storage with minimal code changes.
- **Testing Framework**: Comprehensive test coverage enables confident extension and refactoring.
- **Future Enhancements**: Real-time updates, multi-tenant onboarding, React SPA, analytics dashboards, Teams integration.

## Summary

Konveyor's architecture successfully combines modular Django apps, comprehensive Azure integration, advanced RAG patterns, and a mature Semantic Kernel framework to deliver a production-ready, scalable onboarding solution for software engineers. The system demonstrates excellent separation of concerns through unified interfaces, comprehensive skill architecture, and robust testing frameworks.

Key architectural achievements:
- **Unified Interface Layer**: Eliminates redundancy while maintaining flexibility and extensibility
- **Comprehensive Skills Architecture**: Complete agent orchestration with intelligent request routing
- **Production-Ready Infrastructure**: Full Azure integration with secure credential management
- **Extensible Design**: Factory patterns and modular architecture enable easy enhancement
- **Testing Maturity**: Comprehensive test coverage provides confidence in system reliability

The system is designed for rapid iteration, cloud-native deployment, and deep integration with engineering workflows, while providing a robust, tested framework for adding new AI capabilities. It represents a mature implementation ready for production deployment and end-user testing.

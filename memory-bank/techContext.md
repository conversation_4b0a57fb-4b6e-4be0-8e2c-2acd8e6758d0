# Technical Context: Konveyor

## Technologies Used

- **Backend**: Python 3.10+, Django web framework, Django REST Framework
- **AI/LLM**:
  - Azure OpenAI (embeddings, chat)
  - Semantic Kernel 1.0.1 (agent orchestration, skills, memory)
  - AzureChatCompletion and AzureTextEmbedding services
  - VolatileMemoryStore for session-based data storage
- **Azure SDKs**:
  - azure-identity for authentication
  - azure-ai-formrecognizer for document processing
  - azure-search-documents for cognitive search
  - azure-storage-blob for blob storage
  - azure-keyvault-secrets for secure credential management
- **Bot Integration**:
  - Slack SDK for Slack integration
  - botbuilder-core and botbuilder-integration-aiohttp for Bot Framework
  - BotFrameworkAdapter for message handling
- **Testing**: pytest, pytest-asyncio, pytest-aiohttp
- **Node.js Tooling**: Express, OpenAI, Anthropic SDK, CLI utilities (for development automation and PRD parsing)
- **Infrastructure**:
  - Terraform for Azure resource provisioning (App Service, Cognitive Search, Blob Storage, PostgreSQL, Key Vault)
  - GitHub Actions for CI/CD with branch naming and commit message validation

## Development Setup

- Python dependencies managed via requirements.txt and split environment files (base, development, production, testing)
- Node.js scripts for development automation (see package.json)
- Semantic Kernel skills and setup in `konveyor/skills/`:
  - `setup.py` for Kernel initialization and Azure OpenAI integration
  - `ChatSkill.py` for basic chat functionality and utility functions
  - Example skills in `examples/` directory
  - PascalCase naming convention for Semantic Kernel skills
- Environment variables for Azure OpenAI and other secrets (optionally stored in Azure Key Vault)
- Fallback mechanism for credentials when Key Vault is unavailable
- Local development uses SQLite; production uses PostgreSQL
- Task-master for task management and tracking implementation progress

## Technical Constraints

- Requires Azure account and provisioned services for production deployment
- Azure OpenAI API keys and endpoints must be configured via Key Vault or environment variables
- Semantic Kernel 1.0.1 compatibility maintained across all implementations
- Volatile memory system operational for development; persistent memory stores planned for production optimization
- All secrets managed via Azure Key Vault with fallback to environment variables
- CodeUnderstandingSkill implementation pending for complete feature set
- PEP 8 naming conventions for Python code and PascalCase for Semantic Kernel Skills
- Comprehensive testing required for all new features and integrations

## Tool Usage Patterns

- **Semantic Kernel Framework** (fully operational):
  - `create_kernel()` function in `konveyor/skills/setup.py` configures Semantic Kernel with Azure OpenAI services
  - Complete Key Vault integration with robust fallback to environment variables
  - Volatile memory store for session-based data storage with extensibility for persistent storage
  - AgentOrchestratorSkill and SkillRegistry provide intelligent request routing

- **Skills Architecture** (comprehensive implementation):
  - Skills use `@kernel_function` decorator to expose functions to Semantic Kernel
  - ChatSkill: Advanced conversation management with Slack formatting and utility functions
  - DocumentationNavigatorSkill: Search, answer generation, and citation handling
  - KnowledgeGapAnalyzerSkill: Taxonomy mapping and learning path generation
  - Unified interfaces for conversation, formatting, and response generation

- **Bot Framework Integration** (production-ready):
  - Complete BotFrameworkAdapter integration with message handling and error management
  - KonveyorBot class with full skill orchestration and member addition handling
  - Secure credential management via Azure Key Vault and environment variable fallback

- **Core Services** (modular and extensible):
  - Document ingestion, search, and RAG workflows with comprehensive testing
  - Unified interfaces enable easy extension and platform integration
  - Factory patterns for creating components based on configuration

## Dependencies

- Python:
  - Django and Django REST Framework for web application framework
  - Azure SDKs for cloud service integration
  - Semantic Kernel 1.0.1 for agent orchestration and skills
  - Azure OpenAI for LLM capabilities
  - Bot Framework (botbuilder-core, botbuilder-integration-aiohttp) for chat integration
  - Slack SDK for Slack integration
  - pytest, pytest-asyncio, pytest-aiohttp for testing
- Node.js:
  - Express for web server
  - OpenAI and Anthropic SDKs for AI capabilities
  - task-master for task management and tracking
  - CLI utilities for development automation
- Azure:
  - Cognitive Search for document indexing and search
  - Blob Storage for document storage
  - Key Vault for secret management
  - PostgreSQL for production database
  - App Service for application hosting

## CI/CD and Testing

- GitHub Actions for continuous integration and deployment:
  - Linting and code quality checks
  - Unit and integration tests
  - Docker image building and pushing to GitHub Container Registry (GHCR)
  - Automated deployment to Azure App Service
  - Feature-branch naming conventions enforcement (feat/task-<id>-<desc>)
  - PR review requirements
  - Conventional Commits formatting validation
- Terraform for continuous deployment and environment management:
  - Infrastructure-as-Code for all Azure resources with successful production deployment
  - Environment-specific configurations (dev, test, prod) all operational
  - Secure credential management with Azure Key Vault integration
- Testing strategy:
  - Unit tests for core services and Semantic Kernel skills
  - Integration tests for service and API layers
  - End-to-end tests for RAG workflows and bot interactions
  - Production validation with real Slack workspace integration
  - Health check endpoints for monitoring application status

## Summary

Konveyor successfully leverages a modern Python backend, comprehensive Azure cloud services, and a mature Semantic Kernel framework to deliver a production-ready, extensible onboarding solution for software engineers. The system demonstrates excellent integration between Django's web application capabilities and Semantic Kernel's AI orchestration to create a powerful, tested knowledge transfer platform.

Key technical achievements include:
- **Complete Semantic Kernel 1.0.1 integration** with Azure OpenAI and comprehensive skill architecture
- **Production-ready skill ecosystem** with ChatSkill, DocumentationNavigatorSkill, and KnowledgeGapAnalyzerSkill
- **Robust agent orchestration** with SkillRegistry and AgentOrchestratorSkill for intelligent request routing
- **Unified interface architecture** eliminating redundancy while maintaining flexibility
- **Secure credential management** with Azure Key Vault and comprehensive fallback mechanisms
- **Complete Bot Framework integration** with full skill orchestration and error handling
- **Infrastructure-as-Code maturity** with Terraform and comprehensive GitHub Actions CI/CD
- **Testing excellence** with comprehensive unit, integration, and mock testing strategies
- **App modernization success** with unified interfaces and modular architecture

The system represents a mature, tested implementation that is **successfully deployed and operational in production** on Azure App Service with full Slack integration. It provides a robust, extensible framework for adding new AI capabilities through Semantic Kernel skills while maintaining high code quality and reliability standards in a live production environment.

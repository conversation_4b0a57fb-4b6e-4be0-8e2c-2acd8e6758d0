{"name": "konveyor", "version": "1.0.0", "description": "<PERSON>n<PERSON>or is an intelligent AI-powered assistant designed to streamline documentation access and code comprehension. It helps developers navigate through complex codebases, understand documentation structures, and provides contextual insights through natural language interactions. The bot supports code analysis, documentation search, and real-time assistance for development teams.", "author": "<PERSON>", "type": "module", "scripts": {"dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "cli-table3": "^0.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0"}}
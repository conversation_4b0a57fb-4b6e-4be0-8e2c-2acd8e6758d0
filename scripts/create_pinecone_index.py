#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create Pinecone index for Konveyor migration.

This script creates the necessary Pinecone index with the correct configuration
for the Konveyor search abstraction layer.
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Load environment variables
load_dotenv(dotenv_path=PROJECT_ROOT / ".env")

def create_pinecone_index():
    """Create Pinecone index with proper configuration."""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        # Get configuration from environment
        api_key = os.getenv("PINECONE_API_KEY")
        index_name = os.getenv("PINECONE_INDEX_NAME", "konveyor-documents")
        
        if not api_key:
            print("Error: PINECONE_API_KEY not found in environment variables")
            return False
        
        print(f"Creating Pinecone index: {index_name}")
        
        # Initialize Pinecone
        pc = Pinecone(api_key=api_key)
        
        # Check if index already exists
        existing_indexes = pc.list_indexes()
        index_names = [idx.name for idx in existing_indexes]
        
        if index_name in index_names:
            print(f"Index {index_name} already exists!")
            
            # Get index stats
            index = pc.Index(index_name)
            stats = index.describe_index_stats()
            print(f"Index stats: {stats}")
            return True
        
        # Create index with serverless configuration
        print(f"Creating new index: {index_name}")
        pc.create_index(
            name=index_name,
            dimension=1536,  # OpenAI embedding dimension
            metric="cosine",  # Best for OpenAI embeddings
            spec=ServerlessSpec(
                cloud="aws",
                region="us-east-1"
            )
        )
        
        # Wait for index to be ready
        print("Waiting for index to be ready...")
        max_wait = 60  # Maximum wait time in seconds
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                index = pc.Index(index_name)
                stats = index.describe_index_stats()
                print(f"Index is ready! Stats: {stats}")
                return True
            except Exception as e:
                print(f"Index not ready yet, waiting... ({wait_time}s)")
                time.sleep(5)
                wait_time += 5
        
        print(f"Warning: Index creation may still be in progress after {max_wait}s")
        return True
        
    except ImportError:
        print("Error: Pinecone SDK not available. Install with: pip install pinecone")
        return False
    except Exception as e:
        print(f"Error creating Pinecone index: {e}")
        return False


def list_pinecone_indexes():
    """List all available Pinecone indexes."""
    try:
        from pinecone import Pinecone
        
        api_key = os.getenv("PINECONE_API_KEY")
        if not api_key:
            print("Error: PINECONE_API_KEY not found in environment variables")
            return False
        
        pc = Pinecone(api_key=api_key)
        indexes = pc.list_indexes()
        
        print("Available Pinecone indexes:")
        for idx in indexes:
            print(f"  - {idx.name}")
            
        return True
        
    except Exception as e:
        print(f"Error listing Pinecone indexes: {e}")
        return False


def test_pinecone_connection():
    """Test basic Pinecone connectivity."""
    try:
        from pinecone import Pinecone
        
        api_key = os.getenv("PINECONE_API_KEY")
        if not api_key:
            print("Error: PINECONE_API_KEY not found in environment variables")
            return False
        
        print("Testing Pinecone connection...")
        pc = Pinecone(api_key=api_key)
        
        # List indexes to test connection
        indexes = pc.list_indexes()
        print(f"Connection successful! Found {len(indexes)} indexes.")
        
        return True
        
    except Exception as e:
        print(f"Error connecting to Pinecone: {e}")
        return False


if __name__ == "__main__":
    print("=== Pinecone Index Management ===")
    
    # Test connection first
    if not test_pinecone_connection():
        sys.exit(1)
    
    # List existing indexes
    print("\n=== Current Indexes ===")
    list_pinecone_indexes()
    
    # Create index
    print("\n=== Creating Index ===")
    if create_pinecone_index():
        print("✅ Pinecone index setup completed successfully!")
        
        # List indexes again to confirm
        print("\n=== Updated Index List ===")
        list_pinecone_indexes()
        
        sys.exit(0)
    else:
        print("❌ Failed to create Pinecone index")
        sys.exit(1)

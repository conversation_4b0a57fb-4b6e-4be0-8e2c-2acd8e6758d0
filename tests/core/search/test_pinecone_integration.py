"""
Integration tests for Pinecone search provider.

This module contains integration tests that validate the Pinecone provider
with real API calls when credentials are available, or skip gracefully.
"""

import os
import pytest
import asyncio
from unittest.mock import patch, Mock, AsyncMock
from typing import List, Dict, Any

from konveyor.core.search.factory import get_search_service, SearchProviderFactory
from konveyor.core.search.interfaces import (
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)


class TestPineconeIntegration:
    """Integration tests for Pinecone search provider."""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Set up test environment."""
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.has_real_pinecone = bool(self.pinecone_api_key and self.pinecone_api_key != "test-pinecone-key")
        
        # Test configuration
        self.test_config = {
            "pinecone_api_key": self.pinecone_api_key or "test-pinecone-key",
            "pinecone_index_name": "konveyor-test",
            "embedding_dimension": 1536,
            "azure_openai_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT", "https://test.openai.azure.com/"),
            "azure_openai_api_key": os.getenv("AZURE_OPENAI_API_KEY", "test-key"),
            "azure_openai_api_version": "2024-05-13",
            "azure_openai_embedding_deployment": "embeddings"
        }
    
    @pytest.mark.skipif(
        not os.getenv("PINECONE_API_KEY") or os.getenv("PINECONE_API_KEY") == "test-pinecone-key",
        reason="Real Pinecone API key not configured"
    )
    @pytest.mark.asyncio
    async def test_real_pinecone_provider_creation(self):
        """Test creating Pinecone provider with real credentials."""
        # This test only runs with real Pinecone credentials
        search_service = get_search_service(
            vector_provider="pinecone",
            embedding_provider="azure_openai",
            config=self.test_config
        )
        
        assert search_service is not None
        assert hasattr(search_service, 'vector_search_provider')
        
        # Test provider info
        provider_info = search_service.get_provider_info()
        assert "PineconeSearchProvider" in provider_info["vector_search"]
    
    @pytest.mark.skipif(
        not os.getenv("PINECONE_API_KEY") or os.getenv("PINECONE_API_KEY") == "test-pinecone-key",
        reason="Real Pinecone API key not configured"
    )
    @pytest.mark.asyncio
    async def test_real_pinecone_index_operations(self):
        """Test real Pinecone index operations."""
        search_service = get_search_service(
            vector_provider="pinecone",
            embedding_provider="azure_openai",
            config=self.test_config
        )
        
        # Test index listing
        indexes = await search_service.vector_search_provider.list_indexes()
        assert isinstance(indexes, list)
        
        # Test index stats (if index exists)
        if indexes:
            stats = await search_service.vector_search_provider.get_index_stats(indexes[0])
            assert isinstance(stats, dict)
    
    @pytest.mark.asyncio
    async def test_pinecone_provider_with_mock(self):
        """Test Pinecone provider with mocked API calls."""
        # Mock the Pinecone client to avoid real API calls
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone') as mock_pinecone_class:
                # Set up mock
                mock_client = Mock()
                mock_index = Mock()
                mock_pinecone_class.return_value = mock_client
                mock_client.Index.return_value = mock_index
                mock_client.list_indexes.return_value = [Mock(name="test-index")]
                
                # Mock search response
                mock_match = Mock()
                mock_match.id = "test-doc-1"
                mock_match.score = 0.95
                mock_match.metadata = {"content": "Test content", "document_id": "doc-1"}
                
                mock_response = Mock()
                mock_response.matches = [mock_match]
                mock_index.query.return_value = mock_response
                
                # Create search service
                search_service = get_search_service(
                    vector_provider="pinecone",
                    embedding_provider="azure_openai",
                    config=self.test_config
                )
                
                # Test search
                query = SearchQuery(
                    text="test query",
                    search_type=SearchType.VECTOR,
                    top_k=5,
                    embedding=[0.1] * 1536
                )
                
                results = await search_service.vector_search_provider.search(query)
                
                assert len(results) == 1
                assert results[0].id == "test-doc-1"
                assert results[0].score == 0.95
                assert "Test content" in results[0].content
    
    @pytest.mark.asyncio
    async def test_unified_search_service_with_pinecone(self):
        """Test unified search service with Pinecone provider."""
        # Mock both Pinecone and Azure OpenAI
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone') as mock_pinecone:
                with patch('konveyor.core.search.embeddings.azure_openai.AZURE_OPENAI_AVAILABLE', True):
                    with patch('konveyor.core.search.embeddings.azure_openai.AzureOpenAI') as mock_azure_openai:
                        # Set up Pinecone mock
                        mock_pinecone_client = Mock()
                        mock_index = Mock()
                        mock_pinecone.return_value = mock_pinecone_client
                        mock_pinecone_client.Index.return_value = mock_index
                        mock_pinecone_client.list_indexes.return_value = [Mock(name="test-index")]
                        
                        # Mock search response
                        mock_match = Mock()
                        mock_match.id = "test-doc-1"
                        mock_match.score = 0.95
                        mock_match.metadata = {"content": "Test content about machine learning"}
                        
                        mock_response = Mock()
                        mock_response.matches = [mock_match]
                        mock_index.query.return_value = mock_response
                        
                        # Set up Azure OpenAI mock
                        mock_openai_client = Mock()
                        mock_azure_openai.return_value = mock_openai_client
                        
                        mock_embedding_response = Mock()
                        mock_embedding_data = Mock()
                        mock_embedding_data.embedding = [0.1] * 1536
                        mock_embedding_response.data = [mock_embedding_data]
                        mock_openai_client.embeddings.create.return_value = mock_embedding_response
                        
                        # Create unified search service
                        search_service = get_search_service(
                            vector_provider="pinecone",
                            embedding_provider="azure_openai",
                            config=self.test_config
                        )
                        
                        # Test semantic search (end-to-end)
                        results = await search_service.semantic_search("machine learning", top_k=3)
                        
                        assert len(results) == 1
                        assert results[0].id == "test-doc-1"
                        assert "machine learning" in results[0].content
                        
                        # Verify embedding was generated
                        mock_openai_client.embeddings.create.assert_called_once()
                        
                        # Verify Pinecone search was called
                        mock_index.query.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_document_indexing_with_pinecone(self):
        """Test document indexing with Pinecone provider."""
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone') as mock_pinecone:
                with patch('konveyor.core.search.embeddings.azure_openai.AZURE_OPENAI_AVAILABLE', True):
                    with patch('konveyor.core.search.embeddings.azure_openai.AzureOpenAI') as mock_azure_openai:
                        # Set up mocks
                        mock_pinecone_client = Mock()
                        mock_index = Mock()
                        mock_pinecone.return_value = mock_pinecone_client
                        mock_pinecone_client.Index.return_value = mock_index
                        mock_pinecone_client.list_indexes.return_value = [Mock(name="test-index")]
                        mock_index.upsert.return_value = True
                        
                        mock_openai_client = Mock()
                        mock_azure_openai.return_value = mock_openai_client
                        
                        mock_embedding_response = Mock()
                        mock_embedding_data = Mock()
                        mock_embedding_data.embedding = [0.1] * 1536
                        mock_embedding_response.data = [mock_embedding_data]
                        mock_openai_client.embeddings.create.return_value = mock_embedding_response
                        
                        # Create search service
                        search_service = get_search_service(
                            vector_provider="pinecone",
                            embedding_provider="azure_openai",
                            config=self.test_config
                        )
                        
                        # Test document indexing
                        success = await search_service.index_document_chunk(
                            chunk_id="test-chunk-1",
                            document_id="test-doc-1",
                            content="This is a test document about machine learning algorithms.",
                            chunk_index=0,
                            metadata={"category": "documentation", "topic": "ml"}
                        )
                        
                        assert success is True
                        
                        # Verify embedding was generated
                        mock_openai_client.embeddings.create.assert_called_once()
                        
                        # Verify document was upserted to Pinecone
                        mock_index.upsert.assert_called_once()
                        
                        # Check the upsert call arguments
                        upsert_call = mock_index.upsert.call_args
                        vectors = upsert_call[1]['vectors']
                        assert len(vectors) == 1
                        assert vectors[0]['id'] == "test-chunk-1"
                        assert len(vectors[0]['values']) == 1536
                        assert vectors[0]['metadata']['content'] == "This is a test document about machine learning algorithms."
                        assert vectors[0]['metadata']['category'] == "documentation"
    
    @pytest.mark.asyncio
    async def test_search_types_with_pinecone(self):
        """Test different search types with Pinecone provider."""
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone') as mock_pinecone:
                # Set up mock
                mock_pinecone_client = Mock()
                mock_index = Mock()
                mock_pinecone.return_value = mock_pinecone_client
                mock_pinecone_client.Index.return_value = mock_index
                mock_pinecone_client.list_indexes.return_value = [Mock(name="test-index")]
                
                # Mock search response
                mock_match = Mock()
                mock_match.id = "test-doc-1"
                mock_match.score = 0.95
                mock_match.metadata = {"content": "Test content"}
                
                mock_response = Mock()
                mock_response.matches = [mock_match]
                mock_index.query.return_value = mock_response
                
                # Create provider
                from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
                provider = PineconeSearchProvider(self.test_config)
                
                # Test vector search
                vector_query = SearchQuery(
                    text="test query",
                    search_type=SearchType.VECTOR,
                    top_k=5,
                    embedding=[0.1] * 1536
                )
                results = await provider.search(vector_query)
                assert len(results) == 1
                
                # Test hybrid search
                hybrid_query = SearchQuery(
                    text="test query",
                    search_type=SearchType.HYBRID,
                    top_k=5,
                    embedding=[0.1] * 1536
                )
                results = await provider.search(hybrid_query)
                assert len(results) == 1
                
                # Verify query was called with appropriate parameters
                assert mock_index.query.call_count == 2
    
    @pytest.mark.asyncio
    async def test_error_handling_with_pinecone(self):
        """Test error handling with Pinecone provider."""
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone') as mock_pinecone:
                with patch('konveyor.core.search.providers.pinecone_provider.PineconeException', Exception):
                    # Set up mock to raise exception
                    mock_pinecone_client = Mock()
                    mock_index = Mock()
                    mock_pinecone.return_value = mock_pinecone_client
                    mock_pinecone_client.Index.return_value = mock_index
                    mock_pinecone_client.list_indexes.return_value = [Mock(name="test-index")]
                    mock_index.query.side_effect = Exception("Pinecone API error")
                    
                    # Create provider
                    from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
                    provider = PineconeSearchProvider(self.test_config)
                    
                    # Test that exceptions are properly raised
                    query = SearchQuery(
                        text="test query",
                        search_type=SearchType.VECTOR,
                        top_k=5,
                        embedding=[0.1] * 1536
                    )
                    
                    with pytest.raises(Exception, match="Pinecone API error"):
                        await provider.search(query)
    
    def test_provider_factory_pinecone_registration(self):
        """Test that Pinecone provider is properly registered in factory."""
        available_providers = SearchProviderFactory.get_available_providers()
        
        # Pinecone should be available (even if mocked)
        assert "pinecone" in available_providers["vector_search"]
        
        # Test provider creation
        try:
            provider = SearchProviderFactory.create_vector_search("pinecone", self.test_config)
            # Should not raise an exception even with mock credentials
            assert provider is not None
        except ImportError:
            # This is expected if Pinecone SDK is not installed
            pytest.skip("Pinecone SDK not available")
    
    @pytest.mark.asyncio
    async def test_backward_compatibility_with_pinecone(self):
        """Test that backward compatibility wrapper works with Pinecone."""
        with patch('konveyor.core.search.providers.pinecone_provider.PINECONE_AVAILABLE', True):
            with patch('konveyor.core.search.providers.pinecone_provider.Pinecone'):
                with patch('konveyor.core.search.embeddings.azure_openai.AZURE_OPENAI_AVAILABLE', True):
                    with patch('konveyor.core.search.embeddings.azure_openai.AzureOpenAI'):
                        # Create search service
                        search_service = get_search_service(
                            vector_provider="pinecone",
                            embedding_provider="azure_openai",
                            config=self.test_config
                        )
                        
                        # Test backward compatibility wrapper
                        from konveyor.core.search.unified_search_service import BackwardCompatibilityWrapper
                        wrapper = BackwardCompatibilityWrapper(search_service)
                        
                        # Test that wrapper has expected attributes
                        assert hasattr(wrapper, 'client')
                        assert hasattr(wrapper, 'openai_client')
                        assert hasattr(wrapper, 'index_name')
                        
                        # Test that wrapper has expected methods
                        assert hasattr(wrapper, 'search')
                        assert hasattr(wrapper, 'upload_documents')
                        assert hasattr(wrapper, 'index_document_chunk')
                        assert hasattr(wrapper, 'create_search_index')
                        assert hasattr(wrapper, 'generate_embedding')

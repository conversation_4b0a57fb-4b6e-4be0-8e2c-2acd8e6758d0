"""
Mock tests for search abstraction layer.

This module contains comprehensive mock tests for the search provider abstraction layer,
testing interfaces, factory patterns, and backward compatibility without external API calls.
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any

from konveyor.core.search.interfaces import (
    VectorSearchInterface,
    EmbeddingInterface,
    SearchServiceInterface,
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)
from konveyor.core.search.factory import SearchProviderFactory, get_search_service
from konveyor.core.search.unified_search_service import UnifiedSearchService, BackwardCompatibilityWrapper


class MockVectorSearchProvider(VectorSearchInterface):
    """Mock vector search provider for testing."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.documents = {}  # In-memory storage for testing

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        # Mock search results
        return [
            SearchResult(
                id="mock-result-1",
                content="Mock search result content",
                score=0.95,
                metadata={"source": "mock"},
                document_id="mock-doc-1",
                chunk_id="mock-chunk-1",
                chunk_index=0
            )
        ]

    async def add_documents(self, documents: List[DocumentChunk]) -> List[str]:
        successful_ids = []
        for doc in documents:
            self.documents[doc.id] = doc
            successful_ids.append(doc.id)
        return successful_ids

    async def update_document(self, document: DocumentChunk) -> bool:
        self.documents[document.id] = document
        return True

    async def delete_document(self, document_id: str) -> bool:
        if document_id in self.documents:
            del self.documents[document_id]
            return True
        return False

    async def create_index(self, index_name: str, schema: Dict[str, Any] = None) -> bool:
        return True

    async def delete_index(self, index_name: str) -> bool:
        return True

    async def list_indexes(self) -> List[str]:
        return ["mock-index"]

    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        return {"document_count": len(self.documents), "status": "active"}


class MockEmbeddingProvider(EmbeddingInterface):
    """Mock embedding provider for testing."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    async def generate_embedding(self, text: str) -> List[float]:
        # Return mock embedding vector
        return [0.1, 0.2, 0.3] * 512  # 1536 dimensions

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        return [await self.generate_embedding(text) for text in texts]

    @property
    def embedding_dimension(self) -> int:
        return 1536


class TestSearchAbstractionLayer:
    """Test suite for search abstraction layer mock functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        # Register mock providers
        SearchProviderFactory.register_vector_provider("mock_vector", MockVectorSearchProvider)
        SearchProviderFactory.register_embedding_provider("mock_embedding", MockEmbeddingProvider)

        self.mock_config = {
            "mock_api_key": "test-key",
            "mock_index_name": "test-index"
        }

    def test_provider_factory_registration(self):
        """Test provider registration in factory."""
        # Test vector provider registration
        providers = SearchProviderFactory.get_available_providers()
        assert "mock_vector" in providers["vector_search"]
        assert "mock_embedding" in providers["embedding"]

    def test_vector_provider_creation(self):
        """Test vector search provider creation."""
        provider = SearchProviderFactory.create_vector_search("mock_vector", self.mock_config)
        assert isinstance(provider, MockVectorSearchProvider)
        assert provider.config == self.mock_config

    def test_embedding_provider_creation(self):
        """Test embedding provider creation."""
        provider = SearchProviderFactory.create_embedding_service("mock_embedding", self.mock_config)
        assert isinstance(provider, MockEmbeddingProvider)
        assert provider.config == self.mock_config

    def test_unified_search_service_creation(self):
        """Test unified search service creation."""
        service = SearchProviderFactory.create_search_service(
            vector_provider="mock_vector",
            embedding_provider="mock_embedding",
            config=self.mock_config
        )
        assert isinstance(service, UnifiedSearchService)
        assert isinstance(service.vector_search_provider, MockVectorSearchProvider)
        assert isinstance(service.embedding_service, MockEmbeddingProvider)

    @pytest.mark.asyncio
    async def test_search_interface_compliance(self):
        """Test that mock providers comply with search interfaces."""
        vector_provider = MockVectorSearchProvider(self.mock_config)
        embedding_provider = MockEmbeddingProvider(self.mock_config)

        # Test VectorSearchInterface compliance
        assert isinstance(vector_provider, VectorSearchInterface)

        # Test search method
        query = SearchQuery(
            text="test query",
            search_type=SearchType.VECTOR,
            top_k=5,
            embedding=[0.1, 0.2, 0.3] * 512
        )
        results = await vector_provider.search(query)
        assert len(results) == 1
        assert isinstance(results[0], SearchResult)
        assert results[0].id == "mock-result-1"

        # Test document operations
        doc = DocumentChunk(
            id="test-doc-1",
            content="Test content",
            embedding=[0.1, 0.2, 0.3] * 512,
            metadata={"test": True}
        )

        # Test add documents
        added_ids = await vector_provider.add_documents([doc])
        assert added_ids == ["test-doc-1"]

        # Test update document
        success = await vector_provider.update_document(doc)
        assert success is True

        # Test delete document
        success = await vector_provider.delete_document("test-doc-1")
        assert success is True

        # Test EmbeddingInterface compliance
        assert isinstance(embedding_provider, EmbeddingInterface)

        # Test embedding generation
        embedding = await embedding_provider.generate_embedding("test text")
        assert len(embedding) == 1536
        assert all(isinstance(x, float) for x in embedding)

        # Test batch embedding generation
        embeddings = await embedding_provider.generate_embeddings(["text1", "text2"])
        assert len(embeddings) == 2
        assert all(len(emb) == 1536 for emb in embeddings)

    @pytest.mark.asyncio
    async def test_unified_search_service_interface(self):
        """Test unified search service interface compliance."""
        service = UnifiedSearchService(
            vector_search=MockVectorSearchProvider(self.mock_config),
            embedding_service=MockEmbeddingProvider(self.mock_config),
            config=self.mock_config
        )

        # Test SearchServiceInterface compliance
        assert isinstance(service, SearchServiceInterface)

        # Test semantic search
        results = await service.semantic_search("test query", top_k=3)
        assert len(results) == 1
        assert isinstance(results[0], SearchResult)

        # Test hybrid search
        results = await service.hybrid_search("test query", top_k=3)
        assert len(results) == 1

        # Test vector search
        results = await service.vector_search("test query", top_k=3)
        assert len(results) == 1

        # Test document indexing
        success = await service.index_document_chunk(
            chunk_id="test-chunk-1",
            document_id="test-doc-1",
            content="Test content",
            chunk_index=0,
            metadata={"test": True}
        )
        assert success is True

        # Test index creation
        success = await service.create_search_index("test-index")
        assert success is True

        # Test embedding generation
        embedding = await service.generate_embedding("test text")
        assert len(embedding) == 1536
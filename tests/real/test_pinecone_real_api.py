"""
Real Pinecone API integration tests.

This module tests the Pinecone provider with actual API calls to validate
the abstraction layer works correctly with live Pinecone services.
"""

import os
import pytest
import asyncio
import time
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from konveyor.core.search.factory import get_search_service
from konveyor.core.search.interfaces import (
    SearchQuery,
    SearchResult,
    DocumentChunk,
    SearchType
)


class TestPineconeRealAPI:
    """Real Pinecone API integration tests."""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Set up test environment."""
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.pinecone_index_name = os.getenv("PINECONE_INDEX_NAME", "konveyor-documents")
        
        # Skip tests if no real Pinecone credentials
        if not self.pinecone_api_key or self.pinecone_api_key == "test-pinecone-key":
            pytest.skip("Real Pinecone API key not configured")
        
        # Test configuration with mock embedding service (since Azure OpenAI is failing)
        self.test_config = {
            "pinecone_api_key": self.pinecone_api_key,
            "pinecone_index_name": self.pinecone_index_name,
            "embedding_dimension": 1536,
            # Use mock embedding service to avoid Azure OpenAI issues
            "azure_openai_endpoint": "https://test.openai.azure.com/",
            "azure_openai_api_key": "test-key",
            "azure_openai_api_version": "2024-05-13",
            "azure_openai_embedding_deployment": "embeddings"
        }
    
    @pytest.mark.asyncio
    async def test_real_pinecone_connection(self):
        """Test real Pinecone connection and index operations."""
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        
        # Create Pinecone provider
        provider = PineconeSearchProvider(self.test_config)
        
        # Test index listing
        indexes = await provider.list_indexes()
        assert isinstance(indexes, list)
        assert self.pinecone_index_name in indexes
        
        # Test index stats
        stats = await provider.get_index_stats(self.pinecone_index_name)
        assert isinstance(stats, dict)
        assert "dimension" in stats
        assert stats["dimension"] == 1536
    
    @pytest.mark.asyncio
    async def test_real_pinecone_document_operations(self):
        """Test real document indexing and retrieval with Pinecone."""
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        
        # Create Pinecone provider
        provider = PineconeSearchProvider(self.test_config)
        
        # Create test document with embedding
        test_embedding = [0.1] * 1536  # Mock embedding
        test_doc = DocumentChunk(
            id=f"test-doc-{int(time.time())}",
            content="This is a test document for Pinecone integration testing.",
            embedding=test_embedding,
            metadata={
                "test": True,
                "category": "integration_test",
                "timestamp": int(time.time())
            },
            document_id="test-document",
            chunk_index=0
        )
        
        # Test document addition
        result_ids = await provider.add_documents([test_doc])
        assert len(result_ids) == 1
        assert result_ids[0] == test_doc.id
        
        # Wait longer for indexing (Pinecone can take time to index)
        print(f"Waiting for document {test_doc.id} to be indexed...")
        await asyncio.sleep(5)

        # Check index stats to see if document count increased
        stats = await provider.get_index_stats(self.pinecone_index_name)
        print(f"Index stats after adding document: {stats}")

        # Test vector search with exact same embedding
        search_query = SearchQuery(
            text="test document",
            search_type=SearchType.VECTOR,
            top_k=10,  # Increase top_k to get more results
            embedding=test_embedding
        )

        results = await provider.search(search_query)
        print(f"Search results: {len(results)} found")
        for i, result in enumerate(results):
            print(f"  Result {i}: ID={result.id}, Score={result.score}, Content={result.content[:50]}...")

        # If no results with exact embedding, try a broader search
        if len(results) == 0:
            print("No results with exact embedding, trying broader search...")
            # Try with slightly different embedding
            broader_embedding = [0.1 + (i % 3) * 0.01 for i in range(1536)]
            broader_query = SearchQuery(
                text="test document",
                search_type=SearchType.VECTOR,
                top_k=10,
                embedding=broader_embedding
            )
            results = await provider.search(broader_query)
            print(f"Broader search results: {len(results)} found")

        # The test should pass if we find at least one result
        assert len(results) >= 1, f"No search results found for document {test_doc.id}"
        
        # Find our test document in results
        found_doc = None
        for result in results:
            if result.id == test_doc.id:
                found_doc = result
                break
        
        assert found_doc is not None, f"Test document {test_doc.id} not found in search results"
        assert "test document" in found_doc.content
        assert found_doc.metadata["test"] is True
        
        # Test document update
        updated_doc = DocumentChunk(
            id=test_doc.id,
            content="This is an updated test document for Pinecone integration testing.",
            embedding=test_embedding,
            metadata={
                "test": True,
                "category": "integration_test_updated",
                "timestamp": int(time.time())
            },
            document_id="test-document",
            chunk_index=0
        )
        
        success = await provider.update_document(updated_doc)
        assert success is True
        
        # Wait for update to propagate
        await asyncio.sleep(2)
        
        # Verify update
        results = await provider.search(search_query)
        found_updated = None
        for result in results:
            if result.id == test_doc.id:
                found_updated = result
                break
        
        assert found_updated is not None
        assert "updated test document" in found_updated.content
        assert found_updated.metadata["category"] == "integration_test_updated"
        
        # Test document deletion
        success = await provider.delete_document(test_doc.id)
        assert success is True
        
        # Wait for deletion to propagate
        await asyncio.sleep(2)
        
        # Verify deletion (document should not be found)
        results = await provider.search(search_query)
        found_deleted = None
        for result in results:
            if result.id == test_doc.id:
                found_deleted = result
                break
        
        # Document should be deleted (not found in results)
        assert found_deleted is None, f"Deleted document {test_doc.id} still found in search results"
    
    @pytest.mark.asyncio
    async def test_real_pinecone_search_types(self):
        """Test different search types with real Pinecone API."""
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        
        # Create Pinecone provider
        provider = PineconeSearchProvider(self.test_config)
        
        # Create test documents
        test_embedding = [0.2] * 1536
        test_docs = [
            DocumentChunk(
                id=f"search-test-{i}-{int(time.time())}",
                content=f"Test document {i} about machine learning and AI.",
                embedding=test_embedding,
                metadata={"test_batch": "search_types", "doc_number": i}
            )
            for i in range(3)
        ]
        
        # Add documents
        result_ids = await provider.add_documents(test_docs)
        assert len(result_ids) == 3
        
        # Wait for indexing
        await asyncio.sleep(3)
        
        # Test vector search
        vector_query = SearchQuery(
            text="machine learning",
            search_type=SearchType.VECTOR,
            top_k=5,
            embedding=test_embedding
        )
        
        vector_results = await provider.search(vector_query)
        assert len(vector_results) >= 3
        
        # Test hybrid search
        hybrid_query = SearchQuery(
            text="machine learning",
            search_type=SearchType.HYBRID,
            top_k=5,
            embedding=test_embedding
        )
        
        hybrid_results = await provider.search(hybrid_query)
        assert len(hybrid_results) >= 3
        
        # Test semantic search
        semantic_query = SearchQuery(
            text="artificial intelligence",
            search_type=SearchType.SEMANTIC,
            top_k=5,
            embedding=test_embedding
        )
        
        semantic_results = await provider.search(semantic_query)
        assert len(semantic_results) >= 0  # May not find exact matches
        
        # Cleanup test documents
        for doc in test_docs:
            await provider.delete_document(doc.id)
    
    @pytest.mark.asyncio
    async def test_real_pinecone_with_mock_embeddings(self):
        """Test Pinecone with mock embedding service to avoid Azure OpenAI issues."""
        from tests.core.search.test_search_abstraction_mock import MockEmbeddingProvider
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        from konveyor.core.search.unified_search_service import UnifiedSearchService
        
        # Create providers
        vector_provider = PineconeSearchProvider(self.test_config)
        embedding_provider = MockEmbeddingProvider(self.test_config)
        
        # Create unified service
        search_service = UnifiedSearchService(
            vector_search=vector_provider,
            embedding_service=embedding_provider,
            config=self.test_config
        )
        
        # Test end-to-end functionality
        success = await search_service.index_document_chunk(
            chunk_id=f"e2e-test-{int(time.time())}",
            document_id="e2e-test-doc",
            content="End-to-end test document for Pinecone with mock embeddings.",
            chunk_index=0,
            metadata={"test_type": "e2e", "provider": "pinecone_mock"}
        )
        assert success is True
        
        # Wait for indexing
        await asyncio.sleep(2)
        
        # Test semantic search (uses embedding generation + vector search)
        results = await search_service.semantic_search("end-to-end test", top_k=3)
        assert len(results) >= 1
        
        # Find our test document
        found = False
        for result in results:
            if "End-to-end test document" in result.content:
                found = True
                assert result.metadata["test_type"] == "e2e"
                break
        
        assert found, "End-to-end test document not found in search results"
        
        # Test health check
        health = await search_service.health_check()
        assert health["vector_search"] is True
        assert health["embedding_service"] is True  # Mock should always be healthy
    
    @pytest.mark.asyncio
    async def test_real_pinecone_performance(self):
        """Test Pinecone performance with batch operations."""
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        
        # Create Pinecone provider
        provider = PineconeSearchProvider(self.test_config)
        
        # Create batch of test documents
        batch_size = 10
        test_embedding = [0.3] * 1536
        batch_docs = [
            DocumentChunk(
                id=f"perf-test-{i}-{int(time.time())}",
                content=f"Performance test document {i} with various content about technology.",
                embedding=test_embedding,
                metadata={"batch": "performance", "doc_id": i}
            )
            for i in range(batch_size)
        ]
        
        # Measure batch indexing time
        start_time = time.time()
        result_ids = await provider.add_documents(batch_docs)
        indexing_time = time.time() - start_time
        
        assert len(result_ids) == batch_size
        print(f"Batch indexing time for {batch_size} documents: {indexing_time:.2f}s")
        
        # Wait for indexing to complete
        await asyncio.sleep(3)
        
        # Measure search time
        search_query = SearchQuery(
            text="performance test",
            search_type=SearchType.VECTOR,
            top_k=batch_size,
            embedding=test_embedding
        )
        
        start_time = time.time()
        results = await provider.search(search_query)
        search_time = time.time() - start_time
        
        assert len(results) >= batch_size
        print(f"Search time for {batch_size} documents: {search_time:.2f}s")
        
        # Cleanup
        for doc in batch_docs:
            await provider.delete_document(doc.id)
    
    def test_pinecone_provider_configuration(self):
        """Test Pinecone provider configuration validation."""
        from konveyor.core.search.providers.pinecone_provider import PineconeSearchProvider
        
        # Test with valid configuration
        provider = PineconeSearchProvider(self.test_config)
        assert provider.api_key == self.pinecone_api_key
        assert provider.index_name == self.pinecone_index_name
        
        # Test configuration validation
        invalid_config = {
            "pinecone_api_key": "",
            "pinecone_index_name": ""
        }
        
        with pytest.raises(ValueError, match="Pinecone API key is required"):
            PineconeSearchProvider(invalid_config)

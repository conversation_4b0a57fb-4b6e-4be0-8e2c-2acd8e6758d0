"""
Real integration test for search abstraction layer initialization.

This test validates that the search abstraction layer can be properly initialized
with the configured providers (Pinecone + Azure OpenAI).
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(levelname)s %(asctime)s %(name)s %(funcName)s %(message)s",
    handlers=[logging.StreamHandler(sys.stderr)],
)
logger = logging.getLogger(__name__)


def setup_django():
    """Set up Django environment for testing."""
    logger.info("Setting up Django environment...")
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
    
    try:
        import django
        django.setup()
        logger.info("Django setup completed successfully.")
    except Exception as e:
        logger.error(f"Failed to set up Django: {e}", exc_info=True)
        sys.exit(1)


def check_environment_variables():
    """Check for required environment variables based on configured providers."""
    logger.info("Checking for required environment variables...")
    
    # Load environment variables
    env_path = os.path.join(PROJECT_ROOT, ".env")
    if os.path.exists(env_path):
        logger.info(f"Loading environment variables from: {env_path}")
        load_dotenv(dotenv_path=env_path, override=False)
    
    # Get configured providers
    search_provider = os.getenv("SEARCH_PROVIDER", "pinecone")
    embedding_provider = os.getenv("EMBEDDING_PROVIDER", "azure_openai")
    
    logger.info(f"Configured search provider: {search_provider}")
    logger.info(f"Configured embedding provider: {embedding_provider}")
    
    # Check provider-specific requirements
    required_vars = []
    
    if search_provider == "pinecone":
        required_vars.extend([
            "PINECONE_API_KEY",
            "PINECONE_INDEX_NAME"
        ])
    elif search_provider == "azure":
        required_vars.extend([
            "AZURE_SEARCH_ENDPOINT",
            "AZURE_SEARCH_API_KEY"
        ])
    
    if embedding_provider == "azure_openai":
        required_vars.extend([
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_EMBEDDING_DEPLOYMENT"
        ])
    elif embedding_provider == "openai":
        required_vars.extend([
            "OPENAI_API_KEY"
        ])
    
    # Check for missing variables
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value in ["test-pinecone-key", "test-key", "test-app-id"]:
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(
            f"Missing or test-only environment variables: {', '.join(missing_vars)}"
        )
        logger.warning(
            "This test will use mock providers instead of real API calls."
        )
        return False
    else:
        logger.info("All required environment variables are present for real API testing.")
        return True


def test_search_abstraction_init():
    """Test search abstraction layer initialization."""
    logger.info("--- Starting Search Abstraction Layer Initialization Test ---")
    
    # 1. Set up Django
    setup_django()
    
    # 2. Check environment variables
    has_real_credentials = check_environment_variables()
    
    # 3. Import search abstraction layer
    logger.info("Importing search abstraction layer...")
    try:
        from konveyor.core.search import get_search_service
        from konveyor.core.search.factory import SearchProviderFactory
        
        logger.info("Search abstraction layer imported successfully.")
    except ImportError as e:
        logger.error(f"Failed to import search abstraction layer: {e}", exc_info=True)
        assert False, f"Failed to import search abstraction layer: {e}"
    except Exception as e:
        logger.error(f"Unexpected error during import: {e}", exc_info=True)
        assert False, f"Unexpected error during import: {e}"
    
    # 4. Test provider factory
    logger.info("Testing provider factory...")
    try:
        available_providers = SearchProviderFactory.get_available_providers()
        logger.info(f"Available vector search providers: {available_providers['vector_search']}")
        logger.info(f"Available embedding providers: {available_providers['embedding']}")
        
        # Check that our configured providers are available
        search_provider = os.getenv("SEARCH_PROVIDER", "pinecone")
        embedding_provider = os.getenv("EMBEDDING_PROVIDER", "azure_openai")
        
        assert search_provider in available_providers["vector_search"], f"Configured search provider '{search_provider}' not available"
        assert embedding_provider in available_providers["embedding"], f"Configured embedding provider '{embedding_provider}' not available"

        logger.info("Provider factory validation passed.")
    except Exception as e:
        logger.error(f"Provider factory test failed: {e}", exc_info=True)
        assert False, f"Provider factory test failed: {e}"
    
    # 5. Test search service creation
    logger.info("Testing search service creation...")
    try:
        if has_real_credentials:
            # Use real providers
            search_service = get_search_service()
        else:
            # Use mock configuration for testing
            mock_config = {
                "pinecone_api_key": "test-pinecone-key",
                "pinecone_index_name": "test-index",
                "azure_openai_endpoint": "https://test.openai.azure.com/",
                "azure_openai_api_key": "test-key",
                "azure_openai_api_version": "2024-05-13",
                "azure_openai_embedding_deployment": "embeddings"
            }

            # Import mock providers for testing
            from tests.core.search.test_search_abstraction_mock import MockVectorSearchProvider, MockEmbeddingProvider
            from konveyor.core.search.unified_search_service import UnifiedSearchService

            # Create mock search service
            vector_provider = MockVectorSearchProvider(mock_config)
            embedding_provider = MockEmbeddingProvider(mock_config)
            search_service = UnifiedSearchService(vector_provider, embedding_provider, mock_config)

        logger.info("Search service created successfully.")

        # Test provider info
        provider_info = search_service.get_provider_info()
        logger.info(f"Active providers: {provider_info}")

    except Exception as e:
        logger.error(f"Search service creation failed: {e}", exc_info=True)
        assert False, f"Search service creation failed: {e}"
    
    # 6. Test basic functionality (if real credentials available)
    if has_real_credentials:
        logger.info("Testing basic functionality with real credentials...")
        try:
            import asyncio

            # Test health check
            health = asyncio.run(search_service.health_check())
            logger.info(f"Health check results: {health}")

            assert all(health.values()), f"Some providers failed health check: {health}"

            # Test embedding generation
            test_embedding = asyncio.run(search_service.generate_embedding("test query"))
            assert len(test_embedding) == 1536, f"Expected 1536-dimension embedding, got {len(test_embedding)}"

            logger.info("Basic functionality tests passed with real credentials.")

        except Exception as e:
            logger.error(f"Real functionality test failed: {e}", exc_info=True)
            assert False, f"Real functionality test failed: {e}"
    else:
        logger.info("Skipping real functionality tests (using test credentials)")
    
    # 7. Test backward compatibility
    logger.info("Testing backward compatibility...")
    try:
        from konveyor.core.search.unified_search_service import BackwardCompatibilityWrapper
        
        wrapper = BackwardCompatibilityWrapper(search_service)
        
        # Check that wrapper has expected attributes
        assert hasattr(wrapper, 'client'), "Wrapper missing 'client' attribute"
        assert hasattr(wrapper, 'openai_client'), "Wrapper missing 'openai_client' attribute"
        assert hasattr(wrapper, 'index_name'), "Wrapper missing 'index_name' attribute"
        
        # Check that wrapper has expected methods
        assert hasattr(wrapper, 'search'), "Wrapper missing 'search' method"
        assert hasattr(wrapper, 'upload_documents'), "Wrapper missing 'upload_documents' method"
        assert hasattr(wrapper, 'index_document_chunk'), "Wrapper missing 'index_document_chunk' method"
        
        logger.info("Backward compatibility validation passed.")
        
    except Exception as e:
        logger.error(f"Backward compatibility test failed: {e}", exc_info=True)
        assert False, f"Backward compatibility test failed: {e}"
    
    logger.info("--- Search Abstraction Layer Initialization Test Completed Successfully! ---")
    assert True  # Test passed


if __name__ == "__main__":
    try:
        test_search_abstraction_init()
        print("Test completed successfully!")
        sys.exit(0)
    except AssertionError as e:
        print(f"Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Test error: {e}")
        sys.exit(1)

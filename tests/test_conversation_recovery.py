"""
Test conversation recovery scenarios.

This test suite specifically tests the conversation recovery logic
implemented in the bot views to handle "Conversation not found" errors.
"""

import os
import sys
from unittest.mock import AsyncMock, patch

import django
from django.test import TestCase

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
django.setup()

from konveyor.apps.bot.views import process_message


class ConversationRecoveryTests(TestCase):
    """Tests for conversation recovery scenarios."""

    def test_conversation_recovery_on_add_message_failure(self):
        """Test that the system recovers when add_message fails due to missing conversation."""

        # Mock conversation manager that fails on first add_message but succeeds on recreation
        mock_conversation_manager = AsyncMock()

        # First call to add_message fails with "Conversation not found"
        # Second call (after recreation) succeeds for user message
        # Third call succeeds for assistant message
        mock_conversation_manager.add_message.side_effect = [
            ValueError("Conversation not found: test-id"),  # First call fails
            {"id": "msg-1"},  # Second call succeeds (user message)
            {"id": "msg-2"},  # Third call succeeds (assistant message)
        ]

        # Mock conversation creation to return a new conversation
        mock_conversation_manager.create_conversation.return_value = {
            "id": "new-conversation-id"
        }

        # Mock get_conversation_context to return empty context for new conversation
        mock_conversation_manager.get_conversation_context.return_value = []

        # Mock get_user_conversations to return existing conversation first
        mock_conversation_manager.get_user_conversations.return_value = [
            {"id": "test-id"}
        ]

        with patch(
            "konveyor.apps.bot.views.conversation_manager", mock_conversation_manager
        ):
            # This should not crash and should recover gracefully
            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should return a successful result
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)

            # Should have created a new conversation
            mock_conversation_manager.create_conversation.assert_called_once_with(
                "U123456"
            )

            # Should have tried add_message at least twice (user message recovery + assistant message)
            self.assertGreaterEqual(mock_conversation_manager.add_message.call_count, 2)

    def test_conversation_recovery_on_get_context_failure(self):
        """Test recovery when get_conversation_context fails."""

        mock_conversation_manager = AsyncMock()

        # add_message succeeds but get_conversation_context fails
        mock_conversation_manager.add_message.return_value = {"id": "msg-1"}
        mock_conversation_manager.get_conversation_context.side_effect = ValueError(
            "Conversation not found: test-id"
        )

        # Mock conversation creation
        mock_conversation_manager.create_conversation.return_value = {
            "id": "new-conversation-id"
        }

        # Mock get_user_conversations
        mock_conversation_manager.get_user_conversations.return_value = [
            {"id": "test-id"}
        ]

        with patch(
            "konveyor.apps.bot.views.conversation_manager", mock_conversation_manager
        ):
            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should still return a result
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)

    def test_conversation_creation_failure_graceful_degradation(self):
        """Test graceful degradation when conversation creation completely fails."""

        mock_conversation_manager = AsyncMock()

        # All conversation operations fail
        mock_conversation_manager.get_user_conversations.side_effect = Exception(
            "Storage failure"
        )
        mock_conversation_manager.create_conversation.side_effect = Exception(
            "Storage failure"
        )

        with patch(
            "konveyor.apps.bot.views.conversation_manager", mock_conversation_manager
        ):
            # This should not crash
            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should still return a result
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)

            # Should not have a conversation_id in the result
            self.assertIsNone(result.get("conversation_id"))

    def test_assistant_message_storage_failure_handling(self):
        """Test handling of failures when storing assistant messages."""

        mock_conversation_manager = AsyncMock()

        # Initial conversation setup succeeds
        mock_conversation_manager.get_user_conversations.return_value = [
            {"id": "test-conversation-id"}
        ]
        mock_conversation_manager.add_message.side_effect = [
            {"id": "user-msg"},  # User message succeeds
            ValueError(
                "Conversation not found: test-conversation-id"
            ),  # Assistant message fails
        ]
        mock_conversation_manager.get_conversation_context.return_value = []

        with patch(
            "konveyor.apps.bot.views.conversation_manager", mock_conversation_manager
        ):
            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should still return a successful result
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)

            # Should have the conversation_id
            self.assertEqual(result.get("conversation_id"), "test-conversation-id")

    def test_error_message_storage_failure_handling(self):
        """Test handling of failures when storing error messages."""

        mock_conversation_manager = AsyncMock()

        # Initial conversation setup succeeds
        mock_conversation_manager.get_user_conversations.return_value = [
            {"id": "test-conversation-id"}
        ]
        mock_conversation_manager.add_message.side_effect = [
            {"id": "user-msg"},  # User message succeeds
            ValueError(
                "Conversation not found: test-conversation-id"
            ),  # Error message fails
        ]
        mock_conversation_manager.get_conversation_context.return_value = []

        # Mock the orchestrator to raise an exception
        with (
            patch(
                "konveyor.apps.bot.views.conversation_manager",
                mock_conversation_manager,
            ),
            patch("konveyor.apps.bot.views.orchestrator") as mock_orchestrator,
        ):
            mock_orchestrator.process_request_sync.side_effect = Exception(
                "Orchestrator failure"
            )

            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should return an error result
            self.assertIsInstance(result, dict)
            self.assertIn("error", result)
            self.assertFalse(result.get("success", True))

    def test_multiple_conversation_failures_in_sequence(self):
        """Test handling multiple conversation failures in sequence."""

        mock_conversation_manager = AsyncMock()

        # Simulate a sequence of failures and recoveries
        conversation_ids = ["conv-1", "conv-2", "conv-3"]

        # First conversation fails, second succeeds
        mock_conversation_manager.get_user_conversations.return_value = [
            {"id": conversation_ids[0]}
        ]

        mock_conversation_manager.add_message.side_effect = [
            ValueError(f"Conversation not found: {conversation_ids[0]}"),  # First fails
            {"id": "msg-1"},  # Recreation succeeds
        ]

        mock_conversation_manager.create_conversation.return_value = {
            "id": conversation_ids[1]
        }

        mock_conversation_manager.get_conversation_context.return_value = []

        with patch(
            "konveyor.apps.bot.views.conversation_manager", mock_conversation_manager
        ):
            result = process_message(
                text="Test message",
                user_id="U123456",
                channel_id="C123456",
                thread_ts=None,
            )

            # Should recover and return success
            self.assertIsInstance(result, dict)
            self.assertIn("response", result)

            # Should have the new conversation ID
            self.assertEqual(result.get("conversation_id"), conversation_ids[1])


if __name__ == "__main__":
    # Run the tests
    import django
    from django.conf import settings
    from django.test.utils import get_runner

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
    django.setup()

    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["tests.test_conversation_recovery"])

    if failures:
        sys.exit(1)

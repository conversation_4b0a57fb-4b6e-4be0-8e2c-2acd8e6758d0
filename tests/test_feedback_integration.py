"""
Integration tests for the feedback system.

This test suite tests the real feedback system with Django models
and conversation managers to identify issues seen in production.
"""

import asyncio
import os
import sys
import uuid

import django
from django.conf import settings
from django.test import TestCase

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
django.setup()

from konveyor.apps.bot.models import BotFeedback
from konveyor.core.conversation.factory import ConversationManagerFactory
from konveyor.core.conversation.feedback.factory import create_feedback_service


class FeedbackIntegrationTests(TestCase):
    """Integration tests for the feedback system."""

    def setUp(self):
        """Set up the test case."""
        # Create a real feedback service with Django repository
        self.feedback_service = create_feedback_service()

        # Create a conversation manager for testing
        self.conversation_manager = None

        # Initialize conversation manager
        asyncio.run(self._init_conversation_manager())

    async def _init_conversation_manager(self):
        """Initialize the conversation manager for testing."""
        try:
            self.conversation_manager = await ConversationManagerFactory.create_manager(
                "memory"
            )
        except Exception as e:
            print(f"Failed to create conversation manager: {e}")
            self.conversation_manager = None

    def test_feedback_with_conversation_id_flow(self):
        """Test the complete feedback flow with conversation IDs."""
        # Step 1: Create a conversation
        conversation_id = None
        if self.conversation_manager:

            async def create_test_conversation():
                conversation = await self.conversation_manager.create_conversation(
                    "test_user"
                )
                return conversation["id"]

            conversation_id = asyncio.run(create_test_conversation())

        # Step 2: Simulate a message being sent and content being updated
        message_id = "1234567890.123456"
        channel_id = "C123456"
        user_id = "U123456"
        question = "What's the weather today?"
        answer = "I'm sorry, I don't have access to weather information."
        skill_used = "ChatSkill"
        function_used = "process_general_query"

        # Update message content (simulating what happens when a message is sent)
        result = self.feedback_service.update_message_content(
            message_id=message_id,
            channel_id=channel_id,
            question=question,
            answer=answer,
            skill_used=skill_used,
            function_used=function_used,
            conversation_id=conversation_id,
        )

        # Should succeed even if conversation_id doesn't exist
        self.assertTrue(result, "update_message_content should succeed")

        # Step 3: Simulate a reaction event (positive feedback)
        reaction_event = {
            "type": "reaction_added",
            "reaction": "thumbsup",
            "user": user_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        # Process the reaction
        feedback_result = self.feedback_service.process_reaction_event(reaction_event)

        # Should create feedback successfully
        self.assertIsNotNone(feedback_result, "Feedback should be created")
        self.assertEqual(feedback_result["feedback_type"], "positive")
        self.assertEqual(feedback_result["message_id"], message_id)
        self.assertEqual(feedback_result["user_id"], user_id)

        # Step 4: Verify the feedback was stored in Django
        feedback_entries = BotFeedback.objects.filter(
            slack_message_ts=message_id, slack_user_id=user_id
        )
        self.assertTrue(
            feedback_entries.exists(), "Feedback should be stored in Django"
        )

        feedback_entry = feedback_entries.first()
        self.assertEqual(feedback_entry.question, question)
        self.assertEqual(feedback_entry.answer, answer)
        self.assertEqual(feedback_entry.skill_used, skill_used)
        self.assertEqual(feedback_entry.function_used, function_used)
        if conversation_id:
            self.assertEqual(feedback_entry.conversation_id, conversation_id)

    def test_feedback_with_nonexistent_conversation_id(self):
        """Test feedback handling when conversation ID doesn't exist."""
        # Use a non-existent conversation ID
        fake_conversation_id = str(uuid.uuid4())

        message_id = "9876543210.654321"
        channel_id = "C654321"
        user_id = "U654321"
        question = "How do I deploy this application?"
        answer = "You can deploy using Docker or Azure App Service."

        # Update message content with fake conversation ID
        result = self.feedback_service.update_message_content(
            message_id=message_id,
            channel_id=channel_id,
            question=question,
            answer=answer,
            conversation_id=fake_conversation_id,
        )

        # Should still succeed (graceful degradation)
        self.assertTrue(
            result,
            "update_message_content should succeed even with invalid conversation_id",
        )

        # Process a reaction
        reaction_event = {
            "type": "reaction_added",
            "reaction": "thumbsdown",
            "user": user_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        feedback_result = self.feedback_service.process_reaction_event(reaction_event)

        # Should still create feedback
        self.assertIsNotNone(
            feedback_result,
            "Feedback should be created even with invalid conversation_id",
        )
        self.assertEqual(feedback_result["feedback_type"], "negative")

    def test_multiple_reactions_same_message(self):
        """Test handling multiple reactions on the same message."""
        message_id = "1111111111.111111"
        channel_id = "C111111"
        user1_id = "U111111"
        user2_id = "U222222"

        # First user gives positive feedback
        reaction_event_1 = {
            "type": "reaction_added",
            "reaction": "thumbsup",
            "user": user1_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        feedback_1 = self.feedback_service.process_reaction_event(reaction_event_1)
        self.assertEqual(feedback_1["feedback_type"], "positive")

        # Second user gives negative feedback
        reaction_event_2 = {
            "type": "reaction_added",
            "reaction": "thumbsdown",
            "user": user2_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        feedback_2 = self.feedback_service.process_reaction_event(reaction_event_2)
        self.assertEqual(feedback_2["feedback_type"], "negative")

        # Verify both feedbacks are stored separately
        feedback_entries = BotFeedback.objects.filter(slack_message_ts=message_id)
        self.assertEqual(
            feedback_entries.count(), 2, "Should have two separate feedback entries"
        )

    def test_reaction_removal(self):
        """Test handling reaction removal."""
        message_id = "2222222222.222222"
        channel_id = "C222222"
        user_id = "U333333"

        # Add a reaction
        add_event = {
            "type": "reaction_added",
            "reaction": "heart",
            "user": user_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        feedback_add = self.feedback_service.process_reaction_event(add_event)
        self.assertEqual(feedback_add["feedback_type"], "positive")

        # Remove the reaction
        remove_event = {
            "type": "reaction_removed",
            "reaction": "heart",
            "user": user_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        feedback_remove = self.feedback_service.process_reaction_event(remove_event)
        self.assertEqual(feedback_remove["feedback_type"], "removed")

    def test_feedback_stats(self):
        """Test getting feedback statistics."""
        # Create some test feedback
        message_ids = ["3333333333.333333", "4444444444.444444", "5555555555.555555"]
        channel_id = "C333333"
        user_id = "U444444"

        reactions = ["thumbsup", "thumbsdown", "thumbsup"]

        for i, (message_id, reaction) in enumerate(
            zip(message_ids, reactions, strict=False)
        ):
            event = {
                "type": "reaction_added",
                "reaction": reaction,
                "user": f"{user_id}_{i}",  # Different users
                "item": {
                    "type": "message",
                    "channel": channel_id,
                    "ts": message_id,
                },
            }
            self.feedback_service.process_reaction_event(event)

        # Get stats
        stats = self.feedback_service.get_feedback_stats(days=30)

        self.assertGreaterEqual(stats["total_feedback"], 3)
        self.assertGreaterEqual(stats["positive_count"], 2)
        self.assertGreaterEqual(stats["negative_count"], 1)

    def test_error_handling_in_conversation_storage(self):
        """Test error handling when conversation storage fails."""
        # Mock the conversation manager to raise an exception
        original_manager = self.feedback_service.storage_provider.conversation_manager

        class FailingConversationManager:
            async def update_conversation_metadata(self, **kwargs):
                raise ValueError("Conversation not found: test-error-id")

        self.feedback_service.storage_provider.conversation_manager = (
            FailingConversationManager()
        )

        try:
            # This should not crash, but should log the error gracefully
            result = self.feedback_service.update_message_content(
                message_id="error_test_123",
                channel_id="C_ERROR",
                conversation_id="test-error-id",
                question="Test question",
                answer="Test answer",
            )

            # Should still succeed in Django storage even if conversation storage fails
            self.assertTrue(
                result, "Should succeed even when conversation storage fails"
            )

        finally:
            # Restore the original manager
            self.feedback_service.storage_provider.conversation_manager = (
                original_manager
            )


if __name__ == "__main__":
    # Run the tests
    import django
    from django.conf import settings
    from django.test.utils import get_runner

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.local")
    django.setup()

    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["tests.test_feedback_integration"])

    if failures:
        sys.exit(1)

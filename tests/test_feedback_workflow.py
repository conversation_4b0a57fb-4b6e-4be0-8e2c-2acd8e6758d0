#!/usr/bin/env python
"""
Comprehensive test script for Slack feedback mechanism.

This script simulates the complete feedback workflow by:
1. Sending a simulated bot message content update
2. Sending various reaction events (positive, negative, removed)
3. Monitoring logs and checking database state
4. Verifying API endpoints return expected data

Tests the workflow documented in docs/feedback-analysis-workflow.md
"""

import json
import time
from datetime import datetime

import requests

# Base URL for the Django development server
BASE_URL = "http://localhost:8000"
WEBHOOK_URL = f"{BASE_URL}/api/bot/slack/events/"

# Test data for different scenarios
TEST_MESSAGE_TS = "1234567890.123456"
TEST_CHANNEL_ID = "C1234567890"
TEST_USER_ID = "U1234567890"


def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'=' * 60}")
    print(f"  {title}")
    print(f"{'=' * 60}")


def send_webhook_event(event_data, description):
    """Send a webhook event and return the response."""
    print(f"\n📤 Sending {description}...")
    print(f"Event data: {json.dumps(event_data, indent=2)}")

    try:
        response = requests.post(
            WEBHOOK_URL,
            headers={"Content-Type": "application/json"},
            data=json.dumps(event_data),
            timeout=10,
        )
        print(f"✅ Response status: {response.status_code}")
        if response.text:
            print(f"Response body: {response.text}")
        return response
    except requests.exceptions.RequestException as e:
        print(f"❌ Error sending webhook: {e}")
        return None


def check_api_endpoint(endpoint, description):
    """Check an API endpoint and return the response."""
    print(f"\n🔍 Checking {description}...")
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        print(f"✅ Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Error response: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Error checking endpoint: {e}")
        return None


def test_message_content_association():
    """Test the message content association mechanism."""
    print_section("TEST 1: Message Content Association")

    # This would normally be called when the bot sends a message
    # We'll simulate this by creating a webhook event that triggers content storage

    # Create a simulated "app_mention" event that would trigger the bot to respond
    app_mention_event = {
        "token": "verification_token",
        "team_id": "T123456",
        "api_app_id": "A123456",
        "event": {
            "type": "app_mention",
            "user": TEST_USER_ID,
            "text": "<@U0123456789> How do I deploy to Azure?",
            "ts": TEST_MESSAGE_TS,
            "channel": TEST_CHANNEL_ID,
            "event_ts": str(int(time.time())),
        },
        "type": "event_callback",
        "event_id": f"Ev{int(time.time())}",
        "event_time": int(time.time()),
    }

    # Note: This won't actually trigger the content storage since we're not
    # going through the full bot response flow, but it shows the event structure
    send_webhook_event(app_mention_event, "app mention event (simulated user question)")


def test_positive_feedback():
    """Test positive feedback workflow."""
    print_section("TEST 2: Positive Feedback (👍)")

    reaction_event = {
        "token": "verification_token",
        "team_id": "T123456",
        "api_app_id": "A123456",
        "event": {
            "type": "reaction_added",
            "user": TEST_USER_ID,
            "reaction": "thumbsup",
            "item": {
                "type": "message",
                "channel": TEST_CHANNEL_ID,
                "ts": TEST_MESSAGE_TS,
            },
            "event_ts": str(int(time.time())),
        },
        "type": "event_callback",
        "event_id": f"Ev{int(time.time())}",
        "event_time": int(time.time()),
    }

    return send_webhook_event(reaction_event, "thumbs up reaction")


def test_negative_feedback():
    """Test negative feedback workflow."""
    print_section("TEST 3: Negative Feedback (👎)")

    reaction_event = {
        "token": "verification_token",
        "team_id": "T123456",
        "api_app_id": "A123456",
        "event": {
            "type": "reaction_added",
            "user": "U0987654321",  # Different user
            "reaction": "thumbsdown",
            "item": {
                "type": "message",
                "channel": TEST_CHANNEL_ID,
                "ts": TEST_MESSAGE_TS,
            },
            "event_ts": str(int(time.time())),
        },
        "type": "event_callback",
        "event_id": f"Ev{int(time.time())}",
        "event_time": int(time.time()),
    }

    return send_webhook_event(reaction_event, "thumbs down reaction")


def test_reaction_removal():
    """Test reaction removal workflow."""
    print_section("TEST 4: Reaction Removal")

    reaction_event = {
        "token": "verification_token",
        "team_id": "T123456",
        "api_app_id": "A123456",
        "event": {
            "type": "reaction_removed",
            "user": TEST_USER_ID,
            "reaction": "thumbsup",
            "item": {
                "type": "message",
                "channel": TEST_CHANNEL_ID,
                "ts": TEST_MESSAGE_TS,
            },
            "event_ts": str(int(time.time())),
        },
        "type": "event_callback",
        "event_id": f"Ev{int(time.time())}",
        "event_time": int(time.time()),
    }

    return send_webhook_event(reaction_event, "reaction removal")


def test_multiple_reactions():
    """Test multiple different reactions on the same message."""
    print_section("TEST 5: Multiple Reactions on Same Message")

    reactions = ["heart", "clap", "raised_hands"]

    for reaction in reactions:
        reaction_event = {
            "token": "verification_token",
            "team_id": "T123456",
            "api_app_id": "A123456",
            "event": {
                "type": "reaction_added",
                "user": f"U{reaction[:8].ljust(8, '0')}",  # Different user for each
                "reaction": reaction,
                "item": {
                    "type": "message",
                    "channel": TEST_CHANNEL_ID,
                    "ts": TEST_MESSAGE_TS,
                },
                "event_ts": str(int(time.time())),
            },
            "type": "event_callback",
            "event_id": f"Ev{int(time.time())}",
            "event_time": int(time.time()),
        }

        send_webhook_event(reaction_event, f"{reaction} reaction")
        time.sleep(0.5)  # Small delay between events


def test_non_feedback_reactions():
    """Test reactions that shouldn't generate feedback."""
    print_section("TEST 6: Non-Feedback Reactions")

    non_feedback_reactions = ["eyes", "thinking_face", "question"]

    for reaction in non_feedback_reactions:
        reaction_event = {
            "token": "verification_token",
            "team_id": "T123456",
            "api_app_id": "A123456",
            "event": {
                "type": "reaction_added",
                "user": f"U{reaction[:8].ljust(8, '0')}",
                "reaction": reaction,
                "item": {
                    "type": "message",
                    "channel": TEST_CHANNEL_ID,
                    "ts": TEST_MESSAGE_TS,
                },
                "event_ts": str(int(time.time())),
            },
            "type": "event_callback",
            "event_id": f"Ev{int(time.time())}",
            "event_time": int(time.time()),
        }

        send_webhook_event(reaction_event, f"{reaction} reaction (should be ignored)")
        time.sleep(0.5)


def test_api_endpoints():
    """Test all feedback API endpoints."""
    print_section("TEST 7: API Endpoints Verification")

    # Test feedback stats
    stats_data = check_api_endpoint("/api/bot/feedback/stats/", "feedback statistics")

    # Test feedback by skill
    skill_data = check_api_endpoint("/api/bot/feedback/by-skill/", "feedback by skill")

    # Test feedback export (JSON)
    export_data = check_api_endpoint(
        "/api/bot/feedback/export/?format=json", "feedback export (JSON)"
    )

    # Test feedback export (CSV)
    csv_response = check_api_endpoint(
        "/api/bot/feedback/export/?format=csv", "feedback export (CSV)"
    )

    return stats_data, skill_data, export_data, csv_response


def test_url_verification():
    """Test Slack URL verification challenge."""
    print_section("TEST 8: URL Verification Challenge")

    challenge_event = {
        "token": "verification_token",
        "challenge": "test_challenge_string_12345",
        "type": "url_verification",
    }

    response = send_webhook_event(challenge_event, "URL verification challenge")
    if response and response.status_code == 200:
        try:
            response_data = response.json()
            if response_data.get("challenge") == "test_challenge_string_12345":
                print("✅ URL verification working correctly!")
            else:
                print(f"❌ URL verification failed: {response_data}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON response: {response.text}")


def main():
    """Run all tests."""
    print_section("STARTING FEEDBACK MECHANISM TESTS")
    print(f"Testing Django server at: {BASE_URL}")
    print(f"Webhook endpoint: {WEBHOOK_URL}")
    print(f"Test message timestamp: {TEST_MESSAGE_TS}")
    print(f"Test channel ID: {TEST_CHANNEL_ID}")
    print(f"Started at: {datetime.now().isoformat()}")

    # Wait a moment for the server to be ready
    print("\n⏳ Waiting for Django server to be ready...")
    time.sleep(3)

    try:
        # Test URL verification first
        test_url_verification()

        # Test message content association
        test_message_content_association()
        time.sleep(2)

        # Test positive feedback
        test_positive_feedback()
        time.sleep(2)

        # Test negative feedback
        test_negative_feedback()
        time.sleep(2)

        # Test reaction removal
        test_reaction_removal()
        time.sleep(2)

        # Test multiple reactions
        test_multiple_reactions()
        time.sleep(2)

        # Test non-feedback reactions
        test_non_feedback_reactions()
        time.sleep(2)

        # Check API endpoints
        stats_data, skill_data, export_data, csv_response = test_api_endpoints()

        # Summary
        print_section("TEST SUMMARY")
        if stats_data:
            print(f"📊 Total feedback recorded: {stats_data.get('total_feedback', 0)}")
            print(f"👍 Positive feedback: {stats_data.get('positive_count', 0)}")
            print(f"👎 Negative feedback: {stats_data.get('negative_count', 0)}")
            print(
                f"📈 Positive percentage: {stats_data.get('positive_percentage', 0):.1f}%"
            )

        print("\n✅ All tests completed!")
        print(
            "📝 Check the Django server logs to verify the workflow follows the documented pattern."
        )

    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()

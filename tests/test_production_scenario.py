"""
Test that reproduces the production scenario issues.

This test reproduces the exact errors seen in production:
1. Conversation not found errors
2. Skill routing issues
3. Feedback system handling of non-existent conversations
"""

import asyncio
import os
import sys
import uuid

import django
from django.test import TestCase

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
django.setup()

from konveyor.apps.bot.models import BotFeedback
from konveyor.apps.bot.views import process_message
from konveyor.core.conversation.factory import ConversationManagerFactory
from konveyor.core.conversation.feedback.factory import create_feedback_service


class ProductionScenarioTests(TestCase):
    """Tests that reproduce production scenario issues."""

    def setUp(self):
        """Set up the test case."""
        self.feedback_service = create_feedback_service()

    def test_conversation_not_found_error_scenario(self):
        """Test the exact scenario causing 'Conversation not found' errors."""

        # This is the UUID from the error logs
        problematic_conversation_id = "967ff811-0763-4778-8363-666a31485513"

        message_id = "1234567890.123456"
        channel_id = "C123456"
        user_id = "U123456"
        question = "What's the weather today?"
        answer = "I'm sorry, I don't have access to weather information."

        # Try to update message content with a non-existent conversation ID
        # This should NOT crash the system
        result = self.feedback_service.update_message_content(
            message_id=message_id,
            channel_id=channel_id,
            question=question,
            answer=answer,
            conversation_id=problematic_conversation_id,
        )

        # Should succeed gracefully even with invalid conversation ID
        self.assertTrue(result, "Should handle non-existent conversation ID gracefully")

        # Now process a reaction - this should also work
        reaction_event = {
            "type": "reaction_added",
            "reaction": "thumbsup",
            "user": user_id,
            "item": {
                "type": "message",
                "channel": channel_id,
                "ts": message_id,
            },
        }

        # This should work without crashing
        feedback_result = self.feedback_service.process_reaction_event(reaction_event)
        self.assertIsNotNone(feedback_result)

        # Verify feedback was stored in Django despite conversation storage failure
        feedback_entries = BotFeedback.objects.filter(slack_message_ts=message_id)
        self.assertTrue(feedback_entries.exists())

        feedback_entry = feedback_entries.first()
        self.assertEqual(feedback_entry.question, question)
        self.assertEqual(feedback_entry.answer, answer)
        self.assertEqual(feedback_entry.conversation_id, problematic_conversation_id)

    def test_skill_routing_failure_scenario(self):
        """Test the scenario where no skill is found for a request."""

        # Test with the exact message from the error logs
        test_message = "What's the weather today?"
        user_id = "U123456"
        channel_id = "C123456"
        thread_ts = None

        # This should not crash even if no skill is found
        result = process_message(test_message, user_id, channel_id, thread_ts)

        # Should return some kind of response even if no skill matches
        self.assertIsInstance(result, dict)
        self.assertIn("response", result)

        # The response might be an error message, but it should exist
        response_text = result.get("response", "")
        self.assertTrue(len(response_text) > 0, "Should return some response text")

        # Should not crash the system
        self.assertIn("success", result)

    def test_multiple_conversation_not_found_scenarios(self):
        """Test multiple rapid conversation not found scenarios like in production."""

        # Simulate multiple rapid requests with non-existent conversation IDs
        conversation_ids = [
            "967ff811-0763-4778-8363-666a31485513",
            str(uuid.uuid4()),
            str(uuid.uuid4()),
            str(uuid.uuid4()),
        ]

        message_base = "test_message_"

        for i, conversation_id in enumerate(conversation_ids):
            message_id = f"{message_base}{i}"

            # Each should succeed independently
            result = self.feedback_service.update_message_content(
                message_id=message_id,
                channel_id="C123456",
                question=f"Question {i}",
                answer=f"Answer {i}",
                conversation_id=conversation_id,
            )

            self.assertTrue(result, f"Request {i} should succeed")

            # Each should be able to process feedback
            reaction_event = {
                "type": "reaction_added",
                "reaction": "thumbsup",
                "user": f"U{i}",
                "item": {
                    "type": "message",
                    "channel": "C123456",
                    "ts": message_id,
                },
            }

            feedback_result = self.feedback_service.process_reaction_event(
                reaction_event
            )
            self.assertIsNotNone(feedback_result, f"Feedback {i} should succeed")

    def test_conversation_manager_failure_scenario(self):
        """Test scenario where conversation manager itself fails."""

        # Mock the conversation manager to always fail
        original_manager = self.feedback_service.storage_provider.conversation_manager

        class FailingConversationManager:
            def __init__(self):
                self.conversations = {}

            async def update_conversation_metadata(self, **kwargs):
                raise Exception("Conversation manager is down")

        failing_manager = FailingConversationManager()
        self.feedback_service.storage_provider.conversation_manager = failing_manager

        try:
            # This should not crash the entire feedback system
            result = self.feedback_service.update_message_content(
                message_id="test_failure",
                channel_id="C_FAIL",
                conversation_id="test-fail-id",
                question="Test question",
                answer="Test answer",
            )

            # Should still succeed in Django storage
            self.assertTrue(
                result, "Should succeed even when conversation manager fails"
            )

            # Process feedback - should also work
            reaction_event = {
                "type": "reaction_added",
                "reaction": "thumbsdown",
                "user": "U_FAIL",
                "item": {
                    "type": "message",
                    "channel": "C_FAIL",
                    "ts": "test_failure",
                },
            }

            feedback_result = self.feedback_service.process_reaction_event(
                reaction_event
            )
            self.assertIsNotNone(
                feedback_result,
                "Feedback should work despite conversation manager failure",
            )

        finally:
            # Restore original manager
            self.feedback_service.storage_provider.conversation_manager = (
                original_manager
            )

    def test_azure_environment_simulation(self):
        """Test simulating Azure environment conditions."""

        # In Azure, conversation IDs might be generated but conversations
        # not properly persisted due to storage issues

        # First, try to create a conversation manager like in production
        async def simulate_azure_conversation_creation():
            try:
                manager = await ConversationManagerFactory.create_manager("memory")
                # Simulate conversation creation
                conversation = await manager.create_conversation("test_user")
                return conversation["id"]
            except Exception:
                # Simulate Azure storage failures
                return str(uuid.uuid4())  # Return a fake ID

        # Get a conversation ID (might be fake due to Azure issues)
        azure_conversation_id = asyncio.run(simulate_azure_conversation_creation())

        # Now try to use this ID for feedback - this is where the errors occur
        result = self.feedback_service.update_message_content(
            message_id="azure_test_message",
            channel_id="C_AZURE",
            question="Azure test question",
            answer="Azure test answer",
            conversation_id=azure_conversation_id,
        )

        # Should handle this gracefully
        self.assertTrue(result, "Should handle Azure environment issues gracefully")

    def test_rapid_feedback_processing(self):
        """Test rapid feedback processing like in production Slack environment."""

        # Simulate rapid feedback processing like what happens in production
        base_message_id = "rapid_test_"

        for i in range(10):
            message_id = f"{base_message_id}{i}"
            conversation_id = str(uuid.uuid4())  # Each gets a different conversation ID

            # Rapid update + reaction processing
            self.feedback_service.update_message_content(
                message_id=message_id,
                channel_id="C_RAPID",
                question=f"Rapid question {i}",
                answer=f"Rapid answer {i}",
                conversation_id=conversation_id,
            )

            reaction_event = {
                "type": "reaction_added",
                "reaction": "thumbsup" if i % 2 == 0 else "thumbsdown",
                "user": f"U_RAPID_{i}",
                "item": {
                    "type": "message",
                    "channel": "C_RAPID",
                    "ts": message_id,
                },
            }

            # Should all process without errors
            feedback_result = self.feedback_service.process_reaction_event(
                reaction_event
            )
            self.assertIsNotNone(feedback_result, f"Rapid feedback {i} should succeed")

        # Verify all feedback was stored
        rapid_feedback = BotFeedback.objects.filter(slack_channel_id="C_RAPID")
        self.assertEqual(
            rapid_feedback.count(), 10, "All rapid feedback should be stored"
        )


if __name__ == "__main__":
    # Run the tests
    import django
    from django.conf import settings
    from django.test.utils import get_runner

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "konveyor.settings.test")
    django.setup()

    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["tests.test_production_scenario"])

    if failures:
        sys.exit(1)
